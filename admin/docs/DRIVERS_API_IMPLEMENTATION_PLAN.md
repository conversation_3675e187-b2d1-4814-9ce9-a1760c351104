# Drivers Page API Integration Implementation Plan

## Overview
This document outlines the complete implementation plan for integrating the drivers page with the new API endpoints and implementing the phone number + OTP verification flow within a sheet-based UI.

## Current State Analysis

### Existing Components
- **Driver Page**: `admin/src/modules/driver/pages/driver-page.tsx` - Main page with filters and table
- **Create Driver**: `admin/src/modules/driver/components/create-driver.tsx` - Current form in sheet
- **Driver Table**: `admin/src/modules/driver/components/driver-table.tsx` - Table with pagination
- **Driver Filters**: `admin/src/modules/driver/components/driver-filters.tsx` - Search and filter controls
- **API Hooks**: `admin/src/modules/driver/api/queries.ts` and `mutations.ts` - Currently using mock data

### Current API Structure
- Cities API: `GET /api/v1/cities` - Already implemented and working
- Driver APIs: Currently mocked, need to be replaced with real endpoints

## Implementation Flow

### Step 1: Phone Number Registration
**API Endpoint**: `POST /api/v1/drivers/register`
```json
{
  "phoneNumber": "+1234567890"
}
```

**UI Changes**:
- Replace current create driver form with phone number input
- Add phone number validation (international format)
- Show loading state during registration
- Handle success/error responses

### Step 2: OTP Verification
**API Endpoint**: `POST /api/v1/drivers/verify-otp`
```json
{
  "phoneNumber": "+************",
  "otp": "1234"
}
```

**UI Changes**:
- Show OTP input screen after successful phone registration
- Use existing OTP component from `verify-otp-page.tsx` as reference
- Implement 30-second countdown timer
- Store phone number in URL params to prevent loss on refresh

### Step 3: OTP Resend
**API Endpoint**: `POST /api/v1/drivers/resend-otp`
```json
{
  "phoneNumber": "+************"
}
```

**UI Changes**:
- Add resend button with timer
- Reset OTP input on resend
- Show success/error messages

### Step 4: Auto-Create Driver Profile
**API Endpoint**: `POST /api/v1/drivers`
```json
{
  "userId": "uuid-string",
  "firstName": "John",
  "lastName": "Doe", 
  "mobileNumber": "+************",
  "email": "<EMAIL>",
  "gender": "MALE",
  "dob": "1990-01-15",
  "profilePictureUrl": "https://example.com/profile.jpg",
  "cityId": "123e4567-e89b-12d3-a456-************"
}
```

**Implementation**:
- Call this API immediately after successful OTP verification
- Only pass `mobileNumber` initially
- Show the existing create-driver form with all fields after profile creation
- Pre-populate phone number field (disabled)

### Step 5: Update Driver Profile
**API Endpoint**: `PATCH /api/v1/drivers/{profileId}`
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "gender": "MALE", 
  "dob": "1990-01-15",
  "profilePictureUrl": "https://example.com/profile.jpg",
  "cityId": "123e4567-e89b-12d3-a456-************"
}
```

**Implementation**:
- Use this endpoint for both completing initial profile and editing existing drivers
- Update form submission logic in create-driver component
- Handle success/error states

### Step 6: List Drivers with Filters
**API Endpoint**: `GET /api/v1/drivers`
**Parameters**:
```json
{
  "page": 1,
  "limit": 10,
  "sortBy": "sortBy",
  "sortOrder": "asc", 
  "search": "search",
  "cityId": "123e4567-e89b-12d3-a456-************",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phoneNumber": "+************"
}
```

**Implementation**:
- Update `useListDriver` hook to use real API
- Map existing filter parameters to new API structure
- Ensure pagination works with new response format
- Update driver table to handle new data structure

### Step 7: Get Single Driver
**API Endpoint**: `GET /api/v1/drivers/{id}`

**Implementation**:
- Update `useGetDriver` hook for editing functionality
- Use for pre-populating edit forms

## Technical Implementation Details

### 1. API Hooks Updates

#### New Hooks to Create:
```typescript
// Registration and OTP hooks
export const useRegisterDriver = () => useMutation({...})
export const useVerifyDriverOtp = () => useMutation({...})
export const useResendDriverOtp = () => useMutation({...})

// Cities hook (if not exists)
export const useCities = () => useQuery({...})
```

#### Hooks to Update:
```typescript
// Update existing hooks to use real APIs
export const useListDriver = ({...}) => useQuery({...})
export const useGetDriver = (id) => useQuery({...})
export const useCreateDriver = () => useMutation({...}) // Change to PATCH
export const useUpdateDriver = () => useMutation({...})
```

### 2. Type Definitions Updates

#### New Types:
```typescript
interface DriverRegistrationRequest {
  phoneNumber: string;
}

interface DriverOtpVerificationRequest {
  phoneNumber: string;
  otp: string;
}

interface City {
  id: string;
  name: string;
  countryId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
```

#### Updated Types:
```typescript
interface Driver {
  // Update to match new API response structure
  id: string; // Change from number to string
  userId?: string;
  firstName: string;
  lastName: string;
  mobileNumber: string; // Change from phoneNumber
  email: string;
  gender: "MALE" | "FEMALE" | "OTHER";
  dob: string; // Change from dateOfBirth
  profilePictureUrl?: string;
  cityId: string;
  // Remove old fields, add new ones as needed
}
```

### 3. Component Updates

#### Create Driver Component:
- Add phone number registration step
- Add OTP verification step  
- Modify existing form to be step 3 (profile completion)
- Add state management for multi-step flow
- Store phone number in URL params

#### Driver Filters Component:
- Add city filter dropdown using cities API
- Update filter parameters to match new API

#### Driver Table Component:
- Update column definitions for new data structure
- Ensure pagination works with new response format

### 4. URL State Management
- Store phone number in URL params during registration/OTP flow
- Prevent data loss on page refresh
- Clear params after successful completion

### 5. Error Handling
- Add proper error handling for each API call
- Show user-friendly error messages
- Handle network errors and timeouts
- Add retry mechanisms where appropriate

### 6. Loading States
- Add loading indicators for each step
- Disable form inputs during API calls
- Show progress indicators for multi-step flow

### 7. Cache Invalidation
- Invalidate driver list cache after successful creation/update
- Refresh counts and statistics after changes

## File Structure Changes

### New Files to Create:
- `admin/src/modules/driver/components/driver-registration.tsx` - Phone registration step
- `admin/src/modules/driver/components/driver-otp-verification.tsx` - OTP verification step
- `admin/src/modules/driver/hooks/use-driver-registration.ts` - Registration flow state management

### Files to Modify:
- `admin/src/modules/driver/components/create-driver.tsx` - Convert to multi-step flow
- `admin/src/modules/driver/api/queries.ts` - Add new queries, update existing
- `admin/src/modules/driver/api/mutations.ts` - Add new mutations, update existing  
- `admin/src/modules/driver/types/driver.ts` - Update type definitions
- `admin/src/modules/driver/components/driver-filters.tsx` - Add city filter
- `admin/src/modules/driver/components/driver-table.tsx` - Update for new data structure

## Testing Strategy
1. Test phone number validation and registration
2. Test OTP verification and resend functionality
3. Test profile creation and completion flow
4. Test driver listing with all filters
5. Test driver editing functionality
6. Test error scenarios and edge cases
7. Test URL state persistence across refreshes

## Success Criteria
- [ ] Phone number registration works correctly
- [ ] OTP verification and resend functionality works
- [ ] Driver profile auto-creation after OTP verification
- [ ] Complete driver profile form works
- [ ] Driver listing with pagination and filters works
- [ ] Driver editing functionality works
- [ ] All error states are handled gracefully
- [ ] URL state management prevents data loss
- [ ] Cache invalidation works correctly
- [ ] UI follows existing design patterns and branding

import { z } from 'zod';
import { passwordSchema } from './password-schema';

// Forgot Password Schema
export const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

// OTP Verification Schema
export const verifyOtpSchema = z.object({
   otp: z
      .string()
      .length(4, 'OTP must be exactly 4 digits')
      .regex(/^\d{4}$/, 'OTP must contain only numbers'),
});

// Reset Password Schema
export const resetPasswordSchema = z
  .object({
    newPassword: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// Type exports for form data
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type VerifyOtpFormData = z.infer<typeof verifyOtpSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

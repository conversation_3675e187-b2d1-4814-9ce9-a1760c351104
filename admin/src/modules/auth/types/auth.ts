// Forgot Password Types
export type ForgotPasswordPayload = {
  email: string;
};

export type ForgotPasswordResponse = {
  message: string;
  success: boolean;
};

// Verify OTP Types
export type VerifyForgotPasswordOtpPayload = {
  email: string;
  otp: string;
};

export type VerifyForgotPasswordOtpResponse = {
  message: string;
  success: boolean;
  isValid: boolean;
};

// Reset Password Types
export type ResetPasswordPayload = {
  email: string;
  otp: string;
  newPassword: string;
  confirmPassword: string;
};

export type ResetPasswordResponse = {
  message: string;
  success: boolean;
};

// Common Auth Error Response
export type AuthErrorResponse = {
  message: string;
  error: string;
  statusCode: number;
};

// Form Data Types for React Hook Form
export type ForgotPasswordFormData = {
  email: string;
};

export type VerifyOtpFormData = {
  otp: string;
};

export type ResetPasswordFormData = {
  newPassword: string;
  confirmPassword: string;
};

// URL Search Params Types
export type OtpVerificationParams = {
  email: string;
};

export type ResetPasswordParams = {
  email: string;
  otp: string;
};

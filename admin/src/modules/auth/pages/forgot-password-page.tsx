'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { useForgotPassword } from '../api/mutations';
import { AuthHeader } from '../components/auth-header';
import { forgotPasswordSchema, type ForgotPasswordFormData } from '../schema/auth-schemas';

export default function ForgotPasswordPage() {
   const router = useRouter();

   const {
      register,
      handleSubmit,
      formState: { errors },
   } = useForm<ForgotPasswordFormData>({
      resolver: zodResolver(forgotPasswordSchema),
      defaultValues: {
         email: '',
      },
   });

   const forgotPasswordMutation = useForgotPassword();

   const onSubmit = async (data: ForgotPasswordFormData) => {
      forgotPasswordMutation.mutate(data, {
         onSuccess: () => {
            toast.success('OTP sent to your email successfully');
            // Navigate to OTP verification with email in URL params
            router.push(`/auth/verify-otp?email=${encodeURIComponent(data.email)}`);
         },
      });
   };

   return (
      <div className='bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10'>
         <div className='flex w-full max-w-sm flex-col gap-6'>
            <AuthHeader />

            <Card>
               <CardHeader className='text-center'>
                  <CardTitle className='text-xl'>Forgot Password?</CardTitle>
                  <p className='text-sm text-muted-foreground'>
                     Enter your email address and we'll send you an OTP to reset your password
                  </p>
               </CardHeader>
               <CardContent>
                  <form onSubmit={handleSubmit(onSubmit)}>
                     <div className='grid gap-6'>
                        <div className='grid gap-3'>
                           <Label htmlFor='email'>Email</Label>
                           <Input
                              id='email'
                              type='email'
                              placeholder='Enter your email'
                              autoComplete='email'
                              {...register('email')}
                           />
                           <ErrorMessage error={errors.email} />
                        </div>

                        <Button
                           type='submit'
                           className='w-full'
                           disabled={forgotPasswordMutation.isPending}
                        >
                           {forgotPasswordMutation.isPending ? (
                              <>
                                 Sending OTP...
                                 <Spinner className='ml-2' />
                              </>
                           ) : (
                              'Send OTP'
                           )}
                        </Button>
                     </div>
                  </form>
               </CardContent>
            </Card>

            <div className='text-center'>
               <Link
                  href='/'
                  className='flex items-center justify-center gap-2 text-sm text-primary hover:underline'
               >
                  <ArrowLeft className='h-4 w-4' />
                  Back to Login
               </Link>
            </div>
         </div>
      </div>
   );
}

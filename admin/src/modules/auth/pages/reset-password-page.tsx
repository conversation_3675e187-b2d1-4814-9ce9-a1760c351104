'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, CheckCircle, EyeIcon, EyeOffIcon } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useResetPassword } from '../api/mutations';
import { AuthHeader } from '../components/auth-header';
import { resetPasswordSchema, type ResetPasswordFormData } from '../schema/auth-schemas';

export default function ResetPasswordPage() {
   const [isSuccess, setIsSuccess] = useState(false);
   const [showPassword, setShowPassword] = useState(false);
   const [showConfirmPassword, setShowConfirmPassword] = useState(false);
   const router = useRouter();
   const searchParams = useSearchParams();
   const email = searchParams.get('email');
   const otp = searchParams.get('otp');

   const {
      register,
      handleSubmit,
      formState: { errors },
   } = useForm<ResetPasswordFormData>({
      resolver: zodResolver(resetPasswordSchema),
      defaultValues: {
         newPassword: '',
         confirmPassword: '',
      },
   });

   const resetPasswordMutation = useResetPassword();

   // Redirect if no email or OTP in params
   useEffect(() => {
      if (!email || !otp) {
         toast.error('Invalid reset link. Please start from forgot password.');
         router.push('/auth/forgot-password');
      }
   }, [email, otp, router]);

   const onSubmit = async (data: ResetPasswordFormData) => {
      if (!email || !otp) return;

      const payload = {
         email,
         otp,
         newPassword: data.newPassword,
         confirmPassword: data.confirmPassword,
      };

      resetPasswordMutation.mutate(payload, {
         onSuccess: () => {
            setIsSuccess(true);
            toast.success('Password reset successfully');
         },
      });
   };

   const handleBackToLogin = () => {
      router.push('/');
   };

   if (!email || !otp) {
      return null; // Will redirect in useEffect
   }

   return (
      <div className='bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10'>
         <div className='flex w-full max-w-sm flex-col gap-6'>
            <AuthHeader />

            <Card>
               {isSuccess ? (
                  <CardContent className='flex flex-col items-center justify-center space-y-4 pt-6'>
                     <CheckCircle className='h-16 w-16 text-green-500' />
                     <CardTitle className='text-xl font-semibold text-center'>
                        Password Reset Successful
                     </CardTitle>
                     <p className='text-center text-muted-foreground text-sm'>
                        Your password has been successfully reset. You can now log in with your new
                        password.
                     </p>
                     <Button className='w-full mt-4' onClick={handleBackToLogin}>
                        <ArrowLeft className='mr-2 h-4 w-4' />
                        Back to Login
                     </Button>
                  </CardContent>
               ) : (
                  <>
                     <CardHeader className='text-center'>
                        <CardTitle className='text-xl'>Reset Password</CardTitle>
                        <p className='text-sm text-muted-foreground'>
                           Enter your new password below
                        </p>
                     </CardHeader>
                     <CardContent>
                        <form onSubmit={handleSubmit(onSubmit)}>
                           <div className='grid gap-6'>
                              <div className='grid gap-3'>
                                 <Label htmlFor='newPassword'>New Password</Label>
                                 <div className='relative'>
                                    <Input
                                       id='newPassword'
                                       type={showPassword ? 'text' : 'password'}
                                       placeholder='Enter new password'
                                       autoComplete='new-password'
                                       {...register('newPassword')}
                                    />
                                    <Button
                                       type='button'
                                       variant='ghost'
                                       size='sm'
                                       className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                                       onClick={() => setShowPassword(!showPassword)}
                                    >
                                       {showPassword ? (
                                          <EyeOffIcon className='h-4 w-4 text-muted-foreground' />
                                       ) : (
                                          <EyeIcon className='h-4 w-4 text-muted-foreground' />
                                       )}
                                    </Button>
                                 </div>
                                 <ErrorMessage error={errors.newPassword} />
                              </div>

                              <div className='grid gap-3'>
                                 <Label htmlFor='confirmPassword'>Confirm New Password</Label>
                                 <div className='relative'>
                                    <Input
                                       id='confirmPassword'
                                       type={showConfirmPassword ? 'text' : 'password'}
                                       placeholder='Confirm new password'
                                       autoComplete='new-password'
                                       {...register('confirmPassword')}
                                    />
                                    <Button
                                       type='button'
                                       variant='ghost'
                                       size='sm'
                                       className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                                       onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                    >
                                       {showConfirmPassword ? (
                                          <EyeOffIcon className='h-4 w-4 text-muted-foreground' />
                                       ) : (
                                          <EyeIcon className='h-4 w-4 text-muted-foreground' />
                                       )}
                                    </Button>
                                 </div>
                                 <ErrorMessage error={errors.confirmPassword} />
                              </div>

                              <Button
                                 type='submit'
                                 className='w-full'
                                 disabled={resetPasswordMutation.isPending}
                              >
                                 {resetPasswordMutation.isPending ? (
                                    <>
                                       Resetting Password...
                                       <Spinner className='ml-2' />
                                    </>
                                 ) : (
                                    'Reset Password'
                                 )}
                              </Button>
                           </div>
                        </form>
                     </CardContent>
                  </>
               )}
            </Card>

            {!isSuccess && (
               <div className='text-center'>
                  <Link
                     href={`/auth/verify-otp?email=${encodeURIComponent(email)}`}
                     className='flex items-center justify-center gap-2 text-sm text-primary hover:underline'
                  >
                     <ArrowLeft className='h-4 w-4' />
                     Back to OTP Verification
                  </Link>
               </div>
            )}
         </div>
      </div>
   );
}

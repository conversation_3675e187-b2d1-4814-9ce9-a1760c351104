'use client';

import { Card } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { AlertCircle, ArrowLeft, Mail, MapPin, Phone, User } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { useGetDriver } from '../api/queries';
import { DriverPersonalDetails } from '../components/driver-personal-details';
import { DriverKycDocuments } from '../components/driver-kyc-documents';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useParams } from 'next/navigation';

export function DriverDetailsPage() {
   const params = useParams<{ id: string }>();
   const driverId = params.id;
   const [activeTab, setActiveTab] = useState('personal');
   const { data: driver, isLoading, error } = useGetDriver(driverId);

   if (isLoading) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-4'>
            <div className='animate-pulse'>
               <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
               <div className='h-32 bg-gray-200 rounded mb-4'></div>
               <div className='h-96 bg-gray-200 rounded'></div>
            </div>
         </div>
      );
   }

   if (error || !driver?.data) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-4'>
            <div className='flex items-center gap-4 mb-6'>
               <Link href='/dashboard/drivers'>
                  <Button variant='ghost' size='sm' className='gap-2'>
                     <ArrowLeft className='h-4 w-4' />
                     Back to Drivers
                  </Button>
               </Link>
            </div>
            <Card className='p-8 text-center'>
               <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
               <h3 className='text-lg font-medium text-gray-900 mb-2'>Driver Not Found</h3>
               <p className='text-gray-600'>
                  The driver you're looking for doesn't exist or has been removed.
               </p>
            </Card>
         </div>
      );
   }

   const driverData = driver.data;
   const fullName = `${driverData.firstName || ''} ${driverData.lastName || ''}`.trim() || 'N/A';

   return (
      <div className='flex flex-1 flex-col gap-4 p-4'>
         {/* Header with back button */}
         <div className='flex items-center gap-4 mb-6'>
            <Link href='/dashboard/drivers'>
               <Button variant='ghost' size='sm' className='gap-2'>
                  <ArrowLeft className='h-4 w-4' />
                  Back to Drivers
               </Button>
            </Link>
            <div className='h-6 w-px bg-gray-300' />
            <div className='flex items-center gap-2 text-sm text-gray-600'>
               <span>Drivers</span>
               <span>/</span>
               <span>Driver Details</span>
               <span>/</span>
               <span className='text-gray-900 font-medium'>{fullName}</span>
            </div>
         </div>

         {/* Driver Header Card */}
         <Card className='p-6 mb-6'>
            <div className='flex items-start gap-6'>
               {/* Profile Picture */}
               <div className='flex-shrink-0'>
                  {driverData.profilePictureUrl ? (
                     <img
                        src={driverData.profilePictureUrl}
                        alt={fullName}
                        className='w-20 h-20 rounded-full object-cover border-2 border-gray-200'
                     />
                  ) : (
                     <div className='w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center border-2 border-gray-200'>
                        <User className='w-8 h-8 text-gray-400' />
                     </div>
                  )}
               </div>

               {/* Driver Info */}
               <div className='flex-1'>
                  <div className='flex items-center gap-3 mb-2'>
                     <h1 className='text-2xl font-semibold text-gray-900'>{fullName}</h1>
                     <Badge variant={driverData.phoneVerified ? 'default' : 'secondary'}>
                        {driverData.phoneVerified ? 'Verified' : 'Unverified'}
                     </Badge>
                  </div>

                  <div className='grid grid-cols-1 md:grid-cols-3 gap-4 text-sm'>
                     <div className='flex items-center gap-2 text-gray-600'>
                        <Mail className='w-4 h-4' />
                        <span>{driverData.email || 'No email provided'}</span>
                     </div>
                     <div className='flex items-center gap-2 text-gray-600'>
                        <Phone className='w-4 h-4' />
                        <span>{driverData.phoneNumber}</span>
                     </div>
                     <div className='flex items-center gap-2 text-gray-600'>
                        <MapPin className='w-4 h-4' />
                        <span>{driverData.cityName || 'No city specified'}</span>
                     </div>
                  </div>
               </div>
            </div>
         </Card>

         {/* Tabs */}
         <Card className='flex-1'>
            <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
               <div className='border-b border-gray-200 px-6 pt-6'>
                  <TabsList className='grid w-full grid-cols-2 max-w-md'>
                     <TabsTrigger value='personal'>Personal Details</TabsTrigger>
                     <TabsTrigger value='kyc'>KYC</TabsTrigger>
                  </TabsList>
               </div>

               <div className='p-6'>
                  <TabsContent value='personal' className='mt-0'>
                     <DriverPersonalDetails driver={driverData} />
                  </TabsContent>

                  <TabsContent value='kyc' className='mt-0'>
                     <DriverKycDocuments driverId={driverId} />
                  </TabsContent>
               </div>
            </Tabs>
         </Card>
      </div>
   );
}

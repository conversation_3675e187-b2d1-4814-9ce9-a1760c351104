'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, Clock, FileText, Trash2, Upload } from 'lucide-react';
import { useGetDriverKycDocuments } from '../api/queries';
import { useDeleteKycDocument } from '../api/mutations';
import { KycDocument } from '../types/driver';
import { KycDocumentUpload } from './kyc-document-upload';
import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from '@/lib/toast';

interface DriverKycDocumentsProps {
   driverId: string;
}

export function DriverKycDocuments({ driverId }: DriverKycDocumentsProps) {
   const [uploadingDocumentId, setUploadingDocumentId] = useState<string | null>(null);
   const queryClient = useQueryClient();

   const { data: kycDocuments, isLoading, error } = useGetDriverKycDocuments(driverId);
   const deleteKycMutation = useDeleteKycDocument();

   const handleDeleteDocument = async (documentId: string) => {
      if (!confirm('Are you sure you want to delete this document?')) return;

      try {
         await deleteKycMutation.mutateAsync(documentId);
         toast.success('Document deleted successfully');
         queryClient.invalidateQueries({ queryKey: ['driver-kyc-documents', driverId] });
      } catch (error) {
         toast.error(error);
      }
   };

   const getStatusIcon = (status: string) => {
      switch (status) {
         case 'APPROVED':
            return <CheckCircle className='w-4 h-4 text-green-600' />;
         case 'REJECTED':
            return <AlertCircle className='w-4 h-4 text-red-600' />;
         case 'PENDING':
         default:
            return <Clock className='w-4 h-4 text-yellow-600' />;
      }
   };

   const getStatusBadge = (status: string) => {
      switch (status) {
         case 'APPROVED':
            return (
               <Badge variant='default' className='bg-green-100 text-green-800'>
                  Approved
               </Badge>
            );
         case 'REJECTED':
            return <Badge variant='destructive'>Rejected</Badge>;
         case 'PENDING':
         default:
            return (
               <Badge variant='secondary' className='bg-yellow-100 text-yellow-800'>
                  Pending
               </Badge>
            );
      }
   };

   if (isLoading) {
      return (
         <div className='space-y-6'>
            {[1, 2, 3].map(i => (
               <Card key={i} className='p-6'>
                  <div className='animate-pulse'>
                     <div className='h-6 bg-gray-200 rounded w-1/4 mb-4'></div>
                     <div className='h-32 bg-gray-200 rounded'></div>
                  </div>
               </Card>
            ))}
         </div>
      );
   }

   if (error || !kycDocuments?.data) {
      return (
         <Card className='p-8 text-center'>
            <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>Failed to Load KYC Documents</h3>
            <p className='text-gray-600'>
               There was an error loading the KYC documents. Please try again.
            </p>
         </Card>
      );
   }

   return (
      <div className='space-y-6'>
         <div className='mb-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>KYC Verification</h3>
            <p className='text-sm text-gray-600'>Verify your identity documents.</p>
         </div>

         {kycDocuments.data.map((document: KycDocument) => (
            <Card key={document.id} className='p-6'>
               <div className='flex items-start justify-between mb-4'>
                  <div className='flex items-center gap-3'>
                     <FileText className='w-5 h-5 text-gray-400' />
                     <div>
                        <h4 className='font-medium text-gray-900'>{document.name}</h4>
                        {document.isMandatory && (
                           <Badge variant='outline' className='text-xs mt-1'>
                              Required
                           </Badge>
                        )}
                     </div>
                  </div>

                  {document.driverKyc && (
                     <div className='flex items-center gap-2'>
                        {getStatusIcon(document.driverKyc.status)}
                        {getStatusBadge(document.driverKyc.status)}
                     </div>
                  )}
               </div>

               {document.driverKyc ? (
                  <div className='space-y-4'>
                     {/* Document Info */}
                     <div className='bg-gray-50 rounded-lg p-4'>
                        <div className='flex items-center justify-between mb-3'>
                           <div className='flex items-center gap-2'>
                              <FileText className='w-4 h-4 text-gray-500' />
                              <span className='text-sm font-medium text-gray-900'>
                                 Document Uploaded
                              </span>
                           </div>
                           <div className='flex items-center gap-2'>
                              <Button
                                 variant='ghost'
                                 size='sm'
                                 onClick={() =>
                                    window.open(document.driverKyc!.documentUrl, '_blank')
                                 }
                                 className='text-blue-600 hover:text-blue-700'
                              >
                                 View Document
                              </Button>
                              <Button
                                 variant='ghost'
                                 size='sm'
                                 onClick={() => handleDeleteDocument(document.driverKyc!.id)}
                                 disabled={deleteKycMutation.isPending}
                                 className='text-red-600 hover:text-red-700'
                              >
                                 <Trash2 className='w-4 h-4' />
                              </Button>
                           </div>
                        </div>

                        {document.driverKyc.documentNumber && (
                           <div className='mb-2'>
                              <span className='text-xs text-gray-500'>Document Number:</span>
                              <p className='text-sm text-gray-900'>
                                 {document.driverKyc.documentNumber}
                              </p>
                           </div>
                        )}

                        <div className='text-xs text-gray-500'>
                           Uploaded on {new Date(document.driverKyc.createdAt).toLocaleDateString()}
                        </div>
                     </div>

                     {/* Rejection Note */}
                     {document.driverKyc.status === 'REJECTED' &&
                        document.driverKyc.rejectionNote && (
                           <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
                              <div className='flex items-start gap-2'>
                                 <AlertCircle className='w-4 h-4 text-red-600 mt-0.5' />
                                 <div>
                                    <h5 className='text-sm font-medium text-red-800 mb-1'>
                                       Rejection Reason
                                    </h5>
                                    <p className='text-sm text-red-700'>
                                       {document.driverKyc.rejectionNote}
                                    </p>
                                 </div>
                              </div>
                           </div>
                        )}

                     {/* Re-upload option for rejected documents */}
                     {document.driverKyc.status === 'REJECTED' && (
                        <div className='pt-4 border-t border-gray-200'>
                           <Button
                              variant='outline'
                              size='sm'
                              onClick={() => setUploadingDocumentId(document.id)}
                              className='gap-2'
                           >
                              <Upload className='w-4 h-4' />
                              Re-upload Document
                           </Button>
                        </div>
                     )}
                  </div>
               ) : (
                  /* Upload Section */
                  <div className='space-y-4'>
                     <div className='text-center py-8 border-2 border-dashed border-gray-300 rounded-lg'>
                        <FileText className='w-12 h-12 text-gray-400 mx-auto mb-4' />
                        <h4 className='text-sm font-medium text-gray-900 mb-2'>
                           No document uploaded
                        </h4>
                        <p className='text-xs text-gray-500 mb-4'>
                           Upload your {document.name.toLowerCase()} to complete verification
                        </p>
                        <Button
                           variant='outline'
                           size='sm'
                           onClick={() => setUploadingDocumentId(document.id)}
                           className='gap-2'
                        >
                           <Upload className='w-4 h-4' />
                           Upload Document
                        </Button>
                     </div>
                  </div>
               )}

               {/* Upload Modal */}
               {uploadingDocumentId === document.id && (
                  <KycDocumentUpload
                     document={document}
                     driverId={driverId}
                     onClose={() => setUploadingDocumentId(null)}
                     onSuccess={() => {
                        setUploadingDocumentId(null);
                        queryClient.invalidateQueries({
                           queryKey: ['driver-kyc-documents', driverId],
                        });
                     }}
                  />
               )}
            </Card>
         ))}
      </div>
   );
}

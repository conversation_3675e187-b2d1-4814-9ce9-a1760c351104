'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { Spinner } from '@/components/ui/spinner';
import { ErrorMessage } from '@/components/error-message';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useVerifyDriverOtp, useResendDriverOtp } from '../api/mutations';

// OTP validation schema
const otpVerificationSchema = z.object({
   otp: z.string().min(4, 'OTP must be 4 digits').max(4, 'OTP must be 4 digits'),
});

type OtpVerificationFormValues = z.infer<typeof otpVerificationSchema>;

interface DriverOtpVerificationProps {
   phoneNumber: string;
   onSuccess: (phoneNumber: string) => void;
   onBack: () => void;
}

export const DriverOtpVerification = ({ phoneNumber, onSuccess, onBack }: DriverOtpVerificationProps) => {
   const [timeLeft, setTimeLeft] = useState(30);
   const [canResend, setCanResend] = useState(false);

   const verifyOtpMutation = useVerifyDriverOtp();
   const resendOtpMutation = useResendDriverOtp();

   const form = useForm<OtpVerificationFormValues>({
      resolver: zodResolver(otpVerificationSchema),
      defaultValues: {
         otp: '',
      },
   });

   const {
      control,
      handleSubmit,
      formState: { errors },
      setValue,
      setFocus,
   } = form;

   // Countdown timer for resend
   useEffect(() => {
      if (timeLeft > 0) {
         const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
         return () => clearTimeout(timer);
      } else {
         setCanResend(true);
      }
   }, [timeLeft]);

   const onSubmit = async (data: OtpVerificationFormValues) => {
      const payload = {
         phoneNumber,
         otp: data.otp,
      };

      verifyOtpMutation.mutate(payload, {
         onSuccess: () => {
            toast.success('OTP verified successfully');
            onSuccess(phoneNumber);
         },
      });
   };

   const handleResendOtp = () => {
      if (!canResend) return;

      resendOtpMutation.mutate(
         { phoneNumber },
         {
            onSuccess: () => {
               setValue('otp', '');
               setFocus('otp');
               toast.success('OTP resent successfully');
               setTimeLeft(30);
               setCanResend(false);
            },
         }
      );
   };

   return (
      <div className='space-y-6'>
         <div className='text-center space-y-2'>
            <p className='text-sm text-muted-foreground'>
               Enter the 4-digit code sent to{' '}
               <span className='font-medium text-foreground'>{phoneNumber}</span>
            </p>
         </div>

         <Form {...form}>
            <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
               <div className='flex justify-center'>
                  <FormField
                     control={control}
                     name='otp'
                     render={({ field }) => (
                        <FormItem>
                           <FormControl>
                              <InputOTP maxLength={4} {...field}>
                                 <InputOTPGroup className='flex justify-center gap-2 w-[200px]'>
                                    {[0, 1, 2, 3].map(index => (
                                       <InputOTPSlot
                                          key={index}
                                          index={index}
                                          className='rounded-md border border-input w-12 h-12'
                                       />
                                    ))}
                                 </InputOTPGroup>
                              </InputOTP>
                           </FormControl>
                           <div className='flex justify-center'>
                              <ErrorMessage error={errors.otp} />
                           </div>
                        </FormItem>
                     )}
                  />
               </div>

               <Button type='submit' className='w-full' disabled={verifyOtpMutation.isPending}>
                  {verifyOtpMutation.isPending ? (
                     <>
                        Verifying...
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : (
                     'Verify OTP'
                  )}
               </Button>

               <div className='text-center space-y-3'>
                  <p className='text-sm text-muted-foreground'>
                     Didn't receive the OTP?{' '}
                     <Button
                        type='button'
                        variant='link'
                        className='p-0 h-auto font-semibold'
                        disabled={!canResend || resendOtpMutation.isPending}
                        onClick={handleResendOtp}
                     >
                        {resendOtpMutation.isPending ? (
                           <>
                              Resending...
                              <Spinner className='ml-2 h-3 w-3' />
                           </>
                        ) : canResend ? (
                           'Resend OTP'
                        ) : (
                           `Resend in ${timeLeft}s`
                        )}
                     </Button>
                  </p>
               </div>

               <div className='flex justify-center'>
                  <Button
                     type='button'
                     variant='link'
                     onClick={onBack}
                     className='flex items-center gap-2 text-sm text-primary hover:underline'
                  >
                     <ArrowLeft className='h-4 w-4' />
                     Back to Phone Number
                  </Button>
               </div>
            </form>
         </Form>
      </div>
   );
};

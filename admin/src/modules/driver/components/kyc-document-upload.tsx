'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useFileUpload, formatBytes } from '@/hooks/use-file-upload';
import { toast } from '@/lib/toast';
import { AlertCircle, FileText, Upload, X, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useFileUpload as useFileUploadMutation, useAddKycDocument } from '../api/mutations';
import { KycDocument } from '../types/driver';

interface KycDocumentUploadProps {
   document: KycDocument;
   driverId: string;
   onClose: () => void;
   onSuccess: () => void;
}

export function KycDocumentUpload({ document, driverId, onClose, onSuccess }: KycDocumentUploadProps) {
   const [documentNumber, setDocumentNumber] = useState('');
   const [isSubmitting, setIsSubmitting] = useState(false);
   
   const fileUploadMutation = useFileUploadMutation();
   const addKycDocumentMutation = useAddKycDocument();

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.pdf,.jpg,.jpeg,.png';

   const [
      { files, isDragging, errors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   const handleSubmit = async () => {
      if (!file) {
         toast.error('Please select a file to upload');
         return;
      }

      setIsSubmitting(true);

      try {
         // Step 1: Upload file to S3
         const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
         
         // Step 2: Add KYC document
         await addKycDocumentMutation.mutateAsync({
            userProfileId: driverId,
            kycDocumentId: document.id,
            documentUrl: uploadResponse.data.key, // Use the key, not the URL
            fromDigilocker: false,
         });

         toast.success('Document uploaded successfully');
         onSuccess();
      } catch (error) {
         console.error('Upload error:', error);
         toast.error('Failed to upload document. Please try again.');
      } finally {
         setIsSubmitting(false);
      }
   };

   const requiredFields = document.requiredFields?.fields || [];
   const hasDocumentNumberField = requiredFields.some(field => 
      field.includes('number') || field.includes('licence') || field.includes('aadhaar')
   );

   return (
      <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
         <Card className='w-full max-w-2xl max-h-[90vh] overflow-y-auto'>
            <div className='p-6'>
               {/* Header */}
               <div className='flex items-center justify-between mb-6'>
                  <div>
                     <h3 className='text-lg font-semibold text-gray-900'>Upload {document.name}</h3>
                     <p className='text-sm text-gray-600 mt-1'>
                        Upload your {document.name.toLowerCase()} document for verification
                     </p>
                  </div>
                  <Button variant='ghost' size='sm' onClick={onClose}>
                     <X className='w-4 h-4' />
                  </Button>
               </div>

               {/* Digilocker Option */}
               <Card className='p-4 mb-6 bg-blue-50 border-blue-200'>
                  <div className='flex items-center justify-between'>
                     <div>
                        <h4 className='font-medium text-blue-900'>Fetch from Digilocker</h4>
                        <p className='text-sm text-blue-700 mt-1'>
                           Get your document directly from Digilocker
                        </p>
                     </div>
                     <Button variant='outline' disabled className='bg-white'>
                        Fetch from Digilocker
                     </Button>
                  </div>
               </Card>

               <div className='text-center text-sm text-gray-500 mb-6'>OR UPLOAD MANUALLY</div>

               {/* File Upload Area */}
               <div className='space-y-4'>
                  <div
                     role='button'
                     onClick={openFileDialog}
                     onDragEnter={handleDragEnter}
                     onDragLeave={handleDragLeave}
                     onDragOver={handleDragOver}
                     onDrop={handleDrop}
                     data-dragging={isDragging || undefined}
                     className='border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-40 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]'
                  >
                     <input
                        {...getInputProps()}
                        className='sr-only'
                        aria-label='Upload file'
                        disabled={Boolean(file)}
                     />

                     <div className='flex flex-col items-center justify-center text-center'>
                        <div
                           className='bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border'
                           aria-hidden='true'
                        >
                           <Upload className='size-4 opacity-60' />
                        </div>
                        <p className='mb-1.5 text-sm font-medium'>Upload Document</p>
                        <p className='text-muted-foreground text-xs'>
                           Drag & drop or click to browse (max. {formatBytes(maxSize)})
                        </p>
                        <p className='text-muted-foreground text-xs mt-1'>
                           Supported: JPEG, PNG, PDF
                        </p>
                     </div>
                  </div>

                  {errors.length > 0 && (
                     <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
                        <AlertCircle className='size-3 shrink-0' />
                        <span>{errors[0]}</span>
                     </div>
                  )}

                  {/* File Preview */}
                  {file && (
                     <Card className='p-4'>
                        <div className='flex items-center justify-between'>
                           <div className='flex items-center gap-3'>
                              <div className='flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg'>
                                 <FileText className='w-5 h-5 text-blue-600' />
                              </div>
                              <div>
                                 <p className='text-sm font-medium text-gray-900'>{file.file.name}</p>
                                 <p className='text-xs text-gray-500'>{formatBytes(file.file.size)}</p>
                              </div>
                           </div>
                           <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => removeFile(file.id)}
                              className='text-red-600 hover:text-red-700'
                           >
                              <X className='w-4 h-4' />
                           </Button>
                        </div>
                     </Card>
                  )}

                  {/* Document Number Field */}
                  {hasDocumentNumberField && (
                     <div className='space-y-2'>
                        <Label htmlFor='documentNumber'>
                           Document Number
                           {requiredFields.some(field => field.includes('aadhaar')) && (
                              <span className='text-xs text-gray-500 ml-1'>(e.g., **************)</span>
                           )}
                        </Label>
                        <Input
                           id='documentNumber'
                           value={documentNumber}
                           onChange={(e) => setDocumentNumber(e.target.value)}
                           placeholder={
                              requiredFields.some(field => field.includes('aadhaar'))
                                 ? 'Enter Aadhaar number'
                                 : 'Enter document number'
                           }
                        />
                     </div>
                  )}

                  {/* Action Buttons */}
                  <div className='flex gap-3 pt-4'>
                     <Button variant='outline' onClick={onClose} className='flex-1'>
                        Cancel
                     </Button>
                     <Button
                        onClick={handleSubmit}
                        disabled={!file || isSubmitting}
                        className='flex-1 gap-2'
                     >
                        {isSubmitting ? (
                           <>
                              <Loader2 className='w-4 h-4 animate-spin' />
                              Uploading...
                           </>
                        ) : (
                           <>
                              <Upload className='w-4 h-4' />
                              Submit
                           </>
                        )}
                     </Button>
                  </div>
               </div>
            </div>
         </Card>
      </div>
   );
}

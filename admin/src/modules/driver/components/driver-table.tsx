'use client';

import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useDeleteDriver } from '../api/mutations';
import { Driver, ListDriverResponse } from '../types/driver';
import { DeleteDriverDialog } from './delete-driver-dialog';
import { EditDriver } from './edit-driver';
import { DriverTableEmpty } from './driver-table-empty';
import { DriverTableFilteredEmpty } from './driver-table-filtered-empty';
import { DriverTableLoading } from './driver-table-loading';
import { CustomPagination } from '@/components/pagination';

// Define the columns for the table
const getColumns = ({
   deleteDriverMutation,
   handleDeleteClick,
   handleEditClick,
   handleViewClick,
   driverToDelete,
}: {
   handleDeleteClick: (id: string) => void;
   handleEditClick: (id: string) => void;
   handleViewClick: (id: string) => void;
   deleteDriverMutation: any;
   driverToDelete: string | null;
}): ColumnDef<Driver>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Driver</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         const fullName =
            driver.firstName && driver.lastName
               ? `${driver.firstName} ${driver.lastName}`
               : driver.firstName || driver.lastName || 'No Name';
         return (
            <div className='text-left'>
               <div className='font-semibold text-sm'>{fullName}</div>
               <div className='text-xs text-gray-500'>ID: {driver.id.slice(0, 8)}...</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'contact',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Contact</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='text-left'>
               <div className='text-sm'>{driver.phoneNumber}</div>
               <div className='text-xs text-gray-500'>{driver.email || '-'}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'city',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>City</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='text-left'>
               <div className='text-sm'>{driver.cityName || '-'}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'createdAt',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Created</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         const createdAt = driver.createdAt;
         if (!createdAt) return <div className='text-left text-sm'>-</div>;

         const date = new Date(createdAt);
         return (
            <div className='text-left'>
               <span className='text-sm'>
                  {date.toLocaleDateString('en-US', {
                     year: 'numeric',
                     month: '2-digit',
                     day: '2-digit',
                  })}
               </span>
            </div>
         );
      },
      size: 120,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 px-3 py-1 rounded-md transition-colors bg-white'
                  onClick={() => handleViewClick(driver.id)}
               >
                  View
               </button>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white'
                  onClick={() => handleEditClick(driver.id)}
               >
                  Edit
               </button>
               <button
                  className='hidden ktext-sm font-medium text-gray-600 hover:text-red-600 border border-gray-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white'
                  onClick={() => handleDeleteClick(driver.id)}
                  disabled={deleteDriverMutation.isPending && driverToDelete === driver.id}
               >
                  Delete
               </button>
            </div>
         );
      },
      size: 200,
   },
];

interface DriverTableProps {
   data: ListDriverResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters: boolean;
   hasSearch: boolean;
   hasStatus: boolean;
   hasLocation: boolean;
   onClearFilters: () => void;
}

export function DriverTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters,
   hasSearch,
   hasStatus,
   hasLocation,
   onClearFilters,
}: DriverTableProps) {
   const [driverToDelete, setDriverToDelete] = useState<string | null>(null);
   const [driverToEdit, setDriverToEdit] = useState<string | null>(null);
   const deleteDriverMutation = useDeleteDriver();
   const queryClient = useQueryClient();
   const router = useRouter();

   const handleDeleteClick = (id: string) => {
      setDriverToDelete(id);
   };

   const handleEditClick = (id: string) => {
      setDriverToEdit(id);
   };

   const handleViewClick = (id: string) => {
      router.push(`/dashboard/drivers/${id}`);
   };

   const handleDeleteConfirm = () => {
      if (!driverToDelete) return;

      deleteDriverMutation.mutate(driverToDelete, {
         onSuccess: () => {
            toast.success('Driver deleted successfully');
            setDriverToDelete(null);
            queryClient.invalidateQueries({ queryKey: ['drivers'] });
         },
         onError: (error: any) => {
            toast.error(error?.message || 'Failed to delete driver');
            setDriverToDelete(null);
         },
      });
   };

   const columns = getColumns({
      deleteDriverMutation,
      handleDeleteClick,
      handleEditClick,
      handleViewClick,
      driverToDelete,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   // Loading state
   if (isLoading) {
      return <DriverTableLoading />;
   }

   // Empty state with filters
   if (!data?.data?.length && hasFilters) {
      return (
         <DriverTableFilteredEmpty
            hasSearch={hasSearch}
            hasStatus={hasStatus}
            hasLocation={hasLocation}
            onClearFilters={onClearFilters}
         />
      );
   }

   // Empty state without filters
   if (!data?.data?.length) {
      return <DriverTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {/* Pagination */}
         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}

         {/* Delete Confirmation Dialog */}
         <DeleteDriverDialog
            isOpen={!!driverToDelete}
            onClose={() => setDriverToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteDriverMutation.isPending}
         />

         {/* Edit Driver Sheet */}
         <EditDriver
            driverId={driverToEdit}
            isOpen={!!driverToEdit}
            onClose={() => setDriverToEdit(null)}
         />
      </div>
   );
}

'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, Mail, MapPin, Phone, User, UserCheck, UserX } from 'lucide-react';
import { Driver } from '../types/driver';

interface DriverPersonalDetailsProps {
   driver: Driver;
}

export function DriverPersonalDetails({ driver }: DriverPersonalDetailsProps) {
   const formatDate = (dateString: string | null) => {
      if (!dateString) return 'Not provided';
      return new Date(dateString).toLocaleDateString('en-US', {
         year: 'numeric',
         month: 'long',
         day: 'numeric',
      });
   };

   const formatDateTime = (dateString: string) => {
      return new Date(dateString).toLocaleString('en-US', {
         year: 'numeric',
         month: 'long',
         day: 'numeric',
         hour: '2-digit',
         minute: '2-digit',
      });
   };

   return (
      <div className='space-y-6'>
         {/* Basic Information */}
         <Card className='p-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>Basic Information</h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
               <div className='space-y-4'>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Full Name</label>
                     <p className='text-sm text-gray-900 mt-1'>
                        {`${driver.firstName || ''} ${driver.lastName || ''}`.trim() || 'Not provided'}
                     </p>
                  </div>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Gender</label>
                     <p className='text-sm text-gray-900 mt-1'>
                        {driver.gender ? driver.gender.charAt(0) + driver.gender.slice(1).toLowerCase() : 'Not provided'}
                     </p>
                  </div>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Date of Birth</label>
                     <p className='text-sm text-gray-900 mt-1'>{formatDate(driver.dob)}</p>
                  </div>
               </div>
               <div className='space-y-4'>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>User ID</label>
                     <p className='text-sm text-gray-900 mt-1 font-mono'>{driver.userId}</p>
                  </div>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Driver ID</label>
                     <p className='text-sm text-gray-900 mt-1 font-mono'>{driver.id}</p>
                  </div>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Referral Code</label>
                     <p className='text-sm text-gray-900 mt-1'>
                        {driver.referralCode || 'Not provided'}
                     </p>
                  </div>
               </div>
            </div>
         </Card>

         {/* Contact Information */}
         <Card className='p-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>Contact Information</h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
               <div className='space-y-4'>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Phone Number</label>
                     <div className='flex items-center gap-2 mt-1'>
                        <Phone className='w-4 h-4 text-gray-400' />
                        <p className='text-sm text-gray-900'>{driver.phoneNumber}</p>
                        <Badge variant={driver.phoneVerified ? 'default' : 'secondary'} className='text-xs'>
                           {driver.phoneVerified ? (
                              <>
                                 <UserCheck className='w-3 h-3 mr-1' />
                                 Verified
                              </>
                           ) : (
                              <>
                                 <UserX className='w-3 h-3 mr-1' />
                                 Unverified
                              </>
                           )}
                        </Badge>
                     </div>
                  </div>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Email Address</label>
                     <div className='flex items-center gap-2 mt-1'>
                        <Mail className='w-4 h-4 text-gray-400' />
                        <p className='text-sm text-gray-900'>{driver.email || 'Not provided'}</p>
                        {driver.email && (
                           <Badge variant={driver.emailVerified ? 'default' : 'secondary'} className='text-xs'>
                              {driver.emailVerified ? (
                                 <>
                                    <UserCheck className='w-3 h-3 mr-1' />
                                    Verified
                                 </>
                              ) : (
                                 <>
                                    <UserX className='w-3 h-3 mr-1' />
                                    Unverified
                                 </>
                              )}
                           </Badge>
                        )}
                     </div>
                  </div>
               </div>
               <div className='space-y-4'>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>City</label>
                     <div className='flex items-center gap-2 mt-1'>
                        <MapPin className='w-4 h-4 text-gray-400' />
                        <p className='text-sm text-gray-900'>{driver.cityName || 'Not specified'}</p>
                     </div>
                  </div>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>City ID</label>
                     <p className='text-sm text-gray-900 mt-1 font-mono'>{driver.cityId || 'Not specified'}</p>
                  </div>
               </div>
            </div>
         </Card>

         {/* Profile Information */}
         <Card className='p-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>Profile Information</h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
               <div className='space-y-4'>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Profile Picture</label>
                     <div className='flex items-center gap-3 mt-2'>
                        {driver.profilePictureUrl ? (
                           <img
                              src={driver.profilePictureUrl}
                              alt='Profile'
                              className='w-12 h-12 rounded-full object-cover border-2 border-gray-200'
                           />
                        ) : (
                           <div className='w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center border-2 border-gray-200'>
                              <User className='w-6 h-6 text-gray-400' />
                           </div>
                        )}
                        <p className='text-sm text-gray-900'>
                           {driver.profilePictureUrl ? 'Profile picture uploaded' : 'No profile picture'}
                        </p>
                     </div>
                  </div>
               </div>
               <div className='space-y-4'>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Role ID</label>
                     <p className='text-sm text-gray-900 mt-1 font-mono'>{driver.roleId}</p>
                  </div>
               </div>
            </div>
         </Card>

         {/* Account Information */}
         <Card className='p-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>Account Information</h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
               <div className='space-y-4'>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Created At</label>
                     <div className='flex items-center gap-2 mt-1'>
                        <CalendarDays className='w-4 h-4 text-gray-400' />
                        <p className='text-sm text-gray-900'>{formatDateTime(driver.createdAt)}</p>
                     </div>
                  </div>
               </div>
               <div className='space-y-4'>
                  <div>
                     <label className='text-sm font-medium text-gray-500'>Last Updated</label>
                     <div className='flex items-center gap-2 mt-1'>
                        <CalendarDays className='w-4 h-4 text-gray-400' />
                        <p className='text-sm text-gray-900'>{formatDateTime(driver.updatedAt)}</p>
                     </div>
                  </div>
               </div>
            </div>
         </Card>
      </div>
   );
}

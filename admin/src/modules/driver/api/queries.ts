import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { City, DriverResponse, ListDriverParams, ListDriverResponse } from '../types/driver';

export const useListDriver = ({
   page = 1,
   limit = 10,
   search,
   sortBy,
   sortOrder,
   cityId,
   name,
   email,
   phoneNumber,
}: ListDriverParams) => {
   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: [
         'drivers',
         page,
         limit,
         search,
         sortBy,
         sortOrder,
         cityId,
         name,
         email,
         phoneNumber,
      ],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListDriverResponse> => {
         return apiClient.get('/drivers/admin', {
            params: {
               page,
               limit,
               sortBy,
               sortOrder,
               search,
               cityId,
               name,
               email,
               phoneNumber,
            },
         });
      },
   });
};

export const useGetDriver = (id: string | null) => {
   return useQuery({
      queryKey: ['driver', id],
      queryFn: (): Promise<DriverResponse> => {
         if (!id) throw new Error('Driver ID is required');
         return apiClient.get(`/drivers/admin/${id}`);
      },
      enabled: !!id, // Only run the query if id is provided
   });
};

/**
 * Hook for fetching all cities
 */
export const useCities = () => {
   return useQuery({
      queryKey: ['cities'],
      queryFn: (): Promise<{
         success: boolean;
         message: string;
         data: City[];
         timestamp: number;
      }> => {
         return apiClient.get('/cities');
      },
      staleTime: 5 * 60 * 1000, // 5 minutes - cities don't change often
      refetchOnWindowFocus: false,
   });
};

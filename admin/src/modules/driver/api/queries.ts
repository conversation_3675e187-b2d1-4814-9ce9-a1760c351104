import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   City,
   DriverResponse,
   ListDriverParams,
   ListDriverResponse,
   KycDocumentsResponse,
} from '../types/driver';

export const useListDriver = ({
   page = 1,
   limit = 10,
   search,
   sortBy,
   sortOrder,
   cityId,
   name,
   email,
   phoneNumber,
}: ListDriverParams) => {
   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: [
         'drivers',
         page,
         limit,
         search,
         sortBy,
         sortOrder,
         cityId,
         name,
         email,
         phoneNumber,
      ],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListDriverResponse> => {
         return apiClient.get('/drivers/admin', {
            params: {
               page,
               limit,
               sortBy,
               sortOrder,
               search,
               cityId,
               name,
               email,
               phoneNumber,
            },
         });
      },
   });
};

export const useGetDriver = (id: string) => {
   return useQuery({
      queryKey: ['driver', id],
      queryFn: (): Promise<DriverResponse> => {
         return apiClient.get(`/drivers/admin/${id}`);
      },
      enabled: !!id, // Only run the query if id is provided
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for fetching KYC documents for a driver
 */
export const useGetDriverKycDocuments = (profileId: string) => {
   return useQuery({
      queryKey: ['driver-kyc-documents', profileId],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<KycDocumentsResponse> => {
         return apiClient.get(`/driver-kyc/admin/list-documents/user-profile/${profileId}`);
      },
      enabled: !!profileId,
   });
};

/**
 * Hook for fetching all cities
 */
export const useCities = () => {
   return useQuery({
      queryKey: ['cities'],
      queryFn: (): Promise<{
         success: boolean;
         message: string;
         data: City[];
         timestamp: number;
      }> => {
         return apiClient.get('/cities');
      },
      staleTime: 5 * 60 * 1000, // 5 minutes - cities don't change often
      refetchOnWindowFocus: false,
   });
};

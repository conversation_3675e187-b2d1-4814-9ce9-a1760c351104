"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
   SidebarGroup,
   SidebarMenu,
   SidebarMenuButton,
   SidebarMenuItem,
   SidebarMenuSub,
   SidebarMenuSubButton,
   SidebarMenuSubItem,
} from '@/components/ui/sidebar';

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  return (
     <SidebarGroup>
        <SidebarMenu>
           {items.map(item => {
              // If item has sub-items, render as collapsible
              if (item.items && item.items.length > 0) {
                 return (
                    <Collapsible
                       key={item.title}
                       asChild
                       defaultOpen={item.isActive}
                       className='group/collapsible'
                    >
                       <SidebarMenuItem>
                          <CollapsibleTrigger asChild>
                             <SidebarMenuButton tooltip={item.title}>
                                {item.icon && <item.icon />}
                                <span>{item.title}</span>
                                <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
                             </SidebarMenuButton>
                          </CollapsibleTrigger>
                          <CollapsibleContent>
                             <SidebarMenuSub>
                                {item.items.map(subItem => (
                                   <SidebarMenuSubItem key={subItem.title}>
                                      <SidebarMenuSubButton asChild>
                                         <a href={subItem.url}>
                                            <span>{subItem.title}</span>
                                         </a>
                                      </SidebarMenuSubButton>
                                   </SidebarMenuSubItem>
                                ))}
                             </SidebarMenuSub>
                          </CollapsibleContent>
                       </SidebarMenuItem>
                    </Collapsible>
                 );
              }

              // If no sub-items, render as direct link
              return (
                 <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild tooltip={item.title} isActive={item.isActive}>
                       <a href={item.url}>
                          {item.icon && <item.icon />}
                          <span>{item.title}</span>
                       </a>
                    </SidebarMenuButton>
                 </SidebarMenuItem>
              );
           })}
        </SidebarMenu>
     </SidebarGroup>
  );
}

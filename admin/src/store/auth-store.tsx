import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type AuthState = {
  authToken: string | null;
  refreshToken: string | null;
  userId: string | null;
  phoneNumber: string | null;
};

type AuthActions = {
  setToken: (authToken: string, refreshToken: string) => void;
  clearToken: () => void;
  setUserId: (userId: string) => void;
  setPhoneNumber: (phoneNumber: string) => void;
  reset: () => void;
};

const initialState: AuthState = {
  authToken: null,
  refreshToken: null,
  userId: null,
  phoneNumber: null,
};

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set) => ({
      ...initialState,
      setToken: (authToken, refreshToken) => set({ authToken, refreshToken }),
      clearToken: () => set({ authToken: null, refreshToken: null }),
      setUserId: (userId) => set({ userId }),
      setPhoneNumber: (phoneNumber) => set({ phoneNumber }),
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'auth-store',
    },
  ),
);

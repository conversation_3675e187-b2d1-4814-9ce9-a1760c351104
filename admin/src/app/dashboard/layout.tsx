'use client';

import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { UserProfileDropdown } from '@/components/user-profile-dropdown';

export default function DashboardLayout({
   children,
}: {
   children: React.ReactNode;
}) {
   return (
      <SidebarProvider>
         <AppSidebar />
         <SidebarInset>
            <header className='flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 border-b border-gray-200 bg-white px-4'>
               <div className='flex items-center gap-2'>
                  <SidebarTrigger className='-ml-1' />
                  <Separator
                     orientation='vertical'
                     className='mr-2 data-[orientation=vertical]:h-4'
                  />
               </div>
               
               <div className='flex items-center gap-4'>
                  <UserProfileDropdown />
               </div>
            </header>

            <div className='flex flex-1 flex-col'>
               {children}
            </div>
         </SidebarInset>
      </SidebarProvider>
   );
} 
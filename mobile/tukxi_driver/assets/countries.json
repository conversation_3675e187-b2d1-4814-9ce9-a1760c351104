[{"flagCode": "AF", "country": "Afghanistan", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇦🇫", "code": "93"}, {"flagCode": "AL", "country": "Albania", "maxLength": 9, "minLength": 3, "phLength": "3 to 9", "flag": "🇦🇱", "code": "355"}, {"flagCode": "DZ", "country": "Algeria", "maxLength": 9, "minLength": 8, "phLength": "8 or 9", "flag": "🇩🇿", "code": "213"}, {"flagCode": "AS", "country": "American Samoa", "maxLength": 10, "minLength": 10, "phLength": "(684) + 7", "flag": "🇦🇸", "code": "1-684"}, {"flagCode": "AD", "country": "Andorra", "maxLength": 9, "minLength": 6, "phLength": "6, 8 or 9", "flag": "🇦🇩", "code": "376"}, {"flagCode": "AO", "country": "Angola", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇦🇴", "code": "244"}, {"flagCode": "AI", "country": "<PERSON><PERSON><PERSON>", "maxLength": 10, "minLength": 10, "phLength": "(264) + 7", "flag": "🇦🇮", "code": "1-264"}, {"flagCode": "AG", "country": "Antigua and Barbuda", "maxLength": 10, "minLength": 10, "phLength": "(268) + 7", "flag": "🇦🇬", "code": "1-268"}, {"flagCode": "AR", "country": "Argentina", "maxLength": 10, "minLength": 10, "phLength": 10, "flag": "🇦🇷", "code": "54"}, {"flagCode": "AM", "country": "Armenia", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇦🇲", "code": "374"}, {"flagCode": "AW", "country": "Aruba", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇦🇼", "code": "297"}, {"flagCode": "AU", "country": "Australia", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇦🇺", "code": "61"}, {"flagCode": "AT", "country": "Austria", "maxLength": 11, "minLength": 11, "phLength": "11", "flag": "🇦🇹", "code": "43"}, {"flagCode": "AZ", "country": "Azerbaijan", "maxLength": 9, "minLength": 8, "phLength": "8 to 9", "flag": "🇦🇿", "code": "994"}, {"flagCode": "BS", "country": "Bahamas", "maxLength": 10, "minLength": 10, "phLength": "(242) + 7", "flag": "🇧🇸", "code": "1-242"}, {"flagCode": "BH", "country": "Bahrain", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇧🇭", "code": "973"}, {"flagCode": "BD", "country": "Bangladesh", "maxLength": 11, "minLength": 11, "phLength": "11", "flag": "🇧🇩", "code": "880"}, {"flagCode": "BB", "country": "Barbados", "maxLength": 10, "minLength": 10, "phLength": "(246) + 7", "flag": "🇧🇧", "code": "1-246"}, {"flagCode": "BY", "country": "Belarus", "maxLength": 10, "minLength": 9, "phLength": "9 to 10", "flag": "🇧🇾", "code": "375"}, {"flagCode": "BE", "country": "Belgium", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇧🇪", "code": "32"}, {"flagCode": "BZ", "country": "Belize", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇧🇿", "code": "501"}, {"flagCode": "BJ", "country": "Benin", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇧🇯", "code": "229"}, {"flagCode": "BM", "country": "Bermuda", "maxLength": 10, "minLength": 10, "phLength": "(441) + 7", "flag": "🇧🇲", "code": "1-441"}, {"flagCode": "BT", "country": "Bhutan", "maxLength": 8, "minLength": 7, "phLength": "7 to 8", "flag": "🇧🇹", "code": "975"}, {"flagCode": "BO", "country": "Bolivia", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇧🇴", "code": "591"}, {"flagCode": "BA", "country": "Bosnia and Herzegovina", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇧🇦", "code": "387"}, {"flagCode": "BW", "country": "Botswana", "maxLength": 8, "minLength": 7, "phLength": "7 to 8", "flag": "🇧🇼", "code": "267"}, {"flagCode": "BR", "country": "Brazil", "maxLength": 11, "minLength": 11, "phLength": "11", "flag": "🇧🇷", "code": "55"}, {"flagCode": "VG", "country": "British Virgin Islands", "maxLength": 10, "minLength": 10, "phLength": "(284) + 7", "flag": "🇻🇬", "code": "1-284"}, {"flagCode": "BN", "country": "Brunei", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇧🇳", "code": "673"}, {"flagCode": "BG", "country": "Bulgaria", "maxLength": 9, "minLength": 7, "phLength": "7 to 9", "flag": "🇧🇬", "code": "359"}, {"flagCode": "BF", "country": "Burkina Faso", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇧🇫", "code": "226"}, {"flagCode": "BI", "country": "Burundi", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇧🇮", "code": "257"}, {"flagCode": "KH", "country": "Cambodia", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇰🇭", "code": "855"}, {"flagCode": "CM", "country": "Cameroon", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇨🇲", "code": "237"}, {"flagCode": "CA", "country": "Canada", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇨🇦", "code": "1"}, {"flagCode": "CV", "country": "Cape Verde", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇨🇻", "code": "238"}, {"flagCode": "KY", "country": "Cayman Islands", "maxLength": 10, "minLength": 10, "phLength": "(345) + 7", "flag": "🇰🇾", "code": "1-345"}, {"flagCode": "CF", "country": "Central African Republic", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇨🇫", "code": "236"}, {"flagCode": "TD", "country": "Chad", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇹🇩", "code": "235"}, {"flagCode": "CL", "country": "Chile", "maxLength": 9, "minLength": 8, "phLength": "8 to 9", "flag": "🇨🇱", "code": "56"}, {"flagCode": "CN", "country": "China", "maxLength": 11, "minLength": 11, "phLength": "11", "flag": "🇨🇳", "code": "86"}, {"flagCode": "CO", "country": "Colombia", "maxLength": 10, "minLength": 8, "phLength": "8 or 10", "flag": "🇨🇴", "code": "57"}, {"flagCode": "KM", "country": "Comoros", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇰🇲", "code": "269"}, {"flagCode": "CK", "country": "Cook Islands", "maxLength": 5, "minLength": 5, "phLength": 5, "flag": "🇨🇰", "code": "682"}, {"flagCode": "CR", "country": "Costa Rica", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇨🇷", "code": "506"}, {"flagCode": "HR", "country": "Croatia", "maxLength": 12, "minLength": 8, "phLength": "8 to 12", "flag": "🇭🇷", "code": "385"}, {"flagCode": "CU", "country": "Cuba", "maxLength": 8, "minLength": 6, "phLength": "6 to 8", "flag": "🇨🇺", "code": "53"}, {"flagCode": "CW", "country": "Curacao", "maxLength": 8, "minLength": 7, "phLength": "7 to 8", "flag": "🇨🇼", "code": "599"}, {"flagCode": "CY", "country": "Cyprus", "maxLength": 11, "minLength": 8, "phLength": "8 or 11", "flag": "🇨🇾", "code": "357"}, {"flagCode": "CZ", "country": "Czech Republic", "maxLength": 12, "minLength": 4, "phLength": "4 to 12", "flag": "🇨🇿", "code": "420"}, {"flagCode": "CD", "country": "DR Congo", "maxLength": 9, "minLength": 5, "phLength": "5 to 9", "flag": "🇨🇩", "code": "243"}, {"flagCode": "DK", "country": "Denmark", "maxLength": 8, "minLength": 8, "phLength": "8", "flag": "🇩🇰", "code": "45"}, {"flagCode": "DJ", "country": "Djibouti", "maxLength": 6, "minLength": 6, "phLength": 6, "flag": "🇩🇯", "code": "253"}, {"flagCode": "DM", "country": "Dominica", "maxLength": 10, "minLength": 10, "phLength": "(767) + 7", "flag": "🇩🇲", "code": "1-767"}, {"flagCode": "DO", "country": "Dominican Republic", "maxLength": 10, "minLength": 10, "phLength": "(809/829) + 7", "flag": "🇩🇴", "code": "1-809"}, {"flagCode": "EC", "country": "Ecuador", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇪🇨", "code": "593"}, {"flagCode": "EG", "country": "Egypt", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇪🇬", "code": "20"}, {"flagCode": "SV", "country": "El Salvador", "maxLength": 11, "minLength": 7, "phLength": "7, 8 or 11", "flag": "🇸🇻", "code": "503"}, {"flagCode": "GQ", "country": "Equatorial Guinea", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇬🇶", "code": "240"}, {"flagCode": "ER", "country": "Eritrea", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇪🇷", "code": "291"}, {"flagCode": "EE", "country": "Estonia", "maxLength": 10, "minLength": 7, "phLength": "7 to 10", "flag": "🇪🇪", "code": "372"}, {"flagCode": "SZ", "country": "<PERSON><PERSON><PERSON><PERSON>", "maxLength": 8, "minLength": 7, "phLength": "7 to 8", "flag": "🇸🇿", "code": "268"}, {"flagCode": "ET", "country": "Ethiopia", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇪🇹", "code": "251"}, {"flagCode": "FK", "country": "Falkland Islands", "maxLength": 5, "minLength": 5, "phLength": 5, "flag": "🇫🇰", "code": "500"}, {"flagCode": "FO", "country": "Faroe Islands", "maxLength": 6, "minLength": 6, "phLength": 6, "flag": "🇫🇴", "code": "298"}, {"flagCode": "FJ", "country": "Fiji", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇫🇯", "code": "679"}, {"flagCode": "FI", "country": "Finland", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇫🇮", "code": "358"}, {"flagCode": "FR", "country": "France", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇫🇷", "code": "33"}, {"flagCode": "GF", "country": "French Guiana", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇬🇫", "code": "594"}, {"flagCode": "PF", "country": "French Polynesia", "maxLength": 6, "minLength": 6, "phLength": 6, "flag": "🇵🇫", "code": "689"}, {"flagCode": "GA", "country": "Gabon", "maxLength": 7, "minLength": 6, "phLength": "6 or 7", "flag": "🇬🇦", "code": "241"}, {"flagCode": "GM", "country": "Gambia", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇬🇲", "code": "220"}, {"flagCode": "GE", "country": "Georgia", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇬🇪", "code": "995"}, {"flagCode": "DE", "country": "Germany", "maxLength": 11, "minLength": 11, "phLength": "11", "flag": "🇩🇪", "code": "49"}, {"flagCode": "GH", "country": "Ghana", "maxLength": 9, "minLength": 5, "phLength": "5 to 9", "flag": "🇬🇭", "code": "233"}, {"flagCode": "GI", "country": "Gibraltar", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇬🇮", "code": "350"}, {"flagCode": "GR", "country": "Greece", "maxLength": 10, "minLength": 10, "phLength": 10, "flag": "🇬🇷", "code": "30"}, {"flagCode": "GL", "country": "Greenland", "maxLength": 6, "minLength": 6, "phLength": 6, "flag": "🇬🇱", "code": "299"}, {"flagCode": "GD", "country": "Grenada", "maxLength": 10, "minLength": 10, "phLength": "(473) + 7", "flag": "🇬🇩", "code": "1-473"}, {"flagCode": "GP", "country": "Guadeloupe", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇬🇵", "code": "590"}, {"flagCode": "GU", "country": "Guam", "maxLength": 10, "minLength": 10, "phLength": "(671) + 7", "flag": "🇬🇺", "code": "1-671"}, {"flagCode": "GT", "country": "Guatemala", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇬🇹", "code": "502"}, {"flagCode": "GN", "country": "Guinea", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇬🇳", "code": "224"}, {"flagCode": "GY", "country": "Guyana", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇬🇾", "code": "592"}, {"flagCode": "HT", "country": "Haiti", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇭🇹", "code": "509"}, {"flagCode": "HN", "country": "Honduras", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇭🇳", "code": "504"}, {"flagCode": "HK", "country": "Hong Kong", "maxLength": 9, "minLength": 4, "phLength": "4, 8 to 9", "flag": "🇭🇰", "code": "852"}, {"flagCode": "HU", "country": "Hungary", "maxLength": 9, "minLength": 8, "phLength": "8 to 9", "flag": "🇭🇺", "code": "36"}, {"flagCode": "IS", "country": "Iceland", "maxLength": 9, "minLength": 7, "phLength": "7 or 9", "flag": "🇮🇸", "code": "354"}, {"flagCode": "IN", "country": "India", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇮🇳", "code": "91"}, {"flagCode": "ID", "country": "Indonesia", "maxLength": 12, "minLength": 12, "phLength": "12", "flag": "🇮🇩", "code": "62"}, {"flagCode": "IR", "country": "Iran", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇮🇷", "code": "98"}, {"flagCode": "IQ", "country": "Iraq", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇮🇶", "code": "964"}, {"flagCode": "IE", "country": "Ireland", "maxLength": 11, "minLength": 7, "phLength": "7 to 11", "flag": "🇮🇪", "code": "353"}, {"flagCode": "IL", "country": "Israel", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇮🇱", "code": "972"}, {"flagCode": "IT", "country": "Italy", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇮🇹", "code": "39"}, {"flagCode": "CI", "country": "Ivory Coast", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇨🇮", "code": "225"}, {"flagCode": "JM", "country": "Jamaica", "maxLength": 10, "minLength": 10, "phLength": "(876) + 7", "flag": "🇯🇲", "code": "1-876"}, {"flagCode": "JP", "country": "Japan", "maxLength": 11, "minLength": 11, "phLength": "11", "flag": "🇯🇵", "code": "81"}, {"flagCode": "JO", "country": "Jordan", "maxLength": 9, "minLength": 5, "phLength": "5 to 9", "flag": "🇯🇴", "code": "962"}, {"flagCode": "KZ", "country": "Kazakhstan", "maxLength": 10, "minLength": 10, "phLength": 10, "flag": "🇰🇿", "code": "7"}, {"flagCode": "KE", "country": "Kenya", "maxLength": 10, "minLength": 6, "phLength": "6 to 10", "flag": "🇰🇪", "code": "254"}, {"flagCode": "KI", "country": "Kiribati", "maxLength": 5, "minLength": 5, "phLength": 5, "flag": "🇰🇮", "code": "686"}, {"flagCode": "KW", "country": "Kuwait", "maxLength": 8, "minLength": 7, "phLength": "7 or 8", "flag": "🇰🇼", "code": "965"}, {"flagCode": "KG", "country": "Kyrgyzstan", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇰🇬", "code": "996"}, {"flagCode": "LA", "country": "Laos", "maxLength": 10, "minLength": 8, "phLength": "8 to 10", "flag": "🇱🇦", "code": "856"}, {"flagCode": "LV", "country": "Latvia", "maxLength": 8, "minLength": 7, "phLength": "7 or 8", "flag": "🇱🇻", "code": "371"}, {"flagCode": "LB", "country": "Lebanon", "maxLength": 8, "minLength": 7, "phLength": "7 to 8", "flag": "🇱🇧", "code": "961"}, {"flagCode": "LS", "country": "Lesotho", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇱🇸", "code": "266"}, {"flagCode": "LR", "country": "Liberia", "maxLength": 8, "minLength": 7, "phLength": "7 to 8", "flag": "🇱🇷", "code": "231"}, {"flagCode": "LY", "country": "Libya", "maxLength": 9, "minLength": 8, "phLength": "8 to 9", "flag": "🇱🇾", "code": "218"}, {"flagCode": "LI", "country": "Liechtenstein", "maxLength": 9, "minLength": 7, "phLength": "7 to 9", "flag": "🇱🇮", "code": "423"}, {"flagCode": "LT", "country": "Lithuania", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇱🇹", "code": "370"}, {"flagCode": "LU", "country": "Luxembourg", "maxLength": 11, "minLength": 4, "phLength": "4 to 11", "flag": "🇱🇺", "code": "352"}, {"flagCode": "MO", "country": "Macau", "maxLength": 8, "minLength": 7, "phLength": "7 to 8", "flag": "🇲🇴", "code": "853"}, {"flagCode": "MG", "country": "Madagascar", "maxLength": 10, "minLength": 9, "phLength": "9 to 10", "flag": "🇲🇬", "code": "261"}, {"flagCode": "MW", "country": "Malawi", "maxLength": 8, "minLength": 7, "phLength": "7 or 8", "flag": "🇲🇼", "code": "265"}, {"flagCode": "MY", "country": "Malaysia", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇲🇾", "code": "60"}, {"flagCode": "MV", "country": "Maldives", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇲🇻", "code": "960"}, {"flagCode": "ML", "country": "Mali", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇲🇱", "code": "223"}, {"flagCode": "MT", "country": "Malta", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇲🇹", "code": "356"}, {"flagCode": "MH", "country": "Marshall Islands", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇲🇭", "code": "692"}, {"flagCode": "MQ", "country": "Martinique", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇲🇶", "code": "596"}, {"flagCode": "MR", "country": "Mauritania", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇲🇷", "code": "222"}, {"flagCode": "MU", "country": "Mauritius", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇲🇺", "code": "230"}, {"flagCode": "MX", "country": "Mexico", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇲🇽", "code": "52"}, {"flagCode": "FM", "country": "Micronesia", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇫🇲", "code": "691"}, {"flagCode": "MD", "country": "Moldova", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇲🇩", "code": "373"}, {"flagCode": "MC", "country": "Monaco", "maxLength": 9, "minLength": 5, "phLength": "5 to 9", "flag": "🇲🇨", "code": "377"}, {"flagCode": "MN", "country": "Mongolia", "maxLength": 8, "minLength": 7, "phLength": "7 to 8", "flag": "🇲🇳", "code": "976"}, {"flagCode": "ME", "country": "Montenegro", "maxLength": 12, "minLength": 4, "phLength": "4 to 12", "flag": "🇲🇪", "code": "382"}, {"flagCode": "MS", "country": "Montserrat", "maxLength": 10, "minLength": 10, "phLength": "(664) + 7", "flag": "🇲🇸", "code": "1-664"}, {"flagCode": "MA", "country": "Morocco", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇲🇦", "code": "212"}, {"flagCode": "MZ", "country": "Mozambique", "maxLength": 9, "minLength": 8, "phLength": "8 to 9", "flag": "🇲🇿", "code": "258"}, {"flagCode": "MM", "country": "Myanmar", "maxLength": 9, "minLength": 7, "phLength": "7 to 9", "flag": "🇲🇲", "code": "95"}, {"flagCode": "NA", "country": "Namibia", "maxLength": 10, "minLength": 6, "phLength": "6 to 10", "flag": "🇳🇦", "code": "264"}, {"flagCode": "NR", "country": "Nauru", "maxLength": 7, "minLength": 4, "phLength": "4 or 7", "flag": "🇳🇷", "code": "674"}, {"flagCode": "NP", "country": "Nepal", "maxLength": 9, "minLength": 8, "phLength": "8 to 9", "flag": "🇳🇵", "code": "977"}, {"flagCode": "NL", "country": "Netherlands", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇳🇱", "code": "31"}, {"flagCode": "NC", "country": "New Caledonia", "maxLength": 6, "minLength": 6, "phLength": 6, "flag": "🇳🇨", "code": "687"}, {"flagCode": "NZ", "country": "New Zealand", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇳🇿", "code": "64"}, {"flagCode": "NI", "country": "Nicaragua", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇳🇮", "code": "505"}, {"flagCode": "NE", "country": "Niger", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇳🇪", "code": "227"}, {"flagCode": "NG", "country": "Nigeria", "maxLength": 11, "minLength": 11, "phLength": "11", "flag": "🇳🇬", "code": "234"}, {"flagCode": "NU", "country": "Niue", "maxLength": 4, "minLength": 4, "phLength": 4, "flag": "🇳🇺", "code": "683"}, {"flagCode": "KP", "country": "North Korea", "maxLength": 17, "minLength": 6, "phLength": "6 to 17", "flag": "🇰🇵", "code": "850"}, {"flagCode": "NO", "country": "Norway", "maxLength": 8, "minLength": 8, "phLength": "8", "flag": "🇳🇴", "code": "47"}, {"flagCode": "OM", "country": "Oman", "maxLength": 8, "minLength": 7, "phLength": "7 to 8", "flag": "🇴🇲", "code": "968"}, {"flagCode": "PK", "country": "Pakistan", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇵🇰", "code": "92"}, {"flagCode": "PW", "country": "<PERSON><PERSON>", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇵🇼", "code": "680"}, {"flagCode": "PA", "country": "Panama", "maxLength": 8, "minLength": 7, "phLength": "7 or 8", "flag": "🇵🇦", "code": "507"}, {"flagCode": "PG", "country": "Papua New Guinea", "maxLength": 11, "minLength": 4, "phLength": "4 to 11", "flag": "🇵🇬", "code": "675"}, {"flagCode": "PY", "country": "Paraguay", "maxLength": 9, "minLength": 5, "phLength": "5 to 9", "flag": "🇵🇾", "code": "595"}, {"flagCode": "PE", "country": "Peru", "maxLength": 11, "minLength": 8, "phLength": "8 to 11", "flag": "🇵🇪", "code": "51"}, {"flagCode": "PH", "country": "Philippines", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇵🇭", "code": "63"}, {"flagCode": "PL", "country": "Poland", "maxLength": 9, "minLength": 6, "phLength": "6 to 9", "flag": "🇵🇱", "code": "48"}, {"flagCode": "PT", "country": "Portugal", "maxLength": 11, "minLength": 9, "phLength": "9 or 11", "flag": "🇵🇹", "code": "351"}, {"flagCode": "PR", "country": "Puerto Rico", "maxLength": 10, "minLength": 10, "phLength": "(787/939) + 7", "flag": "🇵🇷", "code": "1-787"}, {"flagCode": "QA", "country": "Qatar", "maxLength": 8, "minLength": 3, "phLength": "3 to 8", "flag": "🇶🇦", "code": "974"}, {"flagCode": "RO", "country": "Romania", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇷🇴", "code": "40"}, {"flagCode": "RU", "country": "Russia", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇷🇺", "code": "7"}, {"flagCode": "RW", "country": "Rwanda", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇷🇼", "code": "250"}, {"flagCode": "KN", "country": "Saint Kitts and Nevis", "maxLength": 10, "minLength": 10, "phLength": "(869) + 7", "flag": "🇰🇳", "code": "1-869"}, {"flagCode": "LC", "country": "Saint Lucia", "maxLength": 10, "minLength": 10, "phLength": "(758) + 7", "flag": "🇱🇨", "code": "1-758"}, {"flagCode": "PM", "country": "Saint Pierre and Miquelon", "maxLength": 6, "minLength": 6, "phLength": 6, "flag": "🇵🇲", "code": "508"}, {"flagCode": "VC", "country": "Saint Vincent and the Grenadines", "maxLength": 10, "minLength": 10, "phLength": "(784) + 7", "flag": "🇻🇨", "code": "1-784"}, {"flagCode": "WS", "country": "Samoa", "maxLength": 7, "minLength": 3, "phLength": "3 to 7", "flag": "🇼🇸", "code": "685"}, {"flagCode": "SM", "country": "San Marino", "maxLength": 10, "minLength": 6, "phLength": "6 to 10", "flag": "🇸🇲", "code": "378"}, {"flagCode": "ST", "country": "Sao Tome and Principe", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇸🇹", "code": "239"}, {"flagCode": "SA", "country": "Saudi Arabia", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇸🇦", "code": "966"}, {"flagCode": "SN", "country": "Senegal", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇸🇳", "code": "221"}, {"flagCode": "RS", "country": "Serbia", "maxLength": 12, "minLength": 4, "phLength": "4 to 12", "flag": "🇷🇸", "code": "381"}, {"flagCode": "SC", "country": "Seychelles", "maxLength": 7, "minLength": 7, "phLength": 7, "flag": "🇸🇨", "code": "248"}, {"flagCode": "SL", "country": "Sierra Leone", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇸🇱", "code": "232"}, {"flagCode": "SG", "country": "Singapore", "maxLength": 8, "minLength": 8, "phLength": "8", "flag": "🇸🇬", "code": "65"}, {"flagCode": "SX", "country": "Sint Maarten", "maxLength": 10, "minLength": 10, "phLength": "(721) + 7", "flag": "🇸🇽", "code": "1-721"}, {"flagCode": "SK", "country": "Slovakia", "maxLength": 9, "minLength": 4, "phLength": "4 to 9", "flag": "🇸🇰", "code": "421"}, {"flagCode": "SI", "country": "Slovenia", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇸🇮", "code": "386"}, {"flagCode": "SB", "country": "Solomon Islands", "maxLength": 5, "minLength": 5, "phLength": 5, "flag": "🇸🇧", "code": "677"}, {"flagCode": "SO", "country": "Somalia", "maxLength": 8, "minLength": 5, "phLength": "5 to 8", "flag": "🇸🇴", "code": "252"}, {"flagCode": "ZA", "country": "South Africa", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇿🇦", "code": "27"}, {"flagCode": "KR", "country": "South Korea", "maxLength": 11, "minLength": 11, "phLength": "11", "flag": "🇰🇷", "code": "82"}, {"flagCode": "ES", "country": "Spain", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇪🇸", "code": "34"}, {"flagCode": "LK", "country": "Sri Lanka", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇱🇰", "code": "94"}, {"flagCode": "SD", "country": "Sudan", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇸🇩", "code": "249"}, {"flagCode": "SR", "country": "Suriname", "maxLength": 7, "minLength": 6, "phLength": "6 to 7", "flag": "🇸🇷", "code": "597"}, {"flagCode": "SE", "country": "Sweden", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇸🇪", "code": "46"}, {"flagCode": "CH", "country": "Switzerland", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇨🇭", "code": "41"}, {"flagCode": "SY", "country": "Syria", "maxLength": 10, "minLength": 8, "phLength": "8 to 10", "flag": "🇸🇾", "code": "963"}, {"flagCode": "TW", "country": "Taiwan", "maxLength": 9, "minLength": 8, "phLength": "8 to 9", "flag": "🇹🇼", "code": "886"}, {"flagCode": "TJ", "country": "Tajikistan", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇹🇯", "code": "992"}, {"flagCode": "TZ", "country": "Tanzania", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇹🇿", "code": "255"}, {"flagCode": "TH", "country": "Thailand", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇹🇭", "code": "66"}, {"flagCode": "TG", "country": "Togo", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇹🇬", "code": "228"}, {"flagCode": "TK", "country": "Tokelau", "maxLength": 4, "minLength": 4, "phLength": 4, "flag": "🇹🇰", "code": "690"}, {"flagCode": "TO", "country": "Tonga", "maxLength": 7, "minLength": 5, "phLength": "5 or 7", "flag": "🇹🇴", "code": "676"}, {"flagCode": "TT", "country": "Trinidad and Tobago", "maxLength": 10, "minLength": 10, "phLength": "(868) + 7", "flag": "🇹🇹", "code": "1-868"}, {"flagCode": "TN", "country": "Tunisia", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇹🇳", "code": "216"}, {"flagCode": "TR", "country": "Turkey", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇹🇷", "code": "90"}, {"flagCode": "TM", "country": "Turkmenistan", "maxLength": 8, "minLength": 8, "phLength": 8, "flag": "🇹🇲", "code": "993"}, {"flagCode": "TC", "country": "Turks and Caicos Islands", "maxLength": 10, "minLength": 10, "phLength": "(649) + 7", "flag": "🇹🇨", "code": "1-649"}, {"flagCode": "TV", "country": "Tuvalu", "maxLength": 6, "minLength": 5, "phLength": "5 or 6", "flag": "🇹🇻", "code": "688"}, {"flagCode": "UG", "country": "Uganda", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇺🇬", "code": "256"}, {"flagCode": "UA", "country": "Ukraine", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇺🇦", "code": "380"}, {"flagCode": "AE", "country": "United Arab Emirates", "maxLength": 9, "minLength": 9, "phLength": "9", "flag": "🇦🇪", "code": "971"}, {"flagCode": "GB", "country": "United Kingdom", "maxLength": 11, "minLength": 11, "phLength": "11", "flag": "🇬🇧", "code": "44"}, {"flagCode": "US", "country": "United States", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇺🇸", "code": "1"}, {"flagCode": "VI", "country": "United States Virgin Islands", "maxLength": 10, "minLength": 10, "phLength": "(340) + 7", "flag": "🇻🇮", "code": "1-340"}, {"flagCode": "UY", "country": "Uruguay", "maxLength": 11, "minLength": 4, "phLength": "4 to 11", "flag": "🇺🇾", "code": "598"}, {"flagCode": "UZ", "country": "Uzbekistan", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇺🇿", "code": "998"}, {"flagCode": "VU", "country": "Vanuatu", "maxLength": 7, "minLength": 5, "phLength": "5 or 7", "flag": "🇻🇺", "code": "678"}, {"flagCode": "VA", "country": "Vatican City", "maxLength": 11, "minLength": null, "phLength": "up to 11", "flag": "🇻🇦", "code": "379"}, {"flagCode": "VE", "country": "Venezuela", "maxLength": 10, "minLength": 10, "phLength": 10, "flag": "🇻🇪", "code": "58"}, {"flagCode": "VN", "country": "Vietnam", "maxLength": 10, "minLength": 10, "phLength": "10", "flag": "🇻🇳", "code": "84"}, {"flagCode": "WF", "country": "Wallis and Futuna", "maxLength": 6, "minLength": 6, "phLength": 6, "flag": "🇼🇫", "code": "681"}, {"flagCode": "YE", "country": "Yemen", "maxLength": 9, "minLength": 6, "phLength": "6 to 9", "flag": "🇾🇪", "code": "967"}, {"flagCode": "ZM", "country": "Zambia", "maxLength": 9, "minLength": 9, "phLength": 9, "flag": "🇿🇲", "code": "260"}, {"flagCode": "ZW", "country": "Zimbabwe", "maxLength": 10, "minLength": 5, "phLength": "5 to 10", "flag": "🇿🇼", "code": "263"}]
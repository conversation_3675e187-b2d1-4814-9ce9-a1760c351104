# Tukxi Driver App

## Getting Started

This is a Flutter project designed for mobile application development. Follow the steps below to run the project and produce APK and App Bundle files.

### Prerequisites

Ensure you have the following installed:
- Flutter SDK
- Android Studio (or any IDE supporting Flutter)
- Android device or emulator

### Running the Project

1. Open a terminal and navigate to the project directory.
2. Run the following command to fetch dependencies:
   ```zsh
   flutter pub get
   ```
3. Connect an Android device or start an emulator.
4. Run the app using:
   ```zsh
   flutter run
   ```

### Producing APK and App Bundle Files

To generate APK and App Bundle files for distribution:
1. In the `key.properties` file, ensure the `storeFile` property points to the
   absolute path of the `release-key.jks` file avaiable at `android/release-key.jks` on your local machine.

2. Build the APK:
   ```zsh
   flutter build apk
   ```
   The APK file will be located in the `build/app/outputs/flutter-apk/` directory.

3. Build the App Bundle:
   ```zsh
   flutter build appbundle
   ```
   The App Bundle file will be located in the `build/app/outputs/bundle/release/` directory.

### Notes

- Ensure all dependencies are resolved before building.
- For production builds, consider enabling code obfuscation and minification.



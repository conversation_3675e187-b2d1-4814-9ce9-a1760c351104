import 'package:flutter_test/flutter_test.dart';
import 'package:tukxi_driver/core/utils/file_utils.dart';

void main() {
  group('FileUtils Tests', () {
    test('should validate supported extensions', () {
      expect(FileUtils.supportedExtensions, contains('pdf'));
      expect(FileUtils.supportedExtensions, contains('jpg'));
      expect(FileUtils.supportedExtensions, contains('png'));
      expect(FileUtils.supportedExtensions, contains('webp'));
      expect(FileUtils.supportedExtensions, contains('jpeg'));
    });

    test('should extract file extension correctly', () {
      // Note: These tests use mock file paths since we can't create real files in tests
      final testPath = '/path/to/test.pdf';
      expect(testPath.split('.').last.toLowerCase(), equals('pdf'));

      final imagePath = '/path/to/image.jpg';
      expect(imagePath.split('.').last.toLowerCase(), equals('jpg'));
    });

    test('should identify MIME types correctly', () {
      expect(FileUtils.mimeTypes['pdf'], equals('application/pdf'));
      expect(FileUtils.mimeTypes['jpg'], equals('image/jpeg'));
      expect(FileUtils.mimeTypes['png'], equals('image/png'));
      expect(FileUtils.mimeTypes['webp'], equals('image/webp'));
    });

    test('should format file sizes correctly', () {
      // Test file size formatting logic
      const bytesIn1KB = 1024;
      const bytesIn1MB = 1024 * 1024;

      expect(bytesIn1KB < 1024 * 1024, isTrue); // Should be KB
      expect(bytesIn1MB >= 1024 * 1024, isTrue); // Should be MB
    });

    test('should validate file extensions', () {
      final supportedExtensions = FileUtils.supportedExtensions;

      expect(supportedExtensions.contains('pdf'), isTrue);
      expect(supportedExtensions.contains('txt'), isFalse);
      expect(supportedExtensions.contains('doc'), isFalse);
    });

    test('should identify image vs PDF files', () {
      const imageExtensions = ['jpg', 'jpeg', 'png', 'webp'];
      const pdfExtension = 'pdf';

      for (String ext in imageExtensions) {
        expect(imageExtensions.contains(ext), isTrue);
        expect(ext != pdfExtension, isTrue);
      }

      expect(imageExtensions.contains(pdfExtension), isFalse);
    });
  });
}

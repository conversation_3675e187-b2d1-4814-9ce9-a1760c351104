import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class BuildField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final TextInputType keyboardType;
  final bool isMandetory;
  final bool isCapitalize;
  final Function? onChanged;
  final bool isError;
  final String? errorText;
  final String? hintText;
  final int? maxLength;

  const BuildField({
    super.key,
    required this.label,
    required this.controller,
    this.keyboardType = TextInputType.text,
    this.isMandetory = false,
    this.isCapitalize = false,
    this.onChanged,
    this.isError = false,
    this.errorText,
    this.hintText,
    this.maxLength,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color:
                    (isError && errorText != null && controller.text.isNotEmpty)
                    ? Colors.red
                    : Colors.black,
              ),
            ),
            if (isMandetory) Text(' *', style: TextStyle(color: Colors.red)),
          ],
        ),
        const SizedBox(height: 5),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(
              context,
            ).colorScheme.secondary.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.1)),
          ),
          padding: const EdgeInsets.only(left: 16),
          child: TextField(
            controller: controller,
            maxLength: maxLength,
            keyboardType: keyboardType,
            inputFormatters: [
              if (keyboardType == TextInputType.number)
                FilteringTextInputFormatter.digitsOnly,
            ],
            textCapitalization: isCapitalize
                ? TextCapitalization.words
                : TextCapitalization.none,
            onChanged: onChanged != null ? (value) => onChanged!(value) : null,
            decoration: InputDecoration(
              border: InputBorder.none,
              counter: Container(),
            ),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        if (isError && errorText != null && controller.text.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Text(
              errorText!,
              style: const TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
      ],
    );
  }
}

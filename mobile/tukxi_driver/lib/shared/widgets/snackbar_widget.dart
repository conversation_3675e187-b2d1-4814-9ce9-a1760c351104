import 'package:flutter/material.dart';

class SnackbarWidget {
  BuildContext context;
  SnackbarWidget(this.context);
  void show(String title, bool isError) {
    final snackBar = SnackBar(
      content: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: Colors.white),
      ),
      backgroundColor: isError ? Colors.red : Colors.green,
      duration: const Duration(seconds: 3),
      elevation: 1,
      margin: EdgeInsets.all(16.0),
      behavior: SnackBarBehavior.floating,
      dismissDirection: DismissDirection.horizontal,
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}

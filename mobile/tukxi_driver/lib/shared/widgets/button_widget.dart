import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class ButtonWidget extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color? color;
  final Color? textColor;
  final double? fontSize;
  final double? width;
  final double? height;
  final double? borderRadius;
  final double? paddingHorizontal;
  final double? paddingVertical;
  final Icon? icon;
  final bool isLoading;

  const ButtonWidget({
    super.key,
    required this.text,
    required this.onPressed,
    required this.isLoading,
    this.color,
    this.textColor,
    this.fontSize,
    this.width,
    this.height,
    this.borderRadius,
    this.paddingHorizontal,
    this.paddingVertical,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: width,
        height: height,
        padding: EdgeInsets.symmetric(
          horizontal: paddingHorizontal ?? 0,
          vertical: paddingVertical ?? 0,
        ),
        decoration: BoxDecoration(
          color: color ?? const Color(0xFF4361EE),
          borderRadius: BorderRadius.circular(borderRadius ?? 8),
        ),
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            isLoading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        textColor ?? Colors.white,
                      ),
                    ),
                  )
                : Text(
                    text,
                    style: GoogleFonts.inter(
                      color: textColor ?? Colors.white,
                      fontSize: fontSize ?? 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
            if (icon != null) ...[const SizedBox(width: 8), icon!],
          ],
        ),
      ),
    );
  }
}

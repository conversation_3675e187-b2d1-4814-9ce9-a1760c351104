import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tukxi_driver/core/constants/app_config.dart';
import 'package:tukxi_driver/features/authentication/presentation/viewmodels/auth_provider.dart';
import 'package:http/http.dart' as http;
import 'package:tukxi_driver/shared/widgets/snackbar_widget.dart';

class UserProfile {
  BuildContext context;
  late var header;
  UserProfile(this.context) {
    header = {
      'Content-Type': 'application/json',
      'Authorization':
          'Bearer ${Provider.of<AuthProvider>(context, listen: false).token}',
      'x-app-type': 'driver',
    };
  }

  Future<Map<String, dynamic>> getUserProfile() async {
    String url = '${AppConfig.apiBaseUrl}/api/v1/user-profile';
    var response = await http.get(Uri.parse(url), headers: header);
    log(response.body, name: 'UserProfile.getUserProfile');
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      log('Error: ${response.statusCode}', name: 'UserProfile.getUserProfile');
      SnackbarWidget(context).show(jsonDecode(response.body)['message'], true);
      return {};
    }
  }
}

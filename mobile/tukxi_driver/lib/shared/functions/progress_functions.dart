import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tukxi_driver/core/constants/app_config.dart';
import 'package:tukxi_driver/features/authentication/presentation/viewmodels/auth_provider.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class ProgressFunctions {
  BuildContext context;
  late var header;
  ProgressFunctions(this.context) {
    header = {
      'Content-Type': 'application/json',
      'Authorization':
          'Bearer ${Provider.of<AuthProvider>(context, listen: false).token}',
    };
  }
  Future<String> getProgress() async {
    String url =
        '${AppConfig.apiBaseUrl}/api/v1/user-profile/onboarding-status';
    var headerNew = header;
    headerNew['x-app-type'] = 'driver';
    var response = await http.get(Uri.parse(url), headers: headerNew);
    log(response.body, name: 'Progress Response');
    if (response.statusCode == 200 || response.statusCode == 201) {
      var data = json.decode(response.body)['data'];
      if (data['currentStep'] == 'PHONE_VERIFICATION') {
        return '/choose-language';
      } else if (data['currentStep'] == 'LANGUAGE_UPDATE') {
        return '/personal-details';
      } else if (data['currentStep'] == 'PROFILE_SETUP') {
        return '/select-vehicle';
      } else if (data['currentStep'] == 'VEHICLE_REGISTRATION') {
        return '/vehicle-verification';
      } else if (data['currentStep'] == 'VEHICLE_DOCUMENTS_VERIFICATION') {
        return '/kyc-verification';
      } else {
        return '/login';
      }
    } else {
      throw Exception('Failed to load progress');
    }
  }
}

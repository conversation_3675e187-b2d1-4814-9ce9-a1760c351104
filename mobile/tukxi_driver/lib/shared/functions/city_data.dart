import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tukxi_driver/core/constants/app_config.dart';
import 'package:http/http.dart' as http;
import 'package:tukxi_driver/features/authentication/presentation/viewmodels/auth_provider.dart';
import 'package:tukxi_driver/shared/functions/user_profile.dart';
import 'package:tukxi_driver/shared/widgets/snackbar_widget.dart';

class CityData {
  BuildContext context;
  late var header;
  CityData(this.context) {
    header = {
      'Content-Type': 'application/json',
      'Authorization':
          'Bearer ${Provider.of<AuthProvider>(context, listen: false).token}',
    };
  }
  Future<List<Map<String, dynamic>>> fetchCities() async {
    String url = '${AppConfig.apiBaseUrl}/api/v1/cities';
    var response = await http.get(Uri.parse(url), headers: header);
    log(response.body, name: 'CityData.fetchCities');
    if (response.statusCode == 200) {
      var data = jsonDecode(response.body)['data'];
      return List<Map<String, dynamic>>.from(data);
    } else {
      throw Exception('Failed to load cities');
    }
  }

  Future<List<Map<String, dynamic>>> getCityVehicle() async {
    var userProfile = await UserProfile(context).getUserProfile();
    log(userProfile['cityId'].toString(), name: 'UserProfile.cityId');
    String url =
        '${AppConfig.apiBaseUrl}/api/v1/cities/${userProfile['cityId']}/vehicles';
    var response = await http.get(Uri.parse(url), headers: header);
    log(response.body, name: 'CityData.searchCities');
    if (response.statusCode == 200) {
      var data = jsonDecode(response.body)['data'];
      return List<Map<String, dynamic>>.from(data);
    } else {
      SnackbarWidget(context).show(jsonDecode(response.body)['message'], true);
      log('Error: ${response.statusCode}', name: 'CityData.searchCities');
      return [];
    }
  }
}

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:tukxi_driver/core/constants/app_config.dart';
import 'package:tukxi_driver/features/authentication/presentation/viewmodels/auth_provider.dart';

class GetLanguages {
  BuildContext context;
  GetLanguages(this.context);
  Future<List<Map<String, dynamic>>> fetchLanguages() async {
    String url = '${AppConfig.apiBaseUrl}/api/v1/languages';
    var response = await http.get(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
        'Authorization':
            'Bearer ${Provider.of<AuthProvider>(context, listen: false).token}',
      },
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      List<Map<String, dynamic>> languages = [];
      var data = json.decode(response.body)['data'];
      for (var item in data) {
        languages.add({
          'name': item['name'],
          'example': item['nameInNative'],
          'id': item['id'],
        });
      }
      return languages;
    } else {
      throw Exception('Failed to load languages');
    }
  }
}

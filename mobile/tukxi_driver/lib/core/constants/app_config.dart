//App Configuration for Tukxi Driver App
// This file contains the configuration settings for the Tukxi Driver application.
import 'dart:developer';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:tukxi_driver/core/constants/app_constants.dart';

class AppConfig {
  static Future<void> initialize() async {
    try {
      await dotenv.load(fileName: '.env');
    } catch (e) {
      log('Error loading .env file: $e');
    }
  }

  static String get apiBaseUrl => AppConstants().url;

  static String get googleMapsApiKey => dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';

  static String get engagespotApiKey => dotenv.env['ENGAGESPOT_API_KEY'] ?? '';
}

import 'package:flutter/material.dart';
import 'package:tukxi_driver/features/authentication/data/models/country_model.dart';
import 'package:tukxi_driver/features/onboarding/presentation/pages/choose_language.dart';
import 'package:tukxi_driver/features/authentication/presentation/pages/country_picker.dart';
import 'package:tukxi_driver/features/onboarding/presentation/pages/kyc_status.dart';
import 'package:tukxi_driver/features/onboarding/presentation/pages/kyc_verification.dart';
import 'package:tukxi_driver/features/authentication/presentation/pages/login.dart';
import 'package:tukxi_driver/features/onboarding/presentation/pages/personal_details.dart';
import 'package:tukxi_driver/features/onboarding/presentation/pages/profile_photo.dart';
import 'package:tukxi_driver/features/onboarding/presentation/pages/select_vehicle.dart';
import 'package:tukxi_driver/features/onboarding/presentation/pages/vehicle_verification.dart';
import 'package:tukxi_driver/features/onboarding/presentation/pages/verification_status.dart';
import 'package:tukxi_driver/features/authentication/presentation/pages/verify_otp.dart';
import 'package:tukxi_driver/features/init/splashscreen.dart';

class AppRoutes {
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/':
        return buildPageRoute(const Splashscreen(), settings, fade: true);
      case '/login':
        return buildPageRoute(const Login(), settings);
      case '/select-country':
        if (settings.arguments is Country) {
          return buildPageRoute(
            CountrySelectionScreen(
              selectedCountry: settings.arguments as Country,
            ),
            settings,
          );
        }
        return null;
      case '/otp-verification':
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          return buildPageRoute(
            VerifyOtp(phone: args['phone'] ?? '', country: args['country']),
            settings,
          );
        }
        return null;
      case '/choose-language':
        return buildPageRoute(const ChooseLanguage(), settings);
      case '/personal-details':
        return buildPageRoute(const PersonalDetails(), settings);
      case '/select-vehicle':
        return buildPageRoute(const SelectVehicle(), settings);
      case '/vehicle-verification':
        return buildPageRoute(const VehicleVerification(), settings);
      case '/kyc-verification':
        return buildPageRoute(const KycVerification(), settings);
      case '/kyc-status':
        return buildPageRoute(const KycStatus(), settings);
      case '/profile-photo':
        return buildPageRoute(const ProfilePhoto(), settings);
      case '/verification-status':
        return buildPageRoute(const VerificationStatus(), settings);
      default:
        return null;
    }
  }

  static PageRouteBuilder buildPageRoute(
    Widget page,
    RouteSettings settings, {
    bool fade = false,
  }) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        if (fade) {
          return FadeTransition(opacity: animation, child: child);
        } else {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(
            begin: begin,
            end: end,
          ).chain(CurveTween(curve: curve));
          var offsetAnimation = animation.drive(tween);

          return SlideTransition(position: offsetAnimation, child: child);
        }
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class AppleAuthServices {
  Future<String?> signInWithApple() async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: 'com.bastet.app-service',
          redirectUri: Uri.parse(
            '',
          ),
        ),
      );

      debugPrint('Apple ID Credential: $credential');
      debugPrint('Identity Token: ${credential.identityToken}');
      debugPrint('Authorization Code: ${credential.authorizationCode}');
      debugPrint('Email: ${credential.email}');
      debugPrint('Given Name: ${credential.givenName}');
      debugPrint('Family Name: ${credential.familyName}');
      debugPrint('User Identifier: ${credential.userIdentifier}');

      if (credential.identityToken != null) {
        return credential.identityToken;
      } else {
        debugPrint('Apple Sign-In failed: Identity Token is null.');
        return null;
      }
    } catch (e) {
      debugPrint('Apple Sign-In failed: $e');

      if (e is SignInWithAppleAuthorizationException) {
        debugPrint('Authorization Exception: ${e.code} - ${e.message}');
      }
      return null;
    }
  }
}

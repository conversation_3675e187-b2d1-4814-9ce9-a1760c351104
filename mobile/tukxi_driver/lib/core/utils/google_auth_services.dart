import 'dart:developer';
import 'dart:io';

import 'package:google_sign_in/google_sign_in.dart';

class GoogleAuthService {
  // Use the singleton instance of GoogleSignIn
  static final GoogleSignIn _googleSignIn = GoogleSignIn.instance;

  static bool _isInitialized = false;

  /// Initialize GoogleSignIn (must be called before any other methods)
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await _googleSignIn.initialize(
        clientId: Platform.isAndroid ? '' : '',
        serverClientId: '',
      );
      _isInitialized = true;
    }
  }

  /// Sign in with Google and return ID token if successful, empty string if failed
  static Future<String> signInWithGoogle() async {
    try {
      // Ensure GoogleSignIn is initialized
      await _ensureInitialized();

      // Check if platform supports authenticate method
      if (!_googleSignIn.supportsAuthenticate()) {
        log('Google Sign In not supported on this platform');
        return '';
      }

      // Trigger the authentication flow
      final GoogleSignInAccount result = await _googleSignIn.authenticate();

      // Get the authentication data which contains the ID token
      final GoogleSignInAuthentication auth = result.authentication;

      // Return the ID token if available, otherwise empty string
      return auth.idToken ?? '';
    } catch (e) {
      // Log error if needed for debugging
      log('Google Sign In Error: $e');
      return '';
    }
  }

  /// Sign out from Google
  static Future<void> signOut() async {
    try {
      await _ensureInitialized();
      await _googleSignIn.disconnect();
    } catch (e) {
      log('Google Sign Out Error: $e');
    }
  }

  /// Disconnect from Google (revokes access)
  static Future<void> disconnect() async {
    try {
      await _ensureInitialized();
      await _googleSignIn.disconnect();
    } catch (e) {
      log('Google Disconnect Error: $e');
    }
  }

  /// Check if user is currently signed in
  static Future<bool> isSignedIn() async {
    try {
      await _ensureInitialized();
      // Try to get current user info to check if signed in
      final GoogleSignInAccount? result = await _googleSignIn
          .attemptLightweightAuthentication();
      return result != null;
    } catch (e) {
      log('Error checking sign in status: $e');
      return false;
    }
  }

  /// Get current signed in user (if any)
  static Future<GoogleSignInAccount?> getCurrentUser() async {
    try {
      await _ensureInitialized();
      final GoogleSignInAccount? result = await _googleSignIn
          .attemptLightweightAuthentication();
      return result;
    } catch (e) {
      log('Error getting current user: $e');
      return null;
    }
  }

  /// Get access token for additional scopes
  static Future<String> getAccessTokenForScopes(List<String> scopes) async {
    try {
      await _ensureInitialized();

      final GoogleSignInAccount? user = await getCurrentUser();
      if (user == null) {
        log('No signed in user found');
        return '';
      }

      final GoogleSignInClientAuthorization? authorization = await user
          .authorizationClient
          .authorizationForScopes(scopes);

      return authorization?.accessToken ?? '';
    } catch (e) {
      log('Error getting access token: $e');
      return '';
    }
  }
}

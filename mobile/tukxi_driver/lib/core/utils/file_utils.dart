import 'dart:io';

/// Utility class for file handling and validation
class FileUtils {
  // Supported file extensions
  static const List<String> supportedExtensions = [
    'pdf',
    'jpg',
    'jpeg',
    'png',
    'webp',
  ];

  // Supported MIME types
  static const Map<String, String> mimeTypes = {
    'pdf': 'application/pdf',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'webp': 'image/webp',
  };

  /// Get the MIME type for a file based on its extension
  static String? getMimeType(File file) {
    final extension = getFileExtension(file);
    return mimeTypes[extension];
  }

  /// Get file extension from file path
  static String getFileExtension(File file) {
    return file.path.split('.').last.toLowerCase();
  }

  /// Get file name without path
  static String getFileName(File file) {
    return file.path
        .split('/')
        .last
        .substring(0, file.path.split('/').last.length);
  }

  /// Get file name without extension
  static String getFileNameWithoutExtension(File file) {
    final fileName = getFileName(file);
    final lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex > 0) {
      return fileName.substring(0, lastDotIndex);
    }
    return fileName;
  }

  /// Check if file exists
  static bool fileExists(File file) {
    return file.existsSync();
  }

  /// Get file size in bytes
  static int getFileSizeInBytes(File file) {
    return file.lengthSync();
  }

  /// Get file size in MB
  static double getFileSizeInMB(File file) {
    return getFileSizeInBytes(file) / (1024 * 1024);
  }

  /// Get file size in KB
  static double getFileSizeInKB(File file) {
    return getFileSizeInBytes(file) / 1024;
  }

  /// Format file size for display
  static String formatFileSize(File file) {
    final sizeInBytes = getFileSizeInBytes(file);

    if (sizeInBytes < 1024) {
      return '$sizeInBytes B';
    } else if (sizeInBytes < 1024 * 1024) {
      return '${(sizeInBytes / 1024).toStringAsFixed(2)} KB';
    } else {
      return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    }
  }

  /// Validate file extension
  static bool isValidExtension(File file) {
    final extension = getFileExtension(file);
    return supportedExtensions.contains(extension);
  }

  /// Validate file size (returns true if file is under the limit)
  static bool isValidSize(File file, double maxSizeInMB) {
    return getFileSizeInMB(file) <= maxSizeInMB;
  }

  /// Check if file is an image
  static bool isImage(File file) {
    final extension = getFileExtension(file);
    return ['jpg', 'jpeg', 'png', 'webp'].contains(extension);
  }

  /// Check if file is a PDF
  static bool isPdf(File file) {
    final extension = getFileExtension(file);
    return extension == 'pdf';
  }

  /// Get file icon based on extension
  static String getFileIcon(File file) {
    final extension = getFileExtension(file);

    switch (extension) {
      case 'pdf':
        return '📄';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'webp':
        return '🖼️';
      default:
        return '📁';
    }
  }

  /// Validate file (combines extension and size validation)
  static FileValidationResult validateFile(File file, {double? maxSizeInMB}) {
    final errors = <String>[];

    // Check if file exists
    if (!fileExists(file)) {
      errors.add('File does not exist');
    }

    // Check extension
    if (!isValidExtension(file)) {
      errors.add(
        'Invalid file format. Supported formats: ${supportedExtensions.join(', ')}',
      );
    }

    // Check size if limit is provided
    if (maxSizeInMB != null && !isValidSize(file, maxSizeInMB)) {
      errors.add('File size exceeds limit of ${maxSizeInMB}MB');
    }

    return FileValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      file: file,
    );
  }
}

/// Result of file validation
class FileValidationResult {
  final bool isValid;
  final List<String> errors;
  final File file;

  const FileValidationResult({
    required this.isValid,
    required this.errors,
    required this.file,
  });

  /// Get first error message if any
  String? get firstError => errors.isNotEmpty ? errors.first : null;

  /// Get all error messages as a single string
  String get allErrors => errors.join(', ');
}

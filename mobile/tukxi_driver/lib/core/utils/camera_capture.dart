import 'dart:io';
import 'package:image_picker/image_picker.dart';

class CameraCapture {
  final ImagePicker _picker = ImagePicker();

  Future<File?> capturePhoto() async {
    try {
      // Open the camera and capture a photo
      final XFile? photo = await _picker.pickImage(source: ImageSource.camera);

      // Return the captured photo as a File
      if (photo != null) {
        return File(photo.path);
      }
    } catch (e) {
      print("Error capturing photo: $e");
    }
    return null;
  }
}

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'file_utils.dart';

class FileSelector {
  Future<File?> selectFile(BuildContext context, {double? maxSizeInMB}) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: FileUtils.supportedExtensions,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        final validation = FileUtils.validateFile(
          file,
          maxSizeInMB: maxSizeInMB,
        );

        if (!validation.isValid) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'File validation failed: ${validation.allErrors}',
                ),
                backgroundColor: Colors.orange,
              ),
            );
          }
          return null;
        }

        return file;
      }

      return null;
    } catch (e) {
      debugPrint('Error selecting file: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error selecting file: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      return null;
    }
  }

  Future<List<File>> selectMultipleFiles(
    BuildContext context, {
    double? maxSizeInMB,
  }) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: FileUtils.supportedExtensions,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        List<File> selectedFiles = [];
        List<String> invalidFiles = [];

        for (PlatformFile platformFile in result.files) {
          if (platformFile.path != null) {
            final file = File(platformFile.path!);
            final validation = FileUtils.validateFile(
              file,
              maxSizeInMB: maxSizeInMB,
            );

            if (validation.isValid) {
              selectedFiles.add(file);
            } else {
              invalidFiles.add(
                '${FileUtils.getFileName(file)}: ${validation.allErrors}',
              );
            }
          }
        }

        if (invalidFiles.isNotEmpty && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Some files were invalid: ${invalidFiles.join(', ')}',
              ),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 5),
            ),
          );
        }

        return selectedFiles;
      }

      return [];
    } catch (e) {
      debugPrint('Error selecting files: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error selecting files: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      return [];
    }
  }

  Map<String, dynamic> getFileInfo(File file) {
    return {
      'name': FileUtils.getFileName(file),
      'nameWithoutExtension': FileUtils.getFileNameWithoutExtension(file),
      'extension': FileUtils.getFileExtension(file),
      'sizeInBytes': FileUtils.getFileSizeInBytes(file),
      'sizeInKB': FileUtils.getFileSizeInKB(file).toStringAsFixed(2),
      'sizeInMB': FileUtils.getFileSizeInMB(file).toStringAsFixed(2),
      'formattedSize': FileUtils.formatFileSize(file),
      'path': file.path,
      'mimeType': FileUtils.getMimeType(file),
      'icon': FileUtils.getFileIcon(file),
      'isImage': FileUtils.isImage(file),
      'isPdf': FileUtils.isPdf(file),
    };
  }

  bool isValidFileFormat(File file) {
    return FileUtils.isValidExtension(file);
  }

  bool isValidFileSize(File file, double maxSizeInMB) {
    return FileUtils.isValidSize(file, maxSizeInMB);
  }

  FileValidationResult validateFile(File file, {double? maxSizeInMB}) {
    return FileUtils.validateFile(file, maxSizeInMB: maxSizeInMB);
  }
}

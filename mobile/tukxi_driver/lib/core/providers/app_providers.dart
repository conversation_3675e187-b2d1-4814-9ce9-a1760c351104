import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:tukxi_driver/features/authentication/presentation/viewmodels/auth_provider.dart';

class AppProviders {
  static List<SingleChildWidget> providers = [
    ChangeNotifierProvider<AuthProvider>(create: (_) => AuthProvider()),
  ];

  /// Helper method to get specific provider instances
  static T getProvider<T>(context) {
    return Provider.of<T>(context, listen: false);
  }

  /// Helper method to watch provider changes
  static T watchProvider<T>(context) {
    return Provider.of<T>(context);
  }
}

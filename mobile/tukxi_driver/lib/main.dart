import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tukxi_driver/core/constants/app_theme.dart';
import 'package:tukxi_driver/core/routes/app_routes.dart';
import 'package:tukxi_driver/core/constants/app_config.dart';
import 'package:tukxi_driver/core/providers/app_providers.dart';

late List<CameraDescription> cameras;
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppConfig.initialize();
  cameras = await availableCameras();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(390, 844),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: AppProviders.providers,
          child: MaterialApp(
            title: 'Tukxi Driver',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            onGenerateRoute: AppRoutes.onGenerateRoute,
            initialRoute: '/',
            // Add performance optimizations
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(
                  context,
                ).copyWith(textScaler: TextScaler.linear(1.0)),
                child: child!,
              );
            },
            // Disable unnecessary debug features in release mode
            showPerformanceOverlay: false,
            showSemanticsDebugger: false,
          ),
        );
      },
    );
  }
}

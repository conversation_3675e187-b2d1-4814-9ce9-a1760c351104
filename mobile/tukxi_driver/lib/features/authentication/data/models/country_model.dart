class Country {
  final String flagCode;
  final String country;
  final int maxLength;
  final int minLength;
  final String phLength;
  final String flag;
  final String code;

  Country({
    required this.flagCode,
    required this.country,
    required this.maxLength,
    required this.minLength,
    required this.phLength,
    required this.flag,
    required this.code,
  });

  factory Country.fromJson(Map<String, dynamic> json) {
    return Country(
      flagCode: json['flagCode'] ?? '',
      country: json['country'] ?? '',
      maxLength: json['maxLength'] ?? 15,
      minLength: json['minLength'] ?? 1,
      phLength: json['phLength']?.toString() ?? '',
      flag: json['flag'] ?? '',
      code: json['code'] ?? '',
    );
  }
}

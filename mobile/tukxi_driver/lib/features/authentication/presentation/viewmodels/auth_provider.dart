import 'package:flutter/material.dart';

class AuthProvider extends ChangeNotifier {
  String token = '';
  String refreshToken = '';

  void setToken(String newToken) async {
    token = newToken;
    notifyListeners();
  }

  void setRefreshToken(String refreshToken) async {
    this.refreshToken = refreshToken;
    notifyListeners();
  }

  void clearToken() async {
    token = '';
    refreshToken = '';
    notifyListeners();
  }
}

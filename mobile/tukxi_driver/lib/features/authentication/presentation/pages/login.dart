import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sim_card_code/sim_card_code.dart';
import 'package:tukxi_driver/features/authentication/data/models/country_model.dart';
import 'package:tukxi_driver/features/authentication/functions/auth_functions.dart';
import 'package:tukxi_driver/features/authentication/functions/social_auth.dart';
import 'package:tukxi_driver/features/authentication/presentation/widgets/social_icons.dart';
import 'package:tukxi_driver/shared/widgets/button_widget.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  bool _hasError = false;
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _phoneFocusNode = FocusNode();
  Country? _selectedCountry;
  List<Country> countries = [];
  String errorMessage = '';
  bool isLoading = false;

  Future<String?> getSimCountryCode() async {
    try {
      final String? countryCode = await SimCardManager.simCountryCode;
      log('SIM Country Code: $countryCode');
      return countryCode;
    } catch (e) {
      log('Error getting SIM country code: $e');
      return null;
    }
  }

  void getCountries() async {
    try {
      final String response = await rootBundle.loadString(
        'assets/countries.json',
      );
      final List<dynamic> data = json.decode(response);

      setState(() {
        countries = data
            .where(
              (json) =>
                  json['country'] != null &&
                  json['flagCode'] != null &&
                  json['flag'] != null &&
                  json['code'] != null &&
                  json['country'].toString().isNotEmpty &&
                  json['flagCode'].toString().isNotEmpty,
            )
            .map((json) => Country.fromJson(json))
            .toList();
        log('Countries loaded: ${countries.length}');
      });
      final String? simCountryCode = await getSimCountryCode();

      setState(() {
        if (countries.isNotEmpty) {
          if (simCountryCode != null) {
            final simCountry = countries
                .where(
                  (country) =>
                      country.flagCode.toUpperCase() ==
                      simCountryCode.toUpperCase(),
                )
                .firstOrNull;

            if (simCountry != null) {
              _selectedCountry = simCountry;
              log('Selected country from SIM: ${simCountry.country}');
            } else {
              _selectedCountry = countries.firstWhere(
                (country) => country.flagCode == 'IN',
                orElse: () => countries.first,
              );
              log('SIM country not found, defaulting to India');
            }
          } else {
            _selectedCountry = countries.firstWhere(
              (country) => country.flagCode == 'IN',
              orElse: () => countries.first,
            );
            log('SIM country code is null, defaulting to India');
          }
        }
      });
    } catch (e) {
      log('Error loading countries: $e');
    }
  }

  bool validatePhone() {
    if (_phoneController.text.isEmpty) {
      setState(() {
        _hasError = true;
        errorMessage = 'Please enter your mobile number';
      });
      return false;
    }

    final String phoneNumber = _phoneController.text.trim();
    final int minLength = _selectedCountry?.minLength ?? 1;
    final int maxLength = _selectedCountry?.maxLength ?? 15;
    final String countryName = _selectedCountry?.country ?? 'selected country';

    if (phoneNumber.length < minLength) {
      setState(() {
        _hasError = true;
        errorMessage =
            'Mobile number must be at least $minLength digits for $countryName';
      });
      return false;
    }

    if (phoneNumber.length > maxLength) {
      setState(() {
        _hasError = true;
        errorMessage =
            'Mobile number cannot exceed $maxLength digits for $countryName';
      });
      return false;
    }

    setState(() {
      _hasError = false;
      errorMessage = '';
    });
    return true;
  }

  @override
  void initState() {
    super.initState();
    getCountries();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Positioned(
                top: 0,
                child: Image.asset(
                  'assets/sign-in-bg.png',
                  width: MediaQuery.of(context).size.width,
                  fit: BoxFit.cover,
                ),
              )
              .animate(delay: Duration(milliseconds: 100))
              .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
              .slideX(
                begin: 0.3,
                end: 0,
                duration: 600.ms,
                curve: Curves.easeInOut,
              ),
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              height: 40,
              child: Image.asset('assets/logo-text.png', width: 20),
            ),
          ),
          SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).size.height * 0.58,
              ),
              child: Container(
                padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child:
                    Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Text(
                              'Enter your mobile number',
                              style: Theme.of(context).textTheme.headlineLarge,
                            ),
                            const SizedBox(height: 24),
                            Container(
                              decoration: BoxDecoration(
                                color: const Color(0xFFF5F5F5),
                                borderRadius: BorderRadius.circular(12),
                                border: _hasError
                                    ? Border.all(color: Colors.red, width: 1)
                                    : null,
                              ),
                              child: Row(
                                children: [
                                  GestureDetector(
                                    onTap: () async {
                                      if (_selectedCountry != null) {
                                        final Country? selectedCountry =
                                            await Navigator.pushNamed(
                                                  context,
                                                  '/select-country',
                                                  arguments: _selectedCountry,
                                                )
                                                as Country?;

                                        if (selectedCountry != null) {
                                          setState(() {
                                            _selectedCountry = selectedCountry;
                                            _hasError = false;
                                            errorMessage = '';
                                          });
                                        }
                                      }
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 16,
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        spacing: 8,
                                        children: [
                                          Text(
                                            _selectedCountry?.flag ?? '🇮🇳',
                                            style: TextStyle(fontSize: 20.sp),
                                          ),

                                          const Icon(Icons.arrow_drop_down),
                                        ],
                                      ),
                                    ),
                                  ),

                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16,
                                    ),
                                    child: Text(
                                      '+${_selectedCountry?.code ?? '91'}',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyMedium,
                                    ),
                                  ),

                                  Expanded(
                                    child: TextField(
                                      controller: _phoneController,
                                      focusNode: _phoneFocusNode,
                                      keyboardType: TextInputType.phone,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly,
                                        LengthLimitingTextInputFormatter(
                                          _selectedCountry?.maxLength ?? 10,
                                        ),
                                      ],
                                      decoration: InputDecoration(
                                        hintText: 'Mobile number',
                                        hintStyle: Theme.of(context)
                                            .textTheme
                                            .bodySmall
                                            ?.copyWith(
                                              color: Colors.black.withValues(
                                                alpha: 0.40,
                                              ),
                                            ),
                                        border: InputBorder.none,
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 16,
                                            ),
                                      ),
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyMedium,
                                      onChanged: (value) {
                                        if (_phoneController.text.length ==
                                            _selectedCountry!.maxLength) {
                                          _phoneFocusNode.unfocus();
                                        }
                                        if (_hasError) {
                                          setState(() {
                                            _hasError = false;
                                            errorMessage = '';
                                          });
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 8),
                            if (_hasError)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 4,
                                ),
                                child: Text(
                                  errorMessage,
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: Colors.red,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              ),
                            Text(
                              'We\'ll send you a one-time password (OTP) to verify.',
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(fontSize: 12.sp),
                            ),
                            const SizedBox(height: 14),
                            ButtonWidget(
                              text: 'Continue',
                              onPressed: () async {
                                _phoneFocusNode.unfocus();
                                if (validatePhone()) {
                                  setState(() {
                                    isLoading = true;
                                  });
                                  await AuthFunctions(context)
                                      .signinWithPhone(
                                        '+${_selectedCountry?.code ?? '91'}${_phoneController.text.trim()}',
                                      )
                                      .then((value) {
                                        setState(() {
                                          isLoading = false;
                                        });
                                        value
                                            ? Navigator.pushNamed(
                                                context,
                                                '/otp-verification',
                                                arguments: {
                                                  'phone': _phoneController.text
                                                      .trim(),
                                                  'country': _selectedCountry,
                                                },
                                              )
                                            : null;
                                      })
                                      .catchError((error) {
                                        log(
                                          'Error during phone sign-in: $error',
                                        );
                                      });
                                } else {
                                  _phoneFocusNode.requestFocus();
                                }
                              },
                              color: const Color(0xFF4285F4),
                              textColor: Colors.white,
                              width: double.infinity,
                              height: 50,
                              isLoading: isLoading,
                            ),
                            const SizedBox(height: 20),
                            Row(
                              spacing: 10,
                              children: [
                                Expanded(
                                  child: Container(
                                    height: 2,
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                Text(
                                  'More ways to sign in',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: Colors.black.withValues(
                                          alpha: 0.60,
                                        ),
                                      ),
                                ),

                                Expanded(
                                  child: Container(
                                    height: 2,
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              spacing: 20,
                              children: [
                                SocialIcons(
                                  logo: 'assets/icons/google.png',
                                  onTap: () {
                                    SocialAuth(context).signInWithGoogle();
                                  },
                                ),
                                SocialIcons(
                                  logo: 'assets/icons/apple.png',
                                  onTap: () {},
                                ),
                                SocialIcons(
                                  logo: 'assets/icons/email.png',
                                  onTap: () {},
                                ),
                              ],
                            ),
                          ],
                        )
                        .animate(delay: Duration(milliseconds: 100))
                        .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                        .slideX(
                          begin: 0.3,
                          end: 0,
                          duration: 600.ms,
                          curve: Curves.easeInOut,
                        ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi_driver/features/authentication/data/models/country_model.dart';
import 'package:tukxi_driver/features/authentication/functions/auth_functions.dart';
import 'package:tukxi_driver/shared/functions/progress_functions.dart';
import 'package:tukxi_driver/shared/widgets/button_widget.dart';

class VerifyOtp extends StatefulWidget {
  final String phone;
  final Country country;
  const VerifyOtp({super.key, required this.phone, required this.country});

  @override
  State<VerifyOtp> createState() => _VerifyOtpState();
}

class _VerifyOtpState extends State<VerifyOtp> {
  List<TextEditingController> controllers = List.generate(
    4,
    (index) => TextEditingController(),
  );
  List<FocusNode> focusNodes = List.generate(4, (index) => FocusNode());
  List<String> otpValues = ['', '', '', ''];
  bool isError = false;
  int _timerSeconds = 59;
  bool _showResendButton = true;
  bool isLoading = false;
  bool isResending = false;
  @override
  void initState() {
    super.initState();
    _startTimer();
    for (int i = 0; i < focusNodes.length; i++) {
      focusNodes[i].addListener(() {
        setState(() {});
      });
    }
  }

  void _startTimer() {
    setState(() {
      _showResendButton = true;
    });
    Future.delayed(Duration(seconds: 1), () {
      if (!mounted) return;
      if (_timerSeconds > 1) {
        setState(() {
          _timerSeconds--;
        });
        _startTimer();
      } else {
        setState(() {
          _showResendButton = false;
        });
      }
    });
  }

  @override
  void dispose() {
    for (var controller in controllers) {
      controller.dispose();
    }
    for (var node in focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  bool get isOTPComplete {
    return otpValues.every((value) => value.isNotEmpty);
  }

  void onOTPChanged(String value, int index) {
    setState(() {
      otpValues[index] = value;
      isError = false; // Reset error state on input change
    });

    if (value.isNotEmpty && index < 3) {
      // Move to next field
      focusNodes[index + 1].requestFocus();
    }
  }

  void onKeyPressed(KeyEvent event, int index) {
    if (event is KeyDownEvent &&
        event.logicalKey == LogicalKeyboardKey.backspace) {
      // Only move to previous field if current field is empty
      if (controllers[index].text.isEmpty && index > 0) {
        // Clear the previous field and move focus to it
        setState(() {
          otpValues[index - 1] = '';
          controllers[index - 1].clear();
        });
        focusNodes[index - 1].requestFocus();
      }
      // If current field has content, let the TextField handle the backspace naturally
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Verify OTP',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enter the 4-digit code sent via SMS at +${'${widget.country.code} ${widget.phone}'}',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),

            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              spacing: 15,
              children: List.generate(4, (index) {
                return Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.secondary,
                        border: Border.all(
                          color: isError ? Colors.red : Colors.grey[300]!,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: KeyboardListener(
                          focusNode: FocusNode(),
                          onKeyEvent: (event) => onKeyPressed(event, index),
                          child: TextField(
                            controller: controllers[index],
                            focusNode: focusNodes[index],
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.bodyMedium,
                            keyboardType: TextInputType.number,
                            maxLength: 1,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              counterText: '',
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            onChanged: (value) => onOTPChanged(value, index),
                          ),
                        ),
                      ),
                    )
                    .animate(delay: Duration(milliseconds: index * 100))
                    .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                    .slideY(
                      begin: 0.3,
                      end: 0,
                      duration: 600.ms,
                      curve: Curves.easeInOut,
                    );
              }),
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (isError)
                  Text(
                    'Invalid OTP',
                    style: GoogleFonts.raleway(
                      color: Colors.red,
                      fontSize: 14.sp,
                    ),
                  ),
                SizedBox(width: 0),
              ],
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Haven\'t received a code? ',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                if (_showResendButton)
                  Text(
                    'Send again in (00:${_timerSeconds.toString().padLeft(2, '0')})',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                if (!_showResendButton)
                  GestureDetector(
                    onTap: () async {
                      setState(() {
                        isResending = true;
                      });
                      await AuthFunctions(context)
                          .resendOTP('+${widget.country.code}${widget.phone}')
                          .then((success) {
                            if (success) {
                              setState(() {
                                _timerSeconds = 59;
                                _startTimer();
                                isError = false;
                                isResending = false;
                              });
                            } else {
                              setState(() {
                                isResending = false;
                              });
                            }
                          });
                    },
                    child: isResending
                        ? Padding(
                            padding: EdgeInsetsGeometry.only(left: 10),
                            child: SizedBox(
                              height: 10,
                              width: 10,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          )
                        : Text(
                            'Send again',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.underline,
                                ),
                          ),
                  ),
              ],
            ),
            const Spacer(),
            ButtonWidget(
              text: 'Verify',
              color: const Color(0xFF4285F4),
              textColor: Colors.white,
              width: double.infinity,
              height: 50,
              isLoading: isLoading,
              onPressed: isOTPComplete
                  ? () async {
                      setState(() {
                        isLoading = true;
                        isError = false;
                      });
                      await AuthFunctions(context)
                          .verifyOtp(
                            '+${widget.country.code}${widget.phone}',
                            otpValues.join(),
                          )
                          .then((success) async {
                            if (success) {
                              await ProgressFunctions(
                                context,
                              ).getProgress().then((progress) async {
                                setState(() {
                                  isLoading = false;
                                  isError = false;
                                });
                                Navigator.pushNamedAndRemoveUntil(
                                  context,
                                  '/choose-language',
                                  (route) => false,
                                );
                              });
                            } else {
                              setState(() {
                                isLoading = false;
                                isError = false;
                              });
                            }
                          });
                    }
                  : () {
                      setState(() {
                        isLoading = false;
                        isError = false;
                      });
                    },
            ),
            SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

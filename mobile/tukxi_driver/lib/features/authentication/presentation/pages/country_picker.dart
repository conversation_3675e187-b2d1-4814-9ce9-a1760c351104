import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tukxi_driver/features/authentication/data/models/country_model.dart';

class CountrySelectionScreen extends StatefulWidget {
  final Country selectedCountry;
  const CountrySelectionScreen({super.key, required this.selectedCountry});

  @override
  State<CountrySelectionScreen> createState() => _CountrySelectionScreenState();
}

class _CountrySelectionScreenState extends State<CountrySelectionScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Country> _filteredCountries = [];
  List<Country> countries = [];
  Country? _selectedCountry;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _selectedCountry = widget.selectedCountry;
      getCountries();
    });
  }

  void _filterCountries(String query) {
    setState(() {
      if (query.isEmpty) {
        _sortCountriesWithSelectedFirst();
      } else {
        _filteredCountries = countries
            .where(
              (country) =>
                  country.country.toLowerCase().contains(query.toLowerCase()) ||
                  country.code.contains(query) ||
                  country.flagCode.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  '+${country.code}'.contains(query),
            )
            .toList();

        // Sort filtered results with selected country at top
        if (_selectedCountry != null) {
          _filteredCountries.sort((a, b) {
            if (a.flagCode == _selectedCountry!.flagCode) return -1;
            if (b.flagCode == _selectedCountry!.flagCode) return 1;
            return a.country.compareTo(b.country);
          });
        }
      }
    });
  }

  void getCountries() async {
    try {
      final String response = await rootBundle.loadString(
        'assets/countries.json',
      );
      final List<dynamic> data = json.decode(response);

      setState(() {
        countries = data
            .where(
              (json) =>
                  json['country'] != null &&
                  json['flagCode'] != null &&
                  json['flag'] != null &&
                  json['code'] != null &&
                  json['country'].toString().isNotEmpty &&
                  json['flagCode'].toString().isNotEmpty,
            )
            .map((json) => Country.fromJson(json))
            .toList();

        // Sort countries with selected country at top
        _sortCountriesWithSelectedFirst();
        log('Countries loaded: ${countries.length}');
      });
    } catch (e) {
      log('Error loading countries: $e');
    }
  }

  void _sortCountriesWithSelectedFirst() {
    if (_selectedCountry != null) {
      countries.sort((a, b) {
        if (a.flagCode == _selectedCountry!.flagCode) return -1;
        if (b.flagCode == _selectedCountry!.flagCode) return 1;
        return a.country.compareTo(b.country);
      });
    } else {
      countries.sort((a, b) => a.country.compareTo(b.country));
    }
    _filteredCountries = List.from(countries);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(
                context,
                _selectedCountry ?? widget.selectedCountry,
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Select Country',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: Column(
        children: [
          // Search field
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              onChanged: _filterCountries,
              autofocus: true,
              decoration: InputDecoration(
                hintText: 'Search by country or code',
                hintStyle: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(color: Colors.grey[500]),
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                filled: true,
                fillColor: const Color(0xFFF5F5F5),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
              ),
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),

          // Countries list
          Expanded(
            child: ListView.builder(
              itemCount: _filteredCountries.length,
              itemBuilder: (context, index) {
                final country = _filteredCountries[index];
                final isSelected =
                    country.flagCode == _selectedCountry?.flagCode;

                return Container(
                  color: isSelected
                      ? const Color(0xFF4285F4).withValues(alpha: 0.1)
                      : null,
                  child: ListTile(
                    leading: Text(
                      country.flag,
                      style: TextStyle(fontSize: 24.sp),
                    ),
                    title: Text(
                      country.country,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: isSelected ? const Color(0xFF4285F4) : null,
                        fontWeight: isSelected ? FontWeight.w600 : null,
                      ),
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '+${country.code}',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color: isSelected
                                    ? const Color(0xFF4285F4)
                                    : Colors.grey[600],
                              ),
                        ),
                        if (isSelected) ...[
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.check_circle,
                            color: Color(0xFF4285F4),
                            size: 20,
                          ),
                        ],
                      ],
                    ),
                    onTap: () {
                      Navigator.pop(context, country);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

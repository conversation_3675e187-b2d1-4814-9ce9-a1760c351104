import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:provider/provider.dart';
import 'package:tukxi_driver/core/constants/app_config.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:tukxi_driver/features/authentication/presentation/viewmodels/auth_provider.dart';
import 'package:tukxi_driver/shared/widgets/snackbar_widget.dart';

class AuthFunctions {
  BuildContext context;
  late var header;
  AuthFunctions(this.context) {
    header = {'Content-Type': 'application/json', 'x-app-type': 'driver'};
  }
  Future<bool> signinWithPhone(String phoneNumber) async {
    String url = '${AppConfig.apiBaseUrl}/api/v1/auth/phone/signup';
    var body = {'phoneNumber': phoneNumber};
    log(body.toString(), name: 'AuthFunctions.signinWithPhone');
    var response = await http.post(
      Uri.parse(url),
      headers: header,
      body: j<PERSON><PERSON>nco<PERSON>(body),
    );
    log(response.body, name: 'AuthFunctions.signinWithPhone');
    if (response.statusCode == 200 || response.statusCode == 201) {
      SnackbarWidget(context).show('OTP sent successfully', false);
      return true;
    } else {
      log(
        'Error: ${response.statusCode}',
        name: 'AuthFunctions.signinWithPhone',
      );
      return false;
    }
  }

  Future<bool> verifyOtp(String phoneNumber, String otp) async {
    String url = '${AppConfig.apiBaseUrl}/api/v1/auth/phone/verify';
    var body = {'phoneNumber': phoneNumber, 'otp': otp};
    log(body.toString(), name: 'AuthFunctions.verifyOtp');
    var response = await http.post(
      Uri.parse(url),
      headers: header,
      body: jsonEncode(body),
    );
    log(response.body, name: 'AuthFunctions.verifyOtp');
    if (response.statusCode == 200 || response.statusCode == 201) {
      var data = jsonDecode(response.body)['data'];
      var storage = FlutterSecureStorage();
      await storage.write(key: 'token', value: data['accessToken']);
      await storage.write(key: 'refresh', value: data['refreshToken']);
      await storage.write(
        key: 'active_on',
        value: DateTime.now().toIso8601String(),
      );
      SnackbarWidget(context).show('OTP verified successfully', false);
      Provider.of<AuthProvider>(
        context,
        listen: false,
      ).setToken(data['accessToken']);
      Provider.of<AuthProvider>(
        context,
        listen: false,
      ).setRefreshToken(data['refreshToken']);
      log(data['accessToken'], name: 'AuthFunctions.verifyOtp - Access Token');
      log(
        data['refreshToken'],
        name: 'AuthFunctions.verifyOtp - Refresh Token',
      );
      return true;
    } else {
      log('Error: ${response.statusCode}', name: 'AuthFunctions.verifyOtp');
      return false;
    }
  }

  Future<bool> resendOTP(String phoneNumber) async {
    String url = '${AppConfig.apiBaseUrl}/api/v1/auth/phone/resend-otp';
    var body = {'phoneNumber': phoneNumber};
    log(body.toString(), name: 'AuthFunctions.resendOTP');
    var response = await http.post(
      Uri.parse(url),
      headers: header,
      body: jsonEncode(body),
    );
    log(response.body, name: 'AuthFunctions.resendOTP');
    if (response.statusCode == 200 || response.statusCode == 201) {
      SnackbarWidget(context).show('OTP resent successfully', false);
      return true;
    } else {
      log('Error: ${response.statusCode}', name: 'AuthFunctions.resendOTP');
      return false;
    }
  }

  Future<bool> refreshToken() async {
    String url = '${AppConfig.apiBaseUrl}/api/v1/auth/refresh-token';
    var body = {
      'refreshToken': Provider.of<AuthProvider>(
        context,
        listen: false,
      ).refreshToken,
    };
    var response = await http.post(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
        'Authorization':
            'Bearer ${Provider.of<AuthProvider>(context, listen: false).token}',
      },
      body: jsonEncode(body),
    );
    // log(response.body, name: 'AuthFunctions.refreshToken');
    if (response.statusCode == 200 || response.statusCode == 201) {
      var data = jsonDecode(response.body)['data'];
      var storage = FlutterSecureStorage();
      await storage.write(key: 'token', value: data['accessToken']);
      await storage.write(key: 'refresh', value: data['refreshToken']);
      await storage.write(
        key: 'active_on',
        value: DateTime.now().toIso8601String(),
      );
      Provider.of<AuthProvider>(
        context,
        listen: false,
      ).setToken(data['accessToken']);
      Provider.of<AuthProvider>(
        context,
        listen: false,
      ).setRefreshToken(data['refreshToken']);
      return true;
    } else {
      log('Error: ${response.statusCode}', name: 'AuthFunctions.refreshToken');
      return false;
    }
  }

  Future<bool> signOut() async {
    var storage = const FlutterSecureStorage();
    await storage.deleteAll();
    Provider.of<AuthProvider>(context, listen: false).clearToken();
    log('User signed out', name: 'AuthFunctions.signOut');
    return true;
  }
}

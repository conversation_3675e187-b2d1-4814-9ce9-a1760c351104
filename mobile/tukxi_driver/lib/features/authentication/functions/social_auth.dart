import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:tukxi_driver/core/utils/google_auth_services.dart';

class SocialAuth {
  BuildContext context;
  SocialAuth(this.context);

  Future<bool> signInWithGoogle({bool retryOnFailure = true}) async {
    // Sign in with Google and get ID token
    String idToken = await GoogleAuthService.signInWithGoogle();

    if (idToken.isNotEmpty) {
      log('Sign in successful, ID token: $idToken');
      // Use the ID token for your authentication
      return true;
    } else {
      log('Sign in failed or cancelled');
      return false;
    }
  }
}

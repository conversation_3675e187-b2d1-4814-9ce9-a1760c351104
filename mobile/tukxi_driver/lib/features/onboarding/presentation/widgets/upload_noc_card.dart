import 'package:flutter/material.dart';

class UploadNocCard extends StatelessWidget {
  final Function onTap;
  final bool isUploaded;
  const UploadNocCard({
    super.key,
    required this.onTap,
    required this.isUploaded,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: ShapeDecoration(
        color: const Color(0x19FDE14C),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            strokeAlign: BorderSide.strokeAlignCenter,
            color: const Color(0xFFFDE14C),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 10,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.error_outline_outlined,
                color: Color(0xFFA36730),
                size: 24,
              ),
              SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 5,
                  children: [
                    Text(
                      'NOC Required',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Color(0xFFA36730),
                      ),
                    ),
                    Text(
                      'Vehicle owner is different from driver. Please upload No Objection Certificate.',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Color(0xFFA36730)),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (!isUploaded)
            GestureDetector(
              onTap: () => onTap(),
              child: Container(
                height: 35,
                width: double.infinity,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      width: 1,
                      strokeAlign: BorderSide.strokeAlignCenter,
                      color: const Color(0xFFA36730),
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  spacing: 10,
                  children: [
                    Icon(
                      Icons.file_upload_outlined,
                      color: const Color(0xFFA36730),
                      size: 20,
                    ),
                    Text(
                      'Upload NOC',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: const Color(0xFFA36730),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

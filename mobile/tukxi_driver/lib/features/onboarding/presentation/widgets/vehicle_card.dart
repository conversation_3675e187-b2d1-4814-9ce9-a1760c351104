import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VehicleCard extends StatelessWidget {
  final String vehicleName;
  final bool isSelected;
  const VehicleCard({
    super.key,
    required this.vehicleName,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      margin: const EdgeInsets.only(bottom: 14),
      decoration: ShapeDecoration(
        color: isSelected ? const Color(0xFFF0FDF4) : Colors.white,
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            strokeAlign: BorderSide.strokeAlignCenter,
            color: isSelected
                ? Colors.green
                : Colors.black.withValues(alpha: 0.25),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: 20,
        children: [
          Container(
            height: 48,
            width: 48,
            decoration: BoxDecoration(color: Colors.grey),
          ),
          Text(
            vehicleName,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.black,
              fontSize: 16.sp,
            ),
          ),
        ],
      ),
    );
  }
}

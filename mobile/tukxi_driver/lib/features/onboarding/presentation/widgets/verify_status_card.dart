import 'package:flutter/material.dart';

class VerifyStatusCard extends StatelessWidget {
  final String title;
  final String id;
  final String status;
  final String method;
  const VerifyStatusCard({
    super.key,
    required this.title,
    required this.id,
    required this.status,
    required this.method,
  });

  @override
  Widget build(BuildContext context) {
    Color statusColor;
    if (status == 'Pending') {
      statusColor = const Color(0xFFFDE14C);
    } else if (status == 'Failed') {
      statusColor = const Color(0xFFEA5C5C);
    } else {
      statusColor = const Color(0xFF65D78F); // Default color for Verified
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      margin: const EdgeInsets.only(bottom: 10),
      decoration: ShapeDecoration(
        color: statusColor.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: statusColor, width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 10,
        children: [
          Container(
            padding: EdgeInsets.all(5),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100),
              border: Border.all(color: statusColor, width: 2),
            ),
            child: Icon(
              status == 'Pending'
                  ? Icons.hourglass_empty
                  : status == 'Failed'
                  ? Icons.close
                  : Icons.done,
              color: statusColor,
              size: 10,
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$title $status',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 5),
              Text(id, style: Theme.of(context).textTheme.bodySmall),
              const SizedBox(height: 5),
              Text(
                'Verified via $method',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

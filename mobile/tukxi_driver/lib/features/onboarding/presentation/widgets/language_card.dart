import 'package:flutter/material.dart';

class LanguageCard extends StatelessWidget {
  final String title;
  final String example;
  final bool isSelected;
  const LanguageCard({
    super.key,
    required this.title,
    required this.example,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      margin: const EdgeInsets.only(bottom: 10),
      decoration: ShapeDecoration(
        color: isSelected ? const Color(0xFFF0FDF4) : Colors.white,
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            strokeAlign: BorderSide.strokeAlignCenter,
            color: isSelected
                ? const Color(0xFF65D78F)
                : Colors.black.withValues(alpha: 0.25),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.black),
          ),
          const SizedBox(height: 4),
          Text(
            example,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.black.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }
}

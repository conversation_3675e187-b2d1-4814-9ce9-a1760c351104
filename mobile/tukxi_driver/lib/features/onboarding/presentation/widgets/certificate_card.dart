import 'package:flutter/material.dart';

class CertificateCard extends StatelessWidget {
  final String title;
  final String status;
  const CertificateCard({super.key, required this.title, required this.status});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      width: double.infinity,
      padding: const EdgeInsets.only(top: 14, bottom: 14, left: 10),
      decoration: ShapeDecoration(
        color: status == 'Verified'
            ? const Color(0x1965D78F)
            : status == 'Pending'
            ? const Color(0x19FDE14C)
            : const Color(0x19EA5C5C),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            strokeAlign: BorderSide.strokeAlignCenter,
            color: status == 'Verified'
                ? const Color(0xFF65D78F)
                : status == 'Pending'
                ? const Color(0xFFFDE14C)
                : const Color(0xFFEA5C5C),
          ),
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: 10,
        children: [
          Image.asset(
            'assets/icons/document.png',
            color: status == 'Verified'
                ? const Color(0xFF65D78F)
                : status == 'Pending'
                ? const Color(0xFFFDE14C)
                : const Color(0xFFEA5C5C),
            height: 20,
          ),
          Text(title, style: Theme.of(context).textTheme.headlineSmall),
          Spacer(flex: 1),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(
                color: status == 'Verified'
                    ? const Color(0xFF65D78F)
                    : status == 'Pending'
                    ? const Color(0xFFFDE14C)
                    : const Color(0xFFEA5C5C),
              ),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              spacing: 5,
              children: [
                Icon(
                  status == 'Verified'
                      ? Icons.check_circle_outline_rounded
                      : status == 'Pending'
                      ? Icons.schedule_outlined
                      : Icons.error_outline_outlined,
                  color: status == 'Verified'
                      ? const Color(0xFF65D78F)
                      : status == 'Pending'
                      ? const Color(0xFFFDE14C)
                      : const Color(0xFFEA5C5C),
                ),
                Text(
                  status,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: status == 'Verified'
                        ? const Color(0xFF65D78F)
                        : status == 'Pending'
                        ? const Color(0xFFFDE14C)
                        : const Color(0xFFEA5C5C),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VerificationCard extends StatelessWidget {
  const VerificationCard({
    super.key,
    required this.title,
    required this.info,
    required this.status,
    required this.onActionPressed,
  });

  final String title;
  final String info;
  final String status;
  final VoidCallback onActionPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: ShapeDecoration(
        color: const Color(0xFFF3F3F3),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: status == 'Verified'
          ? Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(title, style: Theme.of(context).textTheme.bodyLarge),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: status == 'Verified'
                          ? const Color(0xFF65D78F)
                          : status == 'Pending'
                          ? const Color(0xFFA36730)
                          : status == 'Rejected'
                          ? const Color(0xFFEA5C5C)
                          : const Color(0xFFFF9933),
                    ),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    spacing: 5,
                    children: [
                      Icon(
                        status == 'Verified'
                            ? Icons.check_circle_outline_rounded
                            : status == 'Pending'
                            ? Icons.schedule_outlined
                            : Icons.error_outline_outlined,
                        color: status == 'Verified'
                            ? const Color(0xFF65D78F)
                            : status == 'Pending'
                            ? const Color(0xFFA36730)
                            : status == 'Rejected'
                            ? const Color(0xFFEA5C5C)
                            : const Color(0xFFFF9933),
                        size: 16.sp,
                      ),
                      Text(
                        status,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: status == 'Verified'
                              ? const Color(0xFF65D78F)
                              : status == 'Pending'
                              ? const Color(0xFFA36730)
                              : status == 'Rejected'
                              ? const Color(0xFFEA5C5C)
                              : const Color(0xFFFF9933),
                          fontSize: 10.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          : status == 'Rejected'
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 5,
              children: [
                Text(title, style: Theme.of(context).textTheme.bodyLarge),
                Text(
                  'Info: $info',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: status == 'Verified'
                              ? const Color(0xFF65D78F)
                              : status == 'Pending'
                              ? const Color(0xFFA36730)
                              : status == 'Rejected'
                              ? const Color(0xFFEA5C5C)
                              : const Color(0xFFFF9933),
                        ),
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        spacing: 5,
                        children: [
                          Icon(
                            status == 'Verified'
                                ? Icons.check_circle_outline_rounded
                                : status == 'Pending'
                                ? Icons.schedule_outlined
                                : Icons.error_outline_outlined,
                            color: status == 'Verified'
                                ? const Color(0xFF65D78F)
                                : status == 'Pending'
                                ? const Color(0xFFA36730)
                                : status == 'Rejected'
                                ? const Color(0xFFEA5C5C)
                                : const Color(0xFFFF9933),
                            size: 16.sp,
                          ),
                          Text(
                            status,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: status == 'Verified'
                                      ? const Color(0xFF65D78F)
                                      : status == 'Pending'
                                      ? const Color(0xFFA36730)
                                      : status == 'Rejected'
                                      ? const Color(0xFFEA5C5C)
                                      : const Color(0xFFFF9933),
                                  fontSize: 10.sp,
                                ),
                          ),
                        ],
                      ),
                    ),

                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Row(
                        spacing: 5,
                        children: [
                          Icon(Icons.refresh_outlined, size: 14.sp),
                          Text(
                            'Re-upload',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            )
          : status == 'Pending'
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 5,
              children: [
                Text(title, style: Theme.of(context).textTheme.bodyLarge),
                Text(
                  'Info: $info',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: status == 'Verified'
                              ? const Color(0xFF65D78F)
                              : status == 'Pending'
                              ? const Color(0xFFA36730)
                              : status == 'Rejected'
                              ? const Color(0xFFEA5C5C)
                              : const Color(0xFFFF9933),
                        ),
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        spacing: 5,
                        children: [
                          Icon(
                            status == 'Verified'
                                ? Icons.check_circle_outline_rounded
                                : status == 'Pending'
                                ? Icons.schedule_outlined
                                : Icons.error_outline_outlined,
                            color: status == 'Verified'
                                ? const Color(0xFF65D78F)
                                : status == 'Pending'
                                ? const Color(0xFFA36730)
                                : status == 'Rejected'
                                ? const Color(0xFFEA5C5C)
                                : const Color(0xFFFF9933),
                            size: 16.sp,
                          ),
                          Text(
                            status,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: status == 'Verified'
                                      ? const Color(0xFF65D78F)
                                      : status == 'Pending'
                                      ? const Color(0xFFA36730)
                                      : status == 'Rejected'
                                      ? const Color(0xFFEA5C5C)
                                      : const Color(0xFFFF9933),
                                  fontSize: 10.sp,
                                ),
                          ),
                        ],
                      ),
                    ),

                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Row(
                        spacing: 5,
                        children: [
                          Icon(Icons.cloud_upload_outlined, size: 14.sp),
                          Text(
                            'Upload',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 5,
              children: [
                Text(title, style: Theme.of(context).textTheme.bodyLarge),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: status == 'Verified'
                          ? const Color(0xFF65D78F)
                          : status == 'Pending'
                          ? const Color(0xFFA36730)
                          : status == 'Rejected'
                          ? const Color(0xFFEA5C5C)
                          : const Color(0xFFFF9933),
                    ),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    spacing: 5,
                    children: [
                      Icon(
                        status == 'Verified'
                            ? Icons.check_circle_outline_rounded
                            : status == 'Pending'
                            ? Icons.schedule_outlined
                            : Icons.error_outline_outlined,
                        color: status == 'Verified'
                            ? const Color(0xFF65D78F)
                            : status == 'Pending'
                            ? const Color(0xFFA36730)
                            : status == 'Rejected'
                            ? const Color(0xFFEA5C5C)
                            : const Color(0xFFFF9933),
                        size: 16.sp,
                      ),
                      Text(
                        status,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: status == 'Verified'
                              ? const Color(0xFF65D78F)
                              : status == 'Pending'
                              ? const Color(0xFFA36730)
                              : status == 'Rejected'
                              ? const Color(0xFFEA5C5C)
                              : const Color(0xFFFF9933),
                          fontSize: 10.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
}

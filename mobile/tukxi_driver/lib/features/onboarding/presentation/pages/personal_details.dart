import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:tukxi_driver/core/utils/validators.dart';
import 'package:tukxi_driver/features/onboarding/functions/onboarding_functions.dart';
import 'package:tukxi_driver/shared/functions/city_data.dart';
import 'package:tukxi_driver/shared/widgets/button_widget.dart';
import 'package:tukxi_driver/shared/widgets/snackbar_widget.dart';
import 'package:tukxi_driver/shared/widgets/textfield.dart';

class PersonalDetails extends StatefulWidget {
  const PersonalDetails({super.key});

  @override
  State<PersonalDetails> createState() => _PersonalDetailsState();
}

class _PersonalDetailsState extends State<PersonalDetails> {
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController referralCodeController = TextEditingController();
  DateTime? selectedDate;
  bool isEmailValid = true;
  List<String> genders = ['Male', 'Female', 'Other'];
  String selectedGender = '';
  List<Map<String, dynamic>> cities = [];
  String selectedCity = '';
  bool isLoading = false;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await CityData(context)
          .fetchCities()
          .then((fetchedCities) {
            setState(() {
              cities = fetchedCities;
            });
          })
          .catchError((_) {
            SnackbarWidget(context).show('Failed to load cities.', true);
          });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Your Tuxi Driver Account',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Personal Details',
                style: Theme.of(
                  context,
                ).textTheme.headlineMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 8),
              Text(
                'Please provide us more about yourself.',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 20),
              BuildField(
                    label: 'First name',
                    controller: firstNameController,
                    isMandetory: true,
                    isCapitalize: true,
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Last name',
                    controller: lastNameController,
                    isMandetory: true,
                    isCapitalize: true,
                  )
                  .animate(delay: Duration(milliseconds: 200))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              Row(
                    children: [
                      Text(
                        'Date of Birth',
                        style: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.copyWith(color: Colors.black),
                      ),
                      Text(' *', style: TextStyle(color: Colors.red)),
                    ],
                  )
                  .animate(delay: Duration(milliseconds: 300))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 5),
              GestureDetector(
                    onTap: () {
                      showDatePicker(
                        context: context,
                        initialDate:
                            selectedDate ??
                            DateTime.now().subtract(Duration(days: 6570)),
                        firstDate: DateTime(1900),
                        lastDate: DateTime.now().subtract(Duration(days: 6570)),
                        builder: (BuildContext context, Widget? child) {
                          return Theme(
                            data: ThemeData(
                              primaryColor: const Color(
                                0xFF4285F4,
                              ), // Set your desired primary color
                              colorScheme: ColorScheme.light(
                                primary: const Color(0xFF4285F4),
                              ),
                            ),
                            child: child!,
                          );
                        },
                      ).then((date) {
                        if (date != null) {
                          setState(() {
                            selectedDate = date;
                          });
                        }
                      });
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.secondary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: EdgeInsets.only(left: 16, right: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            selectedDate != null
                                ? DateFormat('dd/MM/yyyy').format(selectedDate!)
                                : 'Date of Birth',
                            style: Theme.of(context).textTheme.bodyLarge
                                ?.copyWith(
                                  color: selectedDate != null
                                      ? Colors.black
                                      : Colors.black.withValues(alpha: 0.6),
                                  fontSize: 14.sp,
                                ),
                          ),
                          Icon(
                            Icons.calendar_today,
                            color: Colors.black.withValues(alpha: 0.6),
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 350))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Email address',
                    controller: emailController,
                    isMandetory: true,
                    onChanged: (value) {
                      setState(() {
                        isEmailValid = Validators.validateEmail(value);
                      });
                    },
                    isError: !isEmailValid,
                    errorText: 'Please enter a valid email address.',
                  )
                  .animate(delay: Duration(milliseconds: 400))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              Row(
                    children: [
                      Text(
                        'Gender',
                        style: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.copyWith(color: Colors.black),
                      ),
                      Text(' *', style: TextStyle(color: Colors.red)),
                    ],
                  )
                  .animate(delay: Duration(milliseconds: 500))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 5),
              Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.secondary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: EdgeInsets.only(left: 16, right: 16),
                    child: DropdownButton<String>(
                      value: selectedGender.isNotEmpty ? selectedGender : null,
                      hint: Text(
                        'Select your gender',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.black.withValues(alpha: 0.6),
                          fontSize: 14.sp,
                        ),
                      ),
                      isExpanded: true,
                      underline: SizedBox(),
                      items: genders.map((String gender) {
                        return DropdownMenuItem<String>(
                          value: gender,
                          child: Text(
                            gender,
                            style: Theme.of(context).textTheme.bodyLarge
                                ?.copyWith(color: Colors.black),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedGender = newValue ?? '';
                        });
                      },
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 550))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              Row(
                    children: [
                      Text(
                        'City',
                        style: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.copyWith(color: Colors.black),
                      ),
                      Text(' *', style: TextStyle(color: Colors.red)),
                    ],
                  )
                  .animate(delay: Duration(milliseconds: 600))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 5),
              Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.secondary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: EdgeInsets.only(left: 16, right: 16),
                    child: DropdownButton<String>(
                      value: selectedCity.isNotEmpty ? selectedCity : null,
                      hint: Text(
                        'Select your city',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.black.withValues(alpha: 0.6),
                          fontSize: 14.sp,
                        ),
                      ),
                      isExpanded: true,
                      underline: SizedBox(),
                      items: cities.map((Map<String, dynamic> city) {
                        return DropdownMenuItem<String>(
                          value: city['name'],
                          child: Text(
                            city['name'],
                            style: Theme.of(context).textTheme.bodyLarge
                                ?.copyWith(color: Colors.black),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedCity = newValue ?? '';
                        });
                      },
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 650))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Referral code (optional)',
                    controller: referralCodeController,
                    isMandetory: false,
                  )
                  .animate(delay: Duration(milliseconds: 700))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
              ButtonWidget(
                    text: 'Continue',
                    onPressed: () async {
                      if (firstNameController.text.trim().isEmpty ||
                          lastNameController.text.trim().isEmpty ||
                          emailController.text.trim().isEmpty ||
                          selectedGender.isEmpty ||
                          selectedDate == null ||
                          selectedCity.isEmpty) {
                        SnackbarWidget(
                          context,
                        ).show('Please fill all mandatory fields.', true);
                        return;
                      }

                      if (!isEmailValid) {
                        SnackbarWidget(
                          context,
                        ).show('Please enter a valid email address.', true);
                        return;
                      }
                      setState(() {
                        isLoading = true;
                      });
                      var body = {
                        "firstName": firstNameController.text.trim(),
                        "lastName": lastNameController.text.trim(),
                        "email": emailController.text.trim(),
                        "gender": selectedGender.toUpperCase(),
                        "dob": DateFormat('yyyy-MM-dd').format(selectedDate!),
                        "cityId": cities.firstWhere(
                          (city) => city['name'] == selectedCity,
                        )['id'],
                        "referralCode": referralCodeController.text.trim(),
                      };
                      log(body.toString(), name: 'PersonalDetails');
                      await OnboardingFunctions(
                        context,
                      ).savePersonalDetails(body).then((success) {
                        setState(() {
                          isLoading = false;
                        });
                        if (success) {
                          Navigator.pushNamed(context, '/select-vehicle');
                        }
                      });
                    },
                    color: const Color(0xFF4285F4),
                    textColor: Colors.white,
                    width: double.infinity,
                    height: 50,
                    isLoading: isLoading,
                  )
                  .animate(delay: Duration(milliseconds: 800))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}

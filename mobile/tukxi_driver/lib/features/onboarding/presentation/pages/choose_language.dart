import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:tukxi_driver/features/onboarding/functions/onboarding_functions.dart';
import 'package:tukxi_driver/features/onboarding/presentation/widgets/language_card.dart';
import 'package:tukxi_driver/shared/functions/get_languages.dart';
import 'package:tukxi_driver/shared/widgets/button_widget.dart';

class ChooseLanguage extends StatefulWidget {
  const ChooseLanguage({super.key});

  @override
  State<ChooseLanguage> createState() => _ChooseLanguageState();
}

class _ChooseLanguageState extends State<ChooseLanguage> {
  List<Map<String, dynamic>> languages = [
    {'name': 'English', 'example': 'English'},
    {'name': 'Malayalam', 'example': 'മലയാളം'},
    {'name': 'Hindi', 'example': 'हिंदी'},
    {'name': 'Tamil', 'example': 'தமிழ்'},
    {'name': 'Telugu', 'example': 'తెలుగు'},
    {'name': 'Kannada', 'example': 'ಕನ್ನಡ'},
    {'name': 'Bengali', 'example': 'বাংলা'},
    {'name': 'Gujarati', 'example': 'ગુજરાતી'},
  ];
  String selectedLanguage = '';
  bool isLoading = true;
  bool isSaving = false;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      GetLanguages(context).fetchLanguages().then((data) {
        setState(() {
          languages = data;
          isLoading = false;
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/login',
                (route) => false,
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Your Tuxi Driver Account',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Chose your Language',
                style: Theme.of(
                  context,
                ).textTheme.headlineMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 8),
              Text(
                'Select your preferred language to continue.',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 20),
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                itemCount: languages.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedLanguage = languages[index]['name'];
                          });
                        },
                        child: LanguageCard(
                          title: languages[index]['name'],
                          example: languages[index]['example'],
                          isSelected:
                              selectedLanguage == languages[index]['name'],
                        ),
                      )
                      .animate(delay: Duration(milliseconds: index * 100))
                      .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                      .slideX(
                        begin: 0.3,
                        end: 0,
                        duration: 600.ms,
                        curve: Curves.easeInOut,
                      );
                },
              ),
              if (selectedLanguage.isNotEmpty)
                ButtonWidget(
                  text: 'Continue',
                  onPressed: () async {
                    if (selectedLanguage.isNotEmpty) {
                      setState(() {
                        isSaving = true;
                      });
                      String id = languages.firstWhere(
                        (lang) => lang['name'] == selectedLanguage,
                        orElse: () => {'id': ''},
                      )['id'];
                      await OnboardingFunctions(context).saveLanguage(id).then((
                        isSaved,
                      ) async {
                        setState(() {
                          isSaving = false;
                        });
                        if (isSaved) {
                          Navigator.pushNamed(context, '/personal-details');
                        }
                      });
                    }
                  },
                  color: const Color(0xFF4285F4),
                  textColor: Colors.white,
                  width: double.infinity,
                  height: 50,
                  isLoading: isSaving,
                ),
              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}

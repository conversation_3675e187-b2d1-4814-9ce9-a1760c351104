import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:tukxi_driver/features/onboarding/presentation/widgets/verify_status_card.dart';
import 'package:tukxi_driver/shared/widgets/button_widget.dart';

class KycStatus extends StatefulWidget {
  const KycStatus({super.key});

  @override
  State<KycStatus> createState() => _KycStatusState();
}

class _KycStatusState extends State<KycStatus> {
  final List<Map<String, String>> verificationData = [
    {
      'title': 'Aadhaar',
      'id': '1234-5678-9012',
      'status': 'Verified',
      'method': 'Digilocker',
    },
    {
      'title': 'Driving License',
      'id': 'ABCDE1234F',
      'status': 'Pending',
      'method': 'Digilocker',
    },
    {
      'title': 'PUC',
      'id': 'ABCDE1234F',
      'status': 'Failed',
      'method': 'Upload',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Verification',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const SizedBox(height: 20),
              Image.asset(
                    'assets/icons/success-shield.png',
                    width: MediaQuery.of(context).size.width * 0.33,
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              const SizedBox(height: 20),
              Text(
                    'KYC Verification',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              Text(
                    'Verify your identity documents',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.black),
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              const SizedBox(height: 20),
              ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: verificationData.length,
                itemBuilder: (context, index) {
                  final data = verificationData[index];
                  return VerifyStatusCard(
                        title: data['title']!,
                        id: data['id']!,
                        status: data['status']!,
                        method: data['method']!,
                      )
                      .animate(delay: Duration(milliseconds: index * 100))
                      .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                      .slideX(
                        begin: 0.3,
                        end: 0,
                        duration: 600.ms,
                        curve: Curves.easeInOut,
                      );
                },
              ),
              SizedBox(height: 10),
              ButtonWidget(
                    text: 'Continue',
                    onPressed: () {
                      Navigator.pushNamed(context, '/profile-photo');
                    },
                    color: const Color(0xFF4285F4),
                    textColor: Colors.white,
                    width: double.infinity,
                    height: 50,
                    isLoading: false,
                  )
                  .animate(
                    delay: Duration(
                      milliseconds: verificationData.length * 100,
                    ),
                  )
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
            ],
          ),
        ),
      ),
    );
  }
}

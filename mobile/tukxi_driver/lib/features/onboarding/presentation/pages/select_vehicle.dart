import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tukxi_driver/features/onboarding/functions/onboarding_functions.dart';
import 'package:tukxi_driver/features/onboarding/presentation/widgets/vehicle_card.dart';
import 'package:tukxi_driver/shared/functions/city_data.dart';
import 'package:tukxi_driver/shared/widgets/button_widget.dart';
import 'package:tukxi_driver/shared/widgets/snackbar_widget.dart';

class SelectVehicle extends StatefulWidget {
  const SelectVehicle({super.key});

  @override
  State<SelectVehicle> createState() => _SelectVehicleState();
}

class _SelectVehicleState extends State<SelectVehicle> {
  List<String> vehicleTypes = ['', '', ''];
  List<Map<String, dynamic>> vehicleData = [];
  String selectedVehicle = '';
  TextEditingController vehicleNumberController = TextEditingController();
  bool isLoading = true;
  bool isSaving = false;
  bool isVehicleNumberValid = true;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        isLoading = true;
      });
      vehicleData = await CityData(context).getCityVehicle();
      if (vehicleData.isNotEmpty) {
        setState(() {
          vehicleTypes = [];
          vehicleTypes = vehicleData.map((e) => e['name'] as String).toList();
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Your Tuxi Driver Account',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select your Vehicle',
                style: Theme.of(
                  context,
                ).textTheme.headlineMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 8),
              Text(
                'Available vehicles in your city.',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 20),
              Row(
                children: [
                  Text(
                    'Vehicle types',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.black),
                  ),
                  Text(' *', style: TextStyle(color: Colors.red)),
                ],
              ),
              SizedBox(height: 10),
              isLoading
                  ? Shimmer(
                      gradient: LinearGradient(
                        colors: [
                          Colors.grey.shade100,
                          Colors.grey.shade200,
                          Colors.grey.shade100,
                        ],
                        stops: const [0.1, 0.5, 0.9],
                      ),
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        itemBuilder: (context, index) {
                          return GestureDetector(
                                onTap: () => setState(() {
                                  selectedVehicle = vehicleTypes[index];
                                }),
                                child: VehicleCard(
                                  vehicleName: vehicleTypes[index],
                                  isSelected:
                                      selectedVehicle == vehicleTypes[index],
                                ),
                              )
                              .animate(
                                delay: Duration(milliseconds: index * 100),
                              )
                              .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                              .slideY(
                                begin: 0.3,
                                end: 0,
                                duration: 600.ms,
                                curve: Curves.easeInOut,
                              );
                        },
                        itemCount: vehicleTypes.length,
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                      ),
                    )
                  : ListView.builder(
                      padding: EdgeInsets.zero,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                              onTap: () => setState(() {
                                selectedVehicle = vehicleTypes[index];
                              }),
                              child: VehicleCard(
                                vehicleName: vehicleTypes[index],
                                isSelected:
                                    selectedVehicle == vehicleTypes[index],
                              ),
                            )
                            .animate(delay: Duration(milliseconds: index * 100))
                            .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                            .slideY(
                              begin: 0.3,
                              end: 0,
                              duration: 600.ms,
                              curve: Curves.easeInOut,
                            );
                      },
                      itemCount: vehicleTypes.length,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                    ),
              if (!isLoading)
                Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              'Vehicle number',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: Colors.black),
                            ),
                            Text(' *', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                        SizedBox(height: 5),
                        Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.secondary,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: !isVehicleNumberValid
                                  ? Colors.red
                                  : Colors.transparent,
                              width: 1,
                            ),
                          ),
                          padding: const EdgeInsets.only(left: 16),
                          child: TextField(
                            controller: vehicleNumberController,
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'[A-Z0-9a-z]'),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                isVehicleNumberValid = value.isNotEmpty;
                                vehicleNumberController.text = value
                                    .toUpperCase();
                              });
                            },

                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: 'Eg: KL29X1998',
                              hintStyle: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(
                                    color: Colors.black.withValues(alpha: 0.6),
                                    fontSize: 14.sp,
                                  ),
                            ),
                            style: Theme.of(context).textTheme.headlineSmall
                                ?.copyWith(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ),
                        if (!isVehicleNumberValid)
                          Padding(
                            padding: const EdgeInsets.only(top: 5.0),
                            child: Text(
                              'Please enter a valid vehicle number',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.red,
                                    fontSize: 12.sp,
                                  ),
                            ),
                          ),
                        SizedBox(height: isVehicleNumberValid ? 20 : 8),
                        ButtonWidget(
                          text: 'Continue',
                          onPressed: () async {
                            if (selectedVehicle.isEmpty) {
                              SnackbarWidget(
                                context,
                              ).show('Please select a vehicle type', true);
                              return;
                            }
                            if (vehicleNumberController.text.isEmpty) {
                              setState(() {
                                isVehicleNumberValid = false;
                              });

                              return;
                            }
                            setState(() {
                              isSaving = true;
                            });
                            await OnboardingFunctions(context)
                                .saveVehicleDetails(
                                  vehicleData.firstWhere(
                                    (element) =>
                                        element['name'] == selectedVehicle,
                                  )['id'],
                                  vehicleNumberController.text,
                                )
                                .then((success) {
                                  setState(() {
                                    isSaving = false;
                                  });
                                  if (success) {
                                    Navigator.pushNamed(
                                      context,
                                      '/vehicle-verification',
                                    );
                                  }
                                });
                          },
                          color: const Color(0xFF4285F4),
                          textColor: Colors.white,
                          width: double.infinity,
                          height: 50,
                          isLoading: isSaving,
                        ),
                      ],
                    )
                    .animate(
                      delay: Duration(milliseconds: vehicleTypes.length * 100),
                    )
                    .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                    .slideY(
                      begin: 0.3,
                      end: 0,
                      duration: 600.ms,
                      curve: Curves.easeInOut,
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

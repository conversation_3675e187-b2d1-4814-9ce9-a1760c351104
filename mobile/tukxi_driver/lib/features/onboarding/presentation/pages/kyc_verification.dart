import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import 'package:tukxi_driver/core/utils/camera_capture.dart';
import 'package:tukxi_driver/core/utils/file_selector.dart';
import 'package:tukxi_driver/shared/widgets/button_widget.dart';
import 'package:tukxi_driver/shared/widgets/textfield.dart';

class KycVerification extends StatefulWidget {
  const KycVerification({super.key});

  @override
  State<KycVerification> createState() => _KycVerificationState();
}

class _KycVerificationState extends State<KycVerification>
    with SingleTickerProviderStateMixin {
  File? aadharFile;
  File? drivingLicenseFile;
  String aadharFileName = '';
  String drivingLicenseFileName = '';
  String aadharFileSize = '';
  String drivingLicenseFileSize = '';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Verification',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'KYC Verification',
                style: Theme.of(
                  context,
                ).textTheme.headlineMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 8),
              Text(
                'Verify your identity documents.',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 20),
              Text(
                'Aadhar Card',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 10),
              Container(
                    width: double.infinity,
                    height: 48,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          width: 1,
                          strokeAlign: BorderSide.strokeAlignCenter,
                          color: Colors.black.withValues(alpha: 0.25),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 10,
                      children: [
                        Icon(
                          Icons.cloud_download_outlined,
                          color: Colors.black,
                        ),
                        Text(
                          'Fetch from DigiLocker',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(color: Colors.black),
                        ),
                      ],
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
              Row(
                    spacing: 15,
                    children: [
                      Expanded(child: Container(height: 1, color: Colors.grey)),
                      Text(
                        'Or Upload Manually',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Expanded(child: Container(height: 1, color: Colors.grey)),
                    ],
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
              Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 24,
                    ),
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          width: 1,
                          strokeAlign: BorderSide.strokeAlignCenter,
                          color: Colors.black.withValues(alpha: 0.25),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.cloud_upload_outlined,
                          size: 50,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 10),
                        Text(
                          'Upload Document',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        SizedBox(height: 10),
                        Text(
                          'Supported PDF, JPG, PNG. Max size: 5MB.',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        SizedBox(height: 20),
                        GestureDetector(
                          onTap: () {
                            FileSelector().selectFile(context).then((file) {
                              if (file != null) {
                                setState(() {
                                  aadharFile = file;
                                  var info = FileSelector().getFileInfo(file);
                                  aadharFileSize = '${info['sizeInKB']} KB';
                                  aadharFileName = info['name'];
                                });
                              }
                            });
                          },
                          child: Container(
                            width: double.infinity,
                            height: 48,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  width: 1,
                                  strokeAlign: BorderSide.strokeAlignCenter,
                                  color: Colors.black.withValues(alpha: 0.25),
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              spacing: 10,
                              children: [
                                Icon(
                                  Icons.attach_file_outlined,
                                  color: Colors.black,
                                  size: 20,
                                ),
                                Text(
                                  'Choose File',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall
                                      ?.copyWith(color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        GestureDetector(
                          onTap: () async {
                            aadharFile = await CameraCapture().capturePhoto();
                            setState(() {
                              var info = FileSelector().getFileInfo(
                                aadharFile!,
                              );
                              aadharFileSize = '${info['sizeInKB']} KB';
                              aadharFileName =
                                  '${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.jpg';
                            });
                          },
                          child: Container(
                            width: double.infinity,
                            height: 48,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  width: 1,
                                  strokeAlign: BorderSide.strokeAlignCenter,
                                  color: Colors.black.withValues(alpha: 0.25),
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              spacing: 10,
                              children: [
                                Icon(
                                  Icons.camera_alt_outlined,
                                  color: Colors.black,
                                  size: 20,
                                ),
                                Text(
                                  'Take a Photo',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall
                                      ?.copyWith(color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
              if (aadharFile != null)
                Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 5,
                        vertical: 10,
                      ),
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                            width: 1,
                            strokeAlign: BorderSide.strokeAlignCenter,
                            color: Colors.black.withValues(alpha: 0.25),
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Row(
                        spacing: 10,
                        children: [
                          Icon(
                            Icons.file_present_rounded,
                            color: Colors.black.withValues(alpha: 0.5),
                            size: 50,
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  aadharFileName,
                                  style: Theme.of(context).textTheme.bodyLarge
                                      ?.copyWith(color: Colors.black),
                                ),
                                Text(
                                  aadharFileSize,
                                  style: Theme.of(context).textTheme.bodyLarge
                                      ?.copyWith(color: Colors.black),
                                ),
                                SizedBox(height: 10),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      aadharFile = null;
                                      aadharFileSize = '';
                                      aadharFileName = '';
                                    });
                                  },
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.delete_outline_rounded,
                                        color: Colors.red,
                                        size: 20,
                                      ),
                                      Text(
                                        'Remove',
                                        style: Theme.of(context)
                                            .textTheme
                                            .headlineSmall
                                            ?.copyWith(color: Colors.red),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                    .animate(delay: Duration(milliseconds: 100))
                    .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                    .slideY(
                      begin: 0.3,
                      end: 0,
                      duration: 600.ms,
                      curve: Curves.easeInOut,
                    ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Aadhar Number',
                    controller: TextEditingController(),
                    isMandetory: true,
                    isCapitalize: true,
                    keyboardType: TextInputType.number,
                    maxLength: 16,
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
              SizedBox(height: 20),
              Text(
                'Driving License',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 10),
              Container(
                    width: double.infinity,
                    height: 48,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          width: 1,
                          strokeAlign: BorderSide.strokeAlignCenter,
                          color: Colors.black.withValues(alpha: 0.25),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 10,
                      children: [
                        Icon(
                          Icons.cloud_download_outlined,
                          color: Colors.black,
                        ),
                        Text(
                          'Fetch from DigiLocker',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(color: Colors.black),
                        ),
                      ],
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
              Row(
                    spacing: 15,
                    children: [
                      Expanded(child: Container(height: 1, color: Colors.grey)),
                      Text(
                        'Or Upload Manually',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Expanded(child: Container(height: 1, color: Colors.grey)),
                    ],
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
              Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 24,
                    ),
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          width: 1,
                          strokeAlign: BorderSide.strokeAlignCenter,
                          color: Colors.black.withValues(alpha: 0.25),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.cloud_upload_outlined,
                          size: 50,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 10),
                        Text(
                          'Upload Document',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        SizedBox(height: 10),
                        Text(
                          'Supported PDF, JPG, PNG. Max size: 5MB.',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        SizedBox(height: 20),
                        GestureDetector(
                          onTap: () {
                            FileSelector().selectFile(context).then((file) {
                              if (file != null) {
                                setState(() {
                                  drivingLicenseFile = file;
                                  var info = FileSelector().getFileInfo(file);
                                  drivingLicenseFileSize =
                                      '${info['sizeInKB']} KB';
                                  drivingLicenseFileName = info['name'];
                                });
                              }
                            });
                          },
                          child: Container(
                            width: double.infinity,
                            height: 48,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  width: 1,
                                  strokeAlign: BorderSide.strokeAlignCenter,
                                  color: Colors.black.withValues(alpha: 0.25),
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              spacing: 10,
                              children: [
                                Icon(
                                  Icons.attach_file_outlined,
                                  color: Colors.black,
                                  size: 20,
                                ),
                                Text(
                                  'Choose File',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall
                                      ?.copyWith(color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        GestureDetector(
                          onTap: () async {
                            drivingLicenseFile = await CameraCapture()
                                .capturePhoto();
                            setState(() {
                              var info = FileSelector().getFileInfo(
                                drivingLicenseFile!,
                              );
                              drivingLicenseFileSize = '${info['sizeInKB']} KB';
                              drivingLicenseFileName =
                                  '${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.jpg';
                            });
                          },
                          child: Container(
                            width: double.infinity,
                            height: 48,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  width: 1,
                                  strokeAlign: BorderSide.strokeAlignCenter,
                                  color: Colors.black.withValues(alpha: 0.25),
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              spacing: 10,
                              children: [
                                Icon(
                                  Icons.camera_alt_outlined,
                                  color: Colors.black,
                                  size: 20,
                                ),
                                Text(
                                  'Take a Photo',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall
                                      ?.copyWith(color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
              if (drivingLicenseFile != null)
                Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 5,
                        vertical: 10,
                      ),
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                            width: 1,
                            strokeAlign: BorderSide.strokeAlignCenter,
                            color: Colors.black.withValues(alpha: 0.25),
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Row(
                        spacing: 10,
                        children: [
                          Icon(
                            Icons.file_present_rounded,
                            color: Colors.black.withValues(alpha: 0.5),
                            size: 50,
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  drivingLicenseFileName,
                                  style: Theme.of(context).textTheme.bodyLarge
                                      ?.copyWith(color: Colors.black),
                                ),
                                Text(
                                  drivingLicenseFileSize,
                                  style: Theme.of(context).textTheme.bodyLarge
                                      ?.copyWith(color: Colors.black),
                                ),
                                SizedBox(height: 10),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      drivingLicenseFile = null;
                                      drivingLicenseFileSize = '';
                                      drivingLicenseFileName = '';
                                    });
                                  },
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.delete_outline_rounded,
                                        color: Colors.red,
                                        size: 20,
                                      ),
                                      Text(
                                        'Remove',
                                        style: Theme.of(context)
                                            .textTheme
                                            .headlineSmall
                                            ?.copyWith(color: Colors.red),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                    .animate(delay: Duration(milliseconds: 100))
                    .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                    .slideY(
                      begin: 0.3,
                      end: 0,
                      duration: 600.ms,
                      curve: Curves.easeInOut,
                    ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Driving License Number',
                    controller: TextEditingController(),
                    isMandetory: true,
                    isCapitalize: true,
                    keyboardType: TextInputType.number,
                    maxLength: 16,
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 20),
              ButtonWidget(
                    text: 'Continue',
                    onPressed: () {
                      Navigator.pushNamed(context, '/kyc-status');
                    },
                    color: const Color(0xFF4285F4),
                    textColor: Colors.white,
                    width: double.infinity,
                    height: 50,
                    isLoading: false,
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),

              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}

import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tukxi_driver/features/onboarding/presentation/pages/flutter_liveness_detection.dart';
import 'package:tukxi_driver/shared/widgets/button_widget.dart';

class ProfilePhoto extends StatefulWidget {
  const ProfilePhoto({super.key});

  @override
  State<ProfilePhoto> createState() => _ProfilePhotoState();
}

class _ProfilePhotoState extends State<ProfilePhoto> {
  File? imageFile;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Profile',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Photo Upload',
                style: Theme.of(
                  context,
                ).textTheme.headlineMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 8),
              Text(
                'Add your photo to help others recognize you.',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 20),
              Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 24,
                    ),
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          width: 1,
                          strokeAlign: BorderSide.strokeAlignCenter,
                          color: Colors.black.withValues(alpha: 0.25),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Column(
                      children: [
                        Container(
                          decoration: ShapeDecoration(
                            color: const Color(0xFFE8E8E8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(98),
                            ),
                          ),
                          child: imageFile == null
                              ? Icon(
                                  Icons.account_circle_outlined,
                                  size: 100,
                                  color: Colors.grey[400],
                                )
                              : CircleAvatar(
                                  radius: 50,
                                  backgroundImage: FileImage(imageFile!),
                                ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Profile Photo',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.black, fontSize: 18.sp),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Make sure your face is clearly visible and well lit.',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.black, fontSize: 12.sp),
                        ),
                        SizedBox(height: 20),
                        GestureDetector(
                          onTap: () async {
                            final cameras = await availableCameras();
                            if (cameras.isNotEmpty) {
                              final XFile? result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const FlutterLivenessDetection(),
                                ),
                              );

                              if (result != null) {
                                setState(() {
                                  imageFile = File(result.path);
                                });

                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Verification Successful!'),
                                  ),
                                );
                              }
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Camera not active!'),
                                ),
                              );
                            }
                          },
                          child: Container(
                            width: double.infinity,
                            height: 48,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  width: 1,
                                  strokeAlign: BorderSide.strokeAlignCenter,
                                  color: Colors.black.withValues(alpha: 0.25),
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.camera_alt_outlined,
                                  color: Colors.black,
                                  size: 20,
                                ),
                                SizedBox(width: 10),
                                Text(
                                  'Take a Photo',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall
                                      ?.copyWith(color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 100))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideX(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),

              SizedBox(height: 20),
              ButtonWidget(
                    text: 'Complete Setup',
                    onPressed: () {
                      Navigator.pushNamed(context, '/verification-status');
                    },
                    color: const Color(0xFF4285F4),
                    textColor: Colors.white,
                    width: double.infinity,
                    height: 50,
                    isLoading: false,
                  )
                  .animate(delay: Duration(milliseconds: 200))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
            ],
          ),
        ),
      ),
    );
  }
}

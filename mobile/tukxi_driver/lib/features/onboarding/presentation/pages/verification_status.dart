import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tukxi_driver/features/onboarding/presentation/widgets/verification_card.dart';

class VerificationStatus extends StatefulWidget {
  const VerificationStatus({super.key});

  @override
  State<VerificationStatus> createState() => _VerificationStatusState();
}

class _VerificationStatusState extends State<VerificationStatus> {
  final List<Map<String, dynamic>> verificationItems = [
    {'title': 'Aadhaar', 'info': '', 'status': 'Verified'},
    {'title': 'Driving License', 'info': 'ABCDE1234F', 'status': 'Pending'},
    {
      'title': 'PUC',
      'info': '1234-**************-**************-5678-9012',
      'status': 'Rejected',
    },
    {'title': 'Voter ID', 'info': 'ABCDE1234F', 'status': 'Under Review'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Verification',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              ListView.builder(
                itemCount: verificationItems.length,
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  final item = verificationItems[index];
                  return VerificationCard(
                        title: item['title'],
                        info: item['info'],
                        status: item['status'],
                        onActionPressed: () {},
                      )
                      .animate(delay: Duration(milliseconds: index * 100))
                      .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                      .slideX(
                        begin: 0.3,
                        end: 0,
                        duration: 600.ms,
                        curve: Curves.easeInOut,
                      );
                },
              ),
              const SizedBox(height: 20),
              Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 24,
                    ),
                    decoration: ShapeDecoration(
                      color: const Color(0xFFF3F3F3),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          'Need Help with Verification?',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        Text(
                          'Our support team is available 24/7 to help with document verification issues.',
                          style: Theme.of(context).textTheme.bodySmall,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Container(
                          width: double.infinity,
                          height: 44,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                          decoration: ShapeDecoration(
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.support_agent_outlined, size: 18.sp),
                              SizedBox(width: 8),
                              Text(
                                'Call Support',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          width: double.infinity,
                          height: 44,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                          decoration: ShapeDecoration(
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.email_outlined, size: 18.sp),
                              SizedBox(width: 8),
                              Text(
                                'Email Support',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                  .animate(
                    delay: Duration(
                      milliseconds: verificationItems.length * 100,
                    ),
                  )
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideX(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
            ],
          ),
        ),
      ),
    );
  }
}

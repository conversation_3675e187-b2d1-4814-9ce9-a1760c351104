import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:tukxi_driver/core/utils/file_selector.dart';
import 'package:tukxi_driver/features/onboarding/presentation/widgets/certificate_card.dart';
import 'package:tukxi_driver/features/onboarding/presentation/widgets/upload_noc_card.dart';
import 'package:tukxi_driver/shared/widgets/button_widget.dart';
import 'package:tukxi_driver/shared/widgets/textfield.dart';

class VehicleVerification extends StatefulWidget {
  const VehicleVerification({super.key});

  @override
  State<VehicleVerification> createState() => _VehicleVerificationState();
}

class _VehicleVerificationState extends State<VehicleVerification> {
  List<Map<String, String>> certificates = [
    {'title': 'Registration Certificate', 'status': 'Verified'},
    {'title': 'Insurance Certificate', 'status': 'Not Verified'},
    {'title': 'PUC Certificate', 'status': 'Pending'},
  ];
  File? nocFile;
  String fileSize = '';
  String fileName = '';
  DateTime? rtoExpiry;
  DateTime? taxExpity;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Image.asset('assets/icons/arrow-back.png'),
            ),
          ),
        ),
        title: Text(
          'Vehicle Information',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vehicle Verification',
                style: Theme.of(
                  context,
                ).textTheme.headlineMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 8),
              Text(
                'Verify data fetched from Parivahan.',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 20),
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: certificates.length,
                padding: EdgeInsets.zero,
                itemBuilder: (context, index) {
                  return CertificateCard(
                        title: certificates[index]['title']!,
                        status: certificates[index]['status']!,
                      )
                      .animate(delay: Duration(milliseconds: 100 * index))
                      .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                      .slideY(
                        begin: 0.3,
                        end: 0,
                        duration: 600.ms,
                        curve: Curves.easeInOut,
                      );
                },
              ),
              SizedBox(height: 20),
              Text(
                'Registration Information',
                style: Theme.of(
                  context,
                ).textTheme.headlineMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Registration Number',
                    controller: TextEditingController(),
                    isMandetory: false,
                    isCapitalize: true,
                  )
                  .animate(delay: Duration(milliseconds: 200))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Owner Name',
                    controller: TextEditingController(),
                    isMandetory: false,
                    isCapitalize: true,
                  )
                  .animate(delay: Duration(milliseconds: 250))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Model',
                    controller: TextEditingController(),
                    isMandetory: false,
                    isCapitalize: true,
                  )
                  .animate(delay: Duration(milliseconds: 300))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              Row(
                spacing: 10,
                children: [
                  Expanded(
                    flex: 2,
                    child:
                        BuildField(
                              label: 'Year',
                              controller: TextEditingController(),
                              isMandetory: false,
                              isCapitalize: true,
                              keyboardType: TextInputType.number,
                              maxLength: 4,
                            )
                            .animate(delay: Duration(milliseconds: 350))
                            .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                            .slideY(
                              begin: 0.3,
                              end: 0,
                              duration: 600.ms,
                              curve: Curves.easeInOut,
                            ),
                  ),
                  Expanded(
                    flex: 2,
                    child:
                        BuildField(
                              label: 'Fuel Type',
                              controller: TextEditingController(),
                              isMandetory: false,
                              isCapitalize: true,
                            )
                            .animate(delay: Duration(milliseconds: 400))
                            .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                            .slideY(
                              begin: 0.3,
                              end: 0,
                              duration: 600.ms,
                              curve: Curves.easeInOut,
                            ),
                  ),
                ],
              ),
              SizedBox(height: 10),
              BuildField(
                    label: 'RTO',
                    controller: TextEditingController(),
                    isMandetory: false,
                    isCapitalize: true,
                  )
                  .animate(delay: Duration(milliseconds: 450))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              Text(
                    'RC Expiry Date',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.black),
                  )
                  .animate(delay: Duration(milliseconds: 300))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 5),
              GestureDetector(
                    onTap: () {
                      showDatePicker(
                        context: context,
                        initialDate:
                            rtoExpiry ??
                            DateTime.now().subtract(Duration(days: 6570)),
                        firstDate: DateTime(1900),
                        lastDate: DateTime.now().subtract(Duration(days: 6570)),
                        builder: (BuildContext context, Widget? child) {
                          return Theme(
                            data: ThemeData(
                              primaryColor: const Color(
                                0xFF4285F4,
                              ), // Set your desired primary color
                              colorScheme: ColorScheme.light(
                                primary: const Color(0xFF4285F4),
                              ),
                            ),
                            child: child!,
                          );
                        },
                      ).then((date) {
                        if (date != null) {
                          setState(() {
                            rtoExpiry = date;
                          });
                        }
                      });
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withValues(alpha: 0.2),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: EdgeInsets.only(left: 16, right: 16),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          rtoExpiry != null
                              ? DateFormat('dd/MM/yyyy').format(rtoExpiry!)
                              : 'DD/MM/YYYY',
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(
                                color: rtoExpiry != null
                                    ? Colors.black
                                    : Colors.black.withValues(alpha: 0.6),
                                fontSize: 14.sp,
                              ),
                        ),
                      ),
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 350))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              Text(
                    'Vehicle Tax Upto',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.black),
                  )
                  .animate(delay: Duration(milliseconds: 300))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 5),
              GestureDetector(
                    onTap: () {
                      showDatePicker(
                        context: context,
                        initialDate:
                            taxExpity ??
                            DateTime.now().subtract(Duration(days: 6570)),
                        firstDate: DateTime(1900),
                        lastDate: DateTime.now().subtract(Duration(days: 6570)),
                        builder: (BuildContext context, Widget? child) {
                          return Theme(
                            data: ThemeData(
                              primaryColor: const Color(
                                0xFF4285F4,
                              ), // Set your desired primary color
                              colorScheme: ColorScheme.light(
                                primary: const Color(0xFF4285F4),
                              ),
                            ),
                            child: child!,
                          );
                        },
                      ).then((date) {
                        if (date != null) {
                          setState(() {
                            taxExpity = date;
                          });
                        }
                      });
                    },
                    child: Container(
                      height: 50,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withValues(alpha: 0.2),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: EdgeInsets.only(left: 16, right: 16),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          taxExpity != null
                              ? DateFormat('dd/MM/yyyy').format(taxExpity!)
                              : 'DD/MM/YYYY',
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(
                                color: taxExpity != null
                                    ? Colors.black
                                    : Colors.black.withValues(alpha: 0.6),
                                fontSize: 14.sp,
                              ),
                        ),
                      ),
                    ),
                  )
                  .animate(delay: Duration(milliseconds: 350))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              SizedBox(height: 20),
              Text(
                'Insurance Information',
                style: Theme.of(
                  context,
                ).textTheme.headlineMedium?.copyWith(color: Colors.black),
              ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Insurance Company',
                    controller: TextEditingController(),
                    isMandetory: false,
                    isCapitalize: true,
                  )
                  .animate(delay: Duration(milliseconds: 200))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              BuildField(
                    label: 'Policy Number',
                    controller: TextEditingController(),
                    isMandetory: false,
                    isCapitalize: true,
                  )
                  .animate(delay: Duration(milliseconds: 250))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),
              SizedBox(height: 10),
              Row(
                spacing: 10,
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                              'Policy Start Date',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: Colors.black),
                            )
                            .animate(delay: Duration(milliseconds: 300))
                            .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                            .slideY(
                              begin: 0.3,
                              end: 0,
                              duration: 600.ms,
                              curve: Curves.easeInOut,
                            ),
                        SizedBox(height: 5),
                        GestureDetector(
                              onTap: () {
                                showDatePicker(
                                  context: context,
                                  initialDate: DateTime.now().subtract(
                                    Duration(days: 200),
                                  ),
                                  firstDate: DateTime(1900),
                                  lastDate: DateTime.now(),

                                  builder:
                                      (BuildContext context, Widget? child) {
                                        return Theme(
                                          data: ThemeData(
                                            primaryColor: const Color(
                                              0xFF4285F4,
                                            ), // Set your desired primary color
                                            colorScheme: ColorScheme.light(
                                              primary: const Color(0xFF4285F4),
                                            ),
                                          ),
                                          child: child!,
                                        );
                                      },
                                ).then((date) {
                                  if (date != null) {}
                                });
                              },
                              child: Container(
                                height: 50,
                                decoration: BoxDecoration(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.secondary,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding: EdgeInsets.only(left: 16, right: 16),
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    DateFormat('dd/MM/yyyy').format(
                                      DateTime.now().subtract(
                                        Duration(days: 200),
                                      ),
                                    ),
                                    style: Theme.of(context).textTheme.bodyLarge
                                        ?.copyWith(
                                          color: Colors.black,
                                          fontSize: 14.sp,
                                        ),
                                  ),
                                ),
                              ),
                            )
                            .animate(delay: Duration(milliseconds: 350))
                            .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                            .slideY(
                              begin: 0.3,
                              end: 0,
                              duration: 600.ms,
                              curve: Curves.easeInOut,
                            ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                              'Policy End Date',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: Colors.black),
                            )
                            .animate(delay: Duration(milliseconds: 300))
                            .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                            .slideY(
                              begin: 0.3,
                              end: 0,
                              duration: 600.ms,
                              curve: Curves.easeInOut,
                            ),
                        SizedBox(height: 5),
                        GestureDetector(
                              onTap: () {
                                showDatePicker(
                                  context: context,
                                  initialDate: DateTime.now().subtract(
                                    Duration(days: 200),
                                  ),
                                  firstDate: DateTime(1900),
                                  lastDate: DateTime.now(),
                                  builder:
                                      (BuildContext context, Widget? child) {
                                        return Theme(
                                          data: ThemeData(
                                            primaryColor: const Color(
                                              0xFF4285F4,
                                            ), // Set your desired primary color
                                            colorScheme: ColorScheme.light(
                                              primary: const Color(0xFF4285F4),
                                            ),
                                          ),
                                          child: child!,
                                        );
                                      },
                                ).then((date) {
                                  if (date != null) {}
                                });
                              },
                              child: Container(
                                height: 50,
                                decoration: BoxDecoration(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.secondary,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding: EdgeInsets.only(left: 16, right: 16),
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    DateFormat('dd/MM/yyyy').format(
                                      DateTime.now().subtract(
                                        Duration(days: 200),
                                      ),
                                    ),
                                    style: Theme.of(context).textTheme.bodyLarge
                                        ?.copyWith(
                                          color: Colors.black,
                                          fontSize: 14.sp,
                                        ),
                                  ),
                                ),
                              ),
                            )
                            .animate(delay: Duration(milliseconds: 350))
                            .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                            .slideY(
                              begin: 0.3,
                              end: 0,
                              duration: 600.ms,
                              curve: Curves.easeInOut,
                            ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20),
              UploadNocCard(
                onTap: () async {
                  await FileSelector().selectFile(context).then((file) {
                    if (file != null) {
                      setState(() {
                        nocFile = file;
                        var info = FileSelector().getFileInfo(file);
                        fileSize = '${info['sizeInKB']} KB';
                        fileName = info['name'];
                      });
                    }
                  });
                },
                isUploaded: nocFile != null,
              ),
              SizedBox(height: 10),
              if (nocFile != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 5,
                    vertical: 10,
                  ),
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 1,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: Colors.black.withValues(alpha: 0.25),
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Row(
                    spacing: 10,
                    children: [
                      Icon(
                        Icons.file_present_rounded,
                        color: Colors.black.withValues(alpha: 0.5),
                        size: 50,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              fileName,
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(color: Colors.black),
                            ),
                            Text(
                              fileSize,
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(color: Colors.black),
                            ),
                            SizedBox(height: 10),
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  nocFile = null;
                                  fileSize = '';
                                  fileName = '';
                                });
                              },
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.delete_outline_rounded,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                  Text(
                                    'Remove',
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineSmall
                                        ?.copyWith(color: Colors.red),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 20),
              ButtonWidget(
                    text: 'Continue',
                    onPressed: () {
                      Navigator.pushNamed(context, '/kyc-verification');
                    },
                    color: const Color(0xFF4285F4),
                    textColor: Colors.white,
                    width: double.infinity,
                    height: 50,
                    isLoading: false,
                  )
                  .animate(delay: Duration(milliseconds: 800))
                  .fadeIn(duration: 600.ms, curve: Curves.easeInOut)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                  ),

              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}

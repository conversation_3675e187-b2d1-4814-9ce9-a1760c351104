import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:tukxi_driver/main.dart';

class PhotoUploadScreen extends StatefulWidget {
  const PhotoUploadScreen({super.key});

  @override
  State<PhotoUploadScreen> createState() => _PhotoUploadScreenState();
}

class _PhotoUploadScreenState extends State<PhotoUploadScreen> {
  File? _imageFile;

  Future<void> _takePhoto() async {
    // Navigate to the liveness check screen and wait for a result.
    final result = await Navigator.of(context).push<String>(
      MaterialPageRoute(builder: (context) => const LivenessCheckScreen()),
    );

    // If a photo path was returned, update the state to display it.
    if (result != null) {
      setState(() {
        _imageFile = File(result);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: const Icon(Icons.arrow_back, color: Colors.black),
        title: const Text('Profile', style: TextStyle(color: Colors.black)),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Photo Upload',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Add your photo to help others recognize you.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 40),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.grey.shade300,
                    style: BorderStyle.solid,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Display the captured image or a placeholder
                    _imageFile != null
                        ? CircleAvatar(
                            radius: 80,
                            backgroundImage: FileImage(_imageFile!),
                          )
                        : const CircleAvatar(
                            radius: 80,
                            backgroundColor: Color(0xFFE0E0E0),
                            child: Icon(
                              Icons.person,
                              size: 80,
                              color: Colors.white,
                            ),
                          ),
                    const SizedBox(height: 16),
                    const Text(
                      'Profile Photo',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Make sure your face is clearly visible and well lit',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 24),
                    OutlinedButton.icon(
                      onPressed: _takePhoto,
                      icon: const Icon(Icons.camera_alt_outlined),
                      label: const Text('Take a Photo'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 24,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _imageFile != null
                  ? () {
                      // Handle setup completion
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4A47FF),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                disabledBackgroundColor: Colors.grey.shade300,
              ),
              child: const Text(
                'Complete Setup',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// liveness_check_screen.dart

class LivenessCheckScreen extends StatefulWidget {
  const LivenessCheckScreen({super.key});

  @override
  State<LivenessCheckScreen> createState() => _LivenessCheckScreenState();
}

class _LivenessCheckScreenState extends State<LivenessCheckScreen> {
  CameraController? _cameraController;
  FaceDetector? _faceDetector;
  bool _isCameraInitialized = false;
  bool _isDetecting = false;

  // Liveness detection state
  bool _faceFound = false;
  bool _eyesOpened = false;
  bool _blinkDetected = false;
  String _statusMessage = "Please position your face in the frame";

  // Add debug information
  int _frameCount = 0;
  String _debugInfo = "";

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  void _showStatusSnackbar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.blue,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _initialize() async {
    try {
      _showStatusSnackbar("Initializing camera...");

      // Check if cameras are available
      if (cameras.isEmpty) {
        _showStatusSnackbar("No cameras available", isError: true);
        return;
      }

      // Initialize the camera
      final frontCamera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.front,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        frontCamera,
        ResolutionPreset
            .medium, // Changed from high to medium for better performance
        enableAudio: false,
        imageFormatGroup: Platform.isAndroid
            ? ImageFormatGroup.nv21
            : ImageFormatGroup.bgra8888,
      );

      await _cameraController!.initialize();
      if (!mounted) return;

      // Initialize the face detector with optimized settings
      _faceDetector = FaceDetector(
        options: FaceDetectorOptions(
          performanceMode: FaceDetectorMode
              .fast, // Changed to fast for better real-time performance
          enableClassification: true, // Needed for eye open probability
          enableContours: false, // Disabled for better performance
          enableLandmarks: false, // Disabled for better performance
          enableTracking: true, // Enable tracking for better performance
        ),
      );

      setState(() {
        _isCameraInitialized = true;
      });

      _showStatusSnackbar("Camera initialized successfully");

      // Start processing the camera stream for face detection
      _cameraController!.startImageStream(_processImageStream);
    } catch (e) {
      _showStatusSnackbar("Failed to initialize camera: $e", isError: true);
      print("Camera initialization error: $e");
    }
  }

  void _processImageStream(CameraImage cameraImage) async {
    if (_isDetecting || !mounted || _blinkDetected) return;

    _isDetecting = true;
    _frameCount++;

    try {
      final inputImage = _inputImageFromCameraImage(cameraImage);
      if (inputImage == null) {
        _debugInfo = "Failed to convert camera image to InputImage";
        print("Frame $_frameCount: $_debugInfo");
        _isDetecting = false;
        return;
      }

      final faces = await _faceDetector!.processImage(inputImage);

      _debugInfo = "Frame $_frameCount: Found ${faces.length} face(s)";
      print(_debugInfo);

      if (faces.isEmpty) {
        if (mounted) {
          setState(() {
            _faceFound = false;
            _statusMessage = "No face detected. Please position your face.";
          });

          // Show snackbar every 30 frames (approximately every 1 second) when no face is detected
          if (_frameCount % 30 == 0) {
            _showStatusSnackbar("Position your face in the center of the oval");
          }
        }
      } else {
        // We only care about the first face detected.
        final face = faces.first;
        _handleLivenessCheck(face);
      }
    } catch (e) {
      _debugInfo = "Error processing image stream: $e";
      print(_debugInfo);

      // Show error snackbar occasionally
      if (_frameCount % 60 == 0) {
        _showStatusSnackbar(
          "Face detection error, please try again",
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        _isDetecting = false;
      }
    }
  }

  void _handleLivenessCheck(Face face) {
    if (!mounted || _blinkDetected) return;

    setState(() {
      _faceFound = true;
    });

    final leftEyeOpenProb = face.leftEyeOpenProbability;
    final rightEyeOpenProb = face.rightEyeOpenProbability;

    if (leftEyeOpenProb == null || rightEyeOpenProb == null) {
      // If probabilities are not available, we can't perform the check.
      setState(() {
        _statusMessage = "Could not verify eyes. Try again.";
      });
      _showStatusSnackbar(
        "Eye detection not available, please move closer",
        isError: true,
      );
      return;
    }

    print(
      "Eye probabilities - Left: $leftEyeOpenProb, Right: $rightEyeOpenProb",
    );

    // Simple blink detection logic:
    // 1. Wait for eyes to be open.
    // 2. Then wait for eyes to be closed.
    // 3. Capture image.

    if (!_eyesOpened) {
      // Step 1: Check if both eyes are open
      if (leftEyeOpenProb > 0.8 && rightEyeOpenProb > 0.8) {
        setState(() {
          _eyesOpened = true;
          _statusMessage = "Great! Now blink once.";
        });
        _showStatusSnackbar("Perfect! Now please blink once");
      } else {
        setState(() {
          _statusMessage = "Please open your eyes.";
        });
      }
    } else {
      // Step 2: Check if both eyes are closed (a blink)
      if (leftEyeOpenProb < 0.2 && rightEyeOpenProb < 0.2) {
        setState(() {
          _blinkDetected = true;
          _statusMessage = "Verified! Taking photo...";
        });
        _showStatusSnackbar("Liveness verified! Capturing photo...");
        // Liveness confirmed, capture the photo
        _capturePhoto();
      }
    }
  }

  Future<void> _capturePhoto() async {
    if (_cameraController == null ||
        !_cameraController!.value.isInitialized ||
        _cameraController!.value.isTakingPicture) {
      _showStatusSnackbar("Camera not ready, please try again", isError: true);
      return;
    }

    try {
      // Stop the image stream to freeze the frame
      await _cameraController!.stopImageStream();

      // Give a slight delay for the status message to be visible
      await Future.delayed(const Duration(milliseconds: 500));

      final XFile imageFile = await _cameraController!.takePicture();

      _showStatusSnackbar("Photo captured successfully!");

      // Return the path of the captured image to the previous screen
      if (mounted) {
        Navigator.of(context).pop(imageFile.path);
      }
    } catch (e) {
      print("Error capturing photo: $e");
      _showStatusSnackbar("Failed to capture photo: $e", isError: true);

      // Reset the state to allow another attempt
      setState(() {
        _blinkDetected = false;
        _eyesOpened = false;
        _statusMessage = "Please position your face in the frame";
      });

      // Restart image stream
      if (_cameraController != null && _cameraController!.value.isInitialized) {
        _cameraController!.startImageStream(_processImageStream);
      }
    }
  }

  @override
  void dispose() {
    _cameraController?.stopImageStream().catchError((e) {
      print("Error stopping image stream: $e");
    });
    _cameraController?.dispose();
    _faceDetector?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isCameraInitialized) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text('Initializing camera...'),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  _initialize(); // Retry initialization
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          CameraPreview(_cameraController!),
          // Overlay UI
          ColorFiltered(
            colorFilter: ColorFilter.mode(
              Colors.black.withOpacity(0.5),
              BlendMode.srcOut,
            ),
            child: Stack(
              fit: StackFit.expand,
              children: [
                Container(
                  decoration: const BoxDecoration(
                    color: Colors.black,
                    backgroundBlendMode: BlendMode.dstOut,
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.8,
                    height:
                        MediaQuery.of(context).size.width *
                        0.8 *
                        1.2, // Oval-like aspect ratio
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(
                        MediaQuery.of(context).size.width * 0.4,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Manual capture button for fallback
          Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: const EdgeInsets.all(50.0),
              child: FloatingActionButton(
                mini: true,
                backgroundColor: Colors.white.withOpacity(0.8),
                onPressed: () {
                  // Manual capture as fallback
                  _showStatusSnackbar("Manual capture initiated");
                  _capturePhoto();
                },
                child: const Icon(Icons.camera_alt, color: Colors.black),
              ),
            ),
          ),
          // Status message and instructions
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 20),
              color: Colors.transparent,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _statusMessage,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Debug information
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Face: ${_faceFound ? "✓" : "✗"} | Eyes: ${_eyesOpened ? "Open" : "Closed"} | Frames: $_frameCount',
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper to convert CameraImage to InputImage
  InputImage? _inputImageFromCameraImage(CameraImage image) {
    if (_cameraController == null) {
      print("Camera controller is null");
      return null;
    }

    try {
      // get image rotation
      // it is used in android to convert the InputImage from Dart's CameraImage to MlKit's InputImage
      final camera = _cameraController!.description;
      final sensorOrientation = camera.sensorOrientation;
      InputImageRotation? rotation;

      if (Platform.isIOS) {
        rotation = InputImageRotationValue.fromRawValue(sensorOrientation);
      } else if (Platform.isAndroid) {
        var rotationCompensation = (sensorOrientation + 360) % 360;
        if (camera.lensDirection == CameraLensDirection.front) {
          // front-facing
          rotationCompensation = (360 - rotationCompensation) % 360;
        }
        rotation = InputImageRotationValue.fromRawValue(rotationCompensation);
      }

      if (rotation == null) {
        print("Could not determine image rotation");
        return null;
      }

      // get image format
      final format = InputImageFormatValue.fromRawValue(image.format.raw);

      // validate format depending on platform
      // only supported formats:
      // * nv21 for Android
      // * bgra8888 for iOS
      if (format == null) {
        print("Unsupported image format: ${image.format.raw}");
        return null;
      }

      if (Platform.isAndroid && format != InputImageFormat.nv21) {
        print("Android requires nv21 format, got: $format");
        return null;
      }

      if (Platform.isIOS && format != InputImageFormat.bgra8888) {
        print("iOS requires bgra8888 format, got: $format");
        return null;
      }

      // since format is constraint to nv21 or bgra8888, both only have one plane
      if (image.planes.length != 1) {
        print("Expected 1 plane, got: ${image.planes.length}");
        return null;
      }

      final plane = image.planes.first;

      // compose InputImage
      return InputImage.fromBytes(
        bytes: plane.bytes,
        metadata: InputImageMetadata(
          size: Size(image.width.toDouble(), image.height.toDouble()),
          rotation: rotation, // used only in android
          format: format, // used only in ios
          bytesPerRow: plane.bytesPerRow, // used only in ios
        ),
      );
    } catch (e) {
      print("Error converting camera image: $e");
      return null;
    }
  }
}

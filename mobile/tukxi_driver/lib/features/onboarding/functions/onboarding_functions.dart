import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tukxi_driver/core/constants/app_config.dart';
import 'package:tukxi_driver/features/authentication/presentation/viewmodels/auth_provider.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:tukxi_driver/shared/widgets/snackbar_widget.dart';

class OnboardingFunctions {
  BuildContext context;
  late var header;
  OnboardingFunctions(this.context) {
    header = {
      'Content-Type': 'application/json',
      'Authorization':
          'Bearer ${Provider.of<AuthProvider>(context, listen: false).token}',
      'x-app-type': 'driver',
    };
  }
  Future<bool> saveLanguage(String id) async {
    String url =
        '${AppConfig.apiBaseUrl}/api/v1/user-profile/driver-onboard/language';
    var response = await http.patch(
      Uri.parse(url),
      headers: header,
      body: json.encode({'languageId': id}),
    );
    log(response.body, name: 'OnboardingFunctions.saveLanguage');
    if (response.statusCode == 200 || response.statusCode == 201) {
      SnackbarWidget(context).show(jsonDecode(response.body)['message'], false);
      return true;
    } else {
      log(
        'Error: ${response.statusCode}',
        name: 'OnboardingFunctions.saveLanguage',
      );
      SnackbarWidget(context).show(jsonDecode(response.body)['message'], true);
      return false;
    }
  }

  Future<bool> savePersonalDetails(Map<String, dynamic> data) async {
    String url = '${AppConfig.apiBaseUrl}/api/v1/user-profile/driver-onboard';
    var response = await http.patch(
      Uri.parse(url),
      headers: header,
      body: json.encode(data),
    );
    log(response.body, name: 'OnboardingFunctions.savePersonalDetails');
    if (response.statusCode == 200 || response.statusCode == 201) {
      SnackbarWidget(context).show(jsonDecode(response.body)['message'], false);
      return true;
    } else {
      log(
        'Error: ${response.statusCode}',
        name: 'OnboardingFunctions.savePersonalDetails',
      );
      SnackbarWidget(
        context,
      ).show(jsonDecode(response.body)['message'].toString(), true);
      return false;
    }
  }

  Future<bool> saveVehicleDetails(
    String vehicleId,
    String vehicleNumber,
  ) async {
    String url = '${AppConfig.apiBaseUrl}/api/v1/driver-vehicles/vehicle';
    var newHeader = header..remove('x-app-type');
    var response = await http.patch(
      Uri.parse(url),
      headers: newHeader,
      body: json.encode({
        'vehicleTypeId': vehicleId,
        'vehicleNumber': vehicleNumber,
      }),
    );
    log(response.body, name: 'OnboardingFunctions.saveVehicleDetails');
    if (response.statusCode == 200 || response.statusCode == 201) {
      SnackbarWidget(context).show(jsonDecode(response.body)['message'], false);
      return true;
    } else {
      log(
        'Error: ${response.statusCode}',
        name: 'OnboardingFunctions.saveVehicleDetails',
      );
      SnackbarWidget(
        context,
      ).show(jsonDecode(response.body)['message'].toString(), true);
      return false;
    }
  }
}

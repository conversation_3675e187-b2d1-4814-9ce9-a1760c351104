import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:provider/provider.dart';
import 'package:tukxi_driver/features/authentication/functions/auth_functions.dart';
import 'package:tukxi_driver/shared/functions/progress_functions.dart';
import 'package:tukxi_driver/features/authentication/presentation/viewmodels/auth_provider.dart';
import 'package:flutter_animate/flutter_animate.dart';

class Splashscreen extends StatefulWidget {
  const Splashscreen({super.key});

  @override
  State<Splashscreen> createState() => _SplashscreenState();
}

class _SplashscreenState extends State<Splashscreen> {
  static var storage = const FlutterSecureStorage();

  @override
  void initState() {
    super.initState();
    BuildContext context = this.context;
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      var authProvider = Provider.of<AuthProvider>(context, listen: false);
      String token;
      token = await storage.read(key: 'token') ?? '';
      authProvider.setToken(token);
      //log(authProvider.token, name: 'Auth Token');
      String refreshToken;
      refreshToken = await storage.read(key: 'refresh') ?? '';
      authProvider.setRefreshToken(refreshToken);
      // log(authProvider.refreshToken, name: 'Refresh Token');
      String activeOn;
      activeOn = await storage.read(key: 'active_on') ?? '';
      log(activeOn, name: 'Active On');

      if (token.isNotEmpty) {
        if (DateTime.parse(activeOn)
                .toLocal()
                .toIso8601String()
                .split('T')[0]
                .compareTo(DateTime.now().toIso8601String().split('T')[0]) <
            0) {
          await AuthFunctions(context).refreshToken().then((isRefreshed) async {
            isRefreshed
                ? await storage.write(
                    key: 'active_on',
                    value: DateTime.now().toIso8601String(),
                  )
                : await AuthFunctions(context).signOut().then((value) async {
                    Navigator.pushNamedAndRemoveUntil(
                      context,
                      '/login',
                      (route) => false,
                    );
                  });
          });
        }
        await ProgressFunctions(context).getProgress().then((progress) async {
          await Future.delayed(const Duration(seconds: 1));
          Navigator.pushNamedAndRemoveUntil(
            context,
            progress,
            (route) => false,
          );
        });
      } else {
        await Future.delayed(const Duration(seconds: 2));
        Navigator.pushReplacementNamed(context, '/login');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child:
            Image.asset(
                  'assets/logo-splash.png',
                  width: MediaQuery.of(context).size.width * 0.33,
                )
                .animate()
                .fadeIn(
                  duration: const Duration(seconds: 1),
                  curve: Curves.easeInOut,
                )
                .move(
                  begin: const Offset(0, 40),
                  end: Offset.zero,
                  duration: const Duration(seconds: 1),
                ),
      ),
    );
  }
}

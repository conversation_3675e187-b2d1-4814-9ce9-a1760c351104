buildscript {
    repositories {
        google()       
        mavenCentral() 
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.5.0")
        classpath("com.google.gms:google-services:4.4.3")
        classpath("com.google.firebase:firebase-crashlytics-gradle:2.9.9")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// Optional build directory customization
val newBuildDir = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.set(newBuildDir)

subprojects {
    val newSubprojectBuildDir = newBuildDir.dir(project.name)
    project.layout.buildDirectory.set(newSubprojectBuildDir)
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}

afterEvaluate {
    subprojects {
        if (name == "country_codes") {
            extensions.findByType<com.android.build.gradle.LibraryExtension>()?.apply {
                defaultConfig {
                    minSdk = 23
                }
            }
        }
    }
}

# Miscellaneous
*.class
*.pyc
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/


# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json


# Flutter & Dart
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
.packages
build/
pubspec.lock

# iOS
ios/Flutter/Generated.xcconfig
ios/Flutter/flutter_assets/
ios/.symlinks/
ios/Pods/
ios/.dart_tool/
ios/Runner.xcworkspace/
ios/Podfile.lock

# Android
android/.gradle/
android/app/build/
android/app/*.iml
android/local.properties
android/key.properties
/android/app/debug
/android/app/profile
/android/app/release

# macOS
macos/Flutter/ephemeral/
macos/.dart_tool/
macos/Pods/
macos/Podfile.lock

# Windows/Linux/macOS System Files
.DS_Store
Thumbs.db

# IDE & Editor Files
.idea/
*.iml
.vscode/

# Web & Firebase
web/.dart_tool/
web/.firebase/
firebase-debug.log
functions/node_modules/
functions/.firebase/

# Secrets & Environment Variables
*.env
.env.dev
.env.prod
.env.secrets
.env.staging

# Debug & Temp Files
*.log
*.temp
*.swp
*.swo
*.swn

linux/

import 'package:tukxi/core/animations/page_transitions.dart';
import 'package:tukxi/features/auth/domain/params/auth_screen_params.dart';
import 'package:tukxi/features/auth/presentation/screens/accept_screen.dart';
import 'package:tukxi/features/auth/presentation/screens/login_screen.dart';
import 'package:tukxi/features/auth/presentation/screens/onboarding_screen.dart';
import 'package:tukxi/features/auth/presentation/screens/otp_screen.dart';
import 'package:tukxi/features/splash/presentation/screens/splash_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/features/tabbar/presentation/screens/tabbar_screen.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String otp = '/otp';
  static const String tabbar = '/tabbar';
  static const String onboarding = '/onboarding';
  static const String accept = '/accept';
}

final router = GoRouter(
  initialLocation: AppRoutes.splash,
  routes: [
    GoRoute(
      path: AppRoutes.splash,
      name: 'splash',
      pageBuilder: (context, state) =>
          SlideTransitionPage(child: const SplashScreen()),
    ),
    GoRoute(
      path: AppRoutes.login,
      name: 'login',
      pageBuilder: (context, state) =>
          FadeTransitionPage(child: const LoginScreen()),
    ),
    GoRoute(
      path: AppRoutes.otp,
      name: 'otp',
      pageBuilder: (context, state) {
        final params = state.extra as AuthScreenParams;
        return FadeTransitionPage(child: OtpScreen(params: params));
      },
    ),
    GoRoute(
      path: AppRoutes.tabbar,
      name: 'tabbar',
      pageBuilder: (context, state) =>
          FadeTransitionPage(child: TabbarScreen()),
    ),
    GoRoute(
      path: AppRoutes.onboarding,
      name: 'onboarding',
      pageBuilder: (context, state) {
        final params = state.extra as AuthScreenParams;
        return FadeTransitionPage(child: OnboardingScreen(params: params));
      },
    ),
    GoRoute(
      path: AppRoutes.accept,
      name: 'accept',
      pageBuilder: (context, state) {
        final params = state.extra as AuthScreenParams;
        return FadeTransitionPage(child: AcceptScreen(params: params));
      },
    ),
  ],
);

import 'package:country_codes/country_codes.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';

class LocationService {
  static Future<Position?> getCurrentPosition() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) return null;

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) return null;
    }

    if (permission == LocationPermission.deniedForever) return null;

    return await Geolocator.getCurrentPosition(
      locationSettings: LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // meters
      ),
    );
  }

  static Future<String?> getCountryCodeFromPosition(Position position) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );
      return placemarks.first.isoCountryCode;
    } catch (e) {
      return null;
    }
  }

  static Future<String?> getDialCode(String countryCode) async {
    await CountryCodes.init(); // call once in main()
    Locale locale = Locale.fromSubtags(countryCode: countryCode);
    final details = CountryCodes.detailsForLocale(locale);
    return details.dialCode;
  }

  static Future<String?> getUserDialCode() async {
    final position = await getCurrentPosition();
    if (position != null) {
      final countryCode = await getCountryCodeFromPosition(position);
      if (countryCode != null) {
        return countryCode; //await getDialCode(countryCode);
      }
    }
    return null;
  }
}

import 'package:tukxi/core/constants/shared_preference_constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A service class for managing shared preferences in the application.
///
/// This class follows the singleton pattern to ensure a single instance
/// is used throughout the application. It provides methods to initialize
/// the shared preferences and manage specific preference values such as
/// the logged-in status.
class SharedPreferenceService {
  /// Singleton instance of the [SharedPreferenceService].
  static final SharedPreferenceService _instance =
      SharedPreferenceService._internal();

  /// Factory constructor to return the singleton instance.
  factory SharedPreferenceService() => _instance;

  /// Private named constructor for internal use.
  SharedPreferenceService._internal();

  /// Instance of [SharedPreferences] used to store and retrieve data.
  late SharedPreferences _prefs;

  /// Initializes the shared preferences instance.
  ///
  /// This method must be called before accessing any shared preferences
  /// to ensure the instance is properly initialized.
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Gets the logged-in status of the user.
  bool get isLoggedIn {
    return _prefs.getBool(SharedPreferenceConstants.isLoggedIn) ?? false;
  }

  /// Sets the logged-in status of the user.
  set isLoggedIn(bool value) {
    _prefs.setBool(SharedPreferenceConstants.isLoggedIn, value);
  }

  /// Gets the token of the user.
  String get token {
    return _prefs.getString(SharedPreferenceConstants.token) ?? '';
  }

  /// Sets the token of the user.
  set token(String value) {
    _prefs.setString(SharedPreferenceConstants.token, value);
  }

  /// Gets the refresh token of the user.
  String get refreshToken {
    return _prefs.getString(SharedPreferenceConstants.refreshToken) ?? '';
  }

  /// Sets the refresh token of the user.
  set refreshToken(String value) {
    _prefs.setString(SharedPreferenceConstants.refreshToken, value);
  }

  /// Clears all the shared preferences.
  Future<void> clearSharedPreference() async {
    await _prefs.clear();
  }
}

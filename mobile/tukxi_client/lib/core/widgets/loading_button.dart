import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class LoadingButton extends StatelessWidget {
  final bool isLoading;
  final VoidCallback? onPressed;
  final String text;
  final double? width;
  final double? height;
  final double? padding;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool enableBorder;
  final BoxBorder? border;
  final double borderRadius;
  final SvgPicture? icon;

  const LoadingButton({
    super.key,
    required this.isLoading,
    required this.onPressed,
    required this.text,
    this.width,
    this.height,
    this.padding = 14,
    this.backgroundColor = AppColors.primary,
    this.foregroundColor = Colors.white,
    this.enableBorder = false,
    this.border,
    this.borderRadius = 8,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? double.infinity,
      height: height ?? 52,
      decoration: enableBorder
          ? BoxDecoration(
              border: border ?? Border.all(color: Colors.grey, width: 2.0),
              borderRadius: BorderRadius.circular(borderRadius),
            )
          : null,
      child: icon == null
          ? FilledButton(
              onPressed: isLoading ? () {} : onPressed,
              style: _setButtonStyle(context),
              child: _buildButtonLabelOrLoader(),
            )
          : FilledButton.icon(
              onPressed: isLoading ? () {} : onPressed,
              style: _setButtonStyle(context),
              icon: isLoading ? null : icon,
              iconAlignment: IconAlignment.end,
              label: _buildButtonLabelOrLoader(),
            ),
    );
  }

  Widget _buildButtonLabelOrLoader() {
    return isLoading
        ? SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        : Text(text);
  }

  ButtonStyle _setButtonStyle(BuildContext context) {
    return FilledButton.styleFrom(
      padding: EdgeInsets.symmetric(vertical: padding ?? 0),
      elevation: 2,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    ).merge(Theme.of(context).filledButtonTheme.style);
  }
}

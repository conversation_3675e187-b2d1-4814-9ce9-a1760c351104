import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';

class DynamicLinkText extends StatelessWidget {
  final String fullText;
  final List<String> linkTexts;
  final List<String> linkUrls;
  final TextStyle? baseStyle;
  final TextStyle? linkStyle;

  const DynamicLinkText({
    super.key,
    required this.fullText,
    required this.linkTexts,
    required this.linkUrls,
    this.baseStyle,
    this.linkStyle,
  });

  void _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (linkTexts.length != linkUrls.length) {
      throw ArgumentError('linkTexts and linkUrls must have the same length');
    }

    List<InlineSpan> spans = [];
    String remainingText = fullText;

    while (remainingText.isNotEmpty) {
      int earliestIndex = remainingText.length;
      String? currentLink;
      String? currentUrl;

      for (int i = 0; i < linkTexts.length; i++) {
        final index = remainingText.indexOf(linkTexts[i]);
        if (index >= 0 && index < earliestIndex) {
          earliestIndex = index;
          currentLink = linkTexts[i];
          currentUrl = linkUrls[i];
        }
      }

      if (currentLink == null || currentUrl == null) {
        spans.add(TextSpan(text: remainingText));
        break;
      }

      if (earliestIndex > 0) {
        spans.add(TextSpan(text: remainingText.substring(0, earliestIndex)));
      }

      spans.add(
        TextSpan(
          text: currentLink,
          style:
              linkStyle ??
              GoogleFonts.inter(
                fontWeight: FontWeight.w400,
                fontSize: 15,
                height: 22 / 15,
                color: const Color.fromARGB(255, 30, 50, 229),
                decoration: TextDecoration.underline,
              ),
          recognizer: TapGestureRecognizer()
            ..onTap = () => _launchURL(currentUrl!),
        ),
      );

      remainingText = remainingText.substring(
        earliestIndex + currentLink.length,
      );
      int removeIndex = linkTexts.indexOf(currentLink);
      linkTexts.removeAt(removeIndex);
      linkUrls.removeAt(removeIndex);
    }

    return RichText(
      text: TextSpan(
        style:
            baseStyle ??
            GoogleFonts.inter(
              color: Colors.black,
              fontWeight: FontWeight.w400,
              fontSize: 15,
              height: 22 / 15,
            ),
        children: spans,
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class CustomFormTextField extends StatefulWidget {
  const CustomFormTextField({
    super.key,
    required this.hintText,
    String? labelText,
    this.initialValue,
    this.controller,
    this.autocorrect = true,
    this.iconAssetPath,
    this.enabled = true,
    this.maxLength,
    this.autofocus = false,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.isDense = false,
    this.backgroundColor = AppColors.textfieldBg,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.textCapitalization = TextCapitalization.none,
    this.horizontalPadding = 16,
    this.verticalPadding = 14,
    this.onSaved,
    this.onChanged,
    this.onEditingComplete,
  }) : labelText = labelText ?? hintText;
  final double horizontalPadding;
  final double verticalPadding;
  final bool autocorrect;
  final TextEditingController? controller;
  final String hintText;
  final String labelText;
  final String? initialValue;
  final String? iconAssetPath;
  final bool obscureText;
  final bool enabled;
  final bool autofocus;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLength;
  final bool isDense;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Color backgroundColor;
  final TextCapitalization textCapitalization;
  final String? Function(String?)? validator;
  final void Function(String?)? onSaved;
  final void Function(String?)? onChanged;
  final VoidCallback? onEditingComplete;

  @override
  State<CustomFormTextField> createState() {
    return _CustomFormTextFieldState();
  }
}

class _CustomFormTextFieldState extends State<CustomFormTextField> {
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      obscureText: widget.obscureText,
      keyboardType: widget.keyboardType,
      style: GoogleFonts.inter(fontSize: 15, fontWeight: FontWeight.w500),
      enabled: widget.enabled,
      maxLength: widget.maxLength,
      inputFormatters: widget.inputFormatters,
      autocorrect: widget.autocorrect,
      autofocus: widget.autofocus,
      textCapitalization: widget.textCapitalization,
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.never,
        filled: true, // !widget.enabled,
        fillColor: widget.backgroundColor,
        isDense: widget.isDense,
        hintText: widget.hintText,
        labelText: widget.labelText,
        labelStyle: GoogleFonts.inter(color: AppColors.hintTextColor),
        hintStyle: GoogleFonts.inter(color: AppColors.hintTextColor),
        contentPadding: EdgeInsets.symmetric(
          horizontal: widget.horizontalPadding,
          vertical: widget.verticalPadding,
        ),
        prefixIcon:
            widget.prefixIcon ??
            (widget.iconAssetPath == null
                ? null
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: SvgPicture.asset(
                      widget.iconAssetPath!,
                      width: 20,
                      height: 20,
                    ),
                  )),
        suffixIcon: widget.suffixIcon,
      ),
      initialValue: widget.initialValue,
      validator: widget.validator,
      onSaved: widget.onSaved,
      onChanged: widget.onChanged,
      onEditingComplete: widget.onEditingComplete,
      autovalidateMode: AutovalidateMode.onUserInteraction,
    );
  }
}

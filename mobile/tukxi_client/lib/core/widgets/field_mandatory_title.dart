import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FieldMandatoryTitle extends StatelessWidget {
  const FieldMandatoryTitle({super.key, required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.left,
      text: TextSpan(
        text: title,
        style: GoogleFonts.inter(
          fontSize: 12,
          color: Colors.black,
          fontWeight: FontWeight.w500,
        ),
        children: [
          TextSpan(
            text: ' *',
            style: GoogleFonts.inter(
              fontSize: 12,
              color: Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

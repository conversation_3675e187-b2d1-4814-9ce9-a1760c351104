import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/features/auth/domain/params/auth_screen_params.dart';

class AuthHeader extends StatelessWidget {
  const AuthHeader({super.key, required this.params});

  final AuthScreenParams params;

  @override
  Widget build(BuildContext context) {
    return Text(
      params.authType == AuthType.phone || params.isPhonVerificationforEmail
          ? 'Enter the 4-digit code sent via SMS at ${params.phoneNumber} '
          : 'Enter the 4-digit code sent via Email at ${params.email}',
      style: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        height: 28 / 20,
      ),
      textAlign: TextAlign.left,
    );
  }
}

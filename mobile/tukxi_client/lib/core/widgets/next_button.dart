import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class NextButton extends StatelessWidget {
  const NextButton({
    super.key,
    this.backgroundColor,
    this.foregroundColor,
    this.onPressed,
    this.icon,
  });

  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final SvgPicture? icon;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: FilledButton.icon(
        style: FilledButton.styleFrom(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          backgroundColor: backgroundColor ?? AppColors.primary,
          foregroundColor: foregroundColor ?? Colors.white,
          padding: const EdgeInsets.only(left: 20, right: 15),
        ),
        onPressed: onPressed,
        icon: icon ?? SvgPicture.asset(AssetPaths.next),
        iconAlignment: IconAlignment.end,
        label: const Text('Next'),
      ),
    );
  }
}

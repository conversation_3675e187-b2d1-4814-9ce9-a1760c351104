import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class AppTheme {
  static ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    primaryColor: AppColors.primary,
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: AppBarTheme(
      centerTitle: false,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      titleTextStyle: GoogleFonts.inter(
        fontWeight: FontWeight.w600,
        fontSize: 24,
        height: 1,
        color: Colors.black,
      ),
      // systemOverlayStyle: SystemUiOverlayStyle.dark,
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        textStyle: GoogleFonts.inter(
          fontWeight: FontWeight.w600,
          fontSize: 16,
          height: 1,
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    // textButtonTheme: TextButtonThemeData(
    //   style: TextButton.styleFrom(
    //     textStyle: GoogleFonts.dmSans(
    //       fontWeight: FontWeight.w500,
    //       fontSize: 14,
    //       height: 1.5,
    //     ),
    //     foregroundColor: AppColors.primaryColor,
    //   ),
    // ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        textStyle: GoogleFonts.inter(
          fontWeight: FontWeight.w600,
          fontSize: 16,
          height: 1,
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),

    textTheme: GoogleFonts.interTextTheme(),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      selectedItemColor: AppColors.selectedTabColor,
      unselectedItemColor: AppColors.unselectedTabColor,
      selectedLabelStyle: GoogleFonts.dmSans(
        fontWeight: FontWeight.w500,
        fontSize: 12,
        height: 1,
      ),
      unselectedLabelStyle: GoogleFonts.dmSans(
        fontWeight: FontWeight.w500,
        fontSize: 12,
        height: 1,
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      labelStyle: GoogleFonts.dmSans(color: AppColors.hintText, fontSize: 12),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
    ),
    // datePickerTheme: DatePickerThemeData(
    //   cancelButtonStyle: ButtonStyle(
    //     foregroundColor: WidgetStateProperty.resolveWith((states) {
    //       if (states.contains(WidgetState.pressed)) {
    //         return Colors.grey;
    //       }
    //       return AppColors.primaryColor;
    //     }),
    //   ),
    //   confirmButtonStyle: ButtonStyle(
    //     foregroundColor: WidgetStateProperty.resolveWith((states) {
    //       if (states.contains(WidgetState.pressed)) {
    //         return Colors.grey;
    //       }
    //       return AppColors.primaryColor;
    //     }),
    //   ),
    //   headerBackgroundColor: AppColors.primaryColor,
    //   headerForegroundColor: Colors.white,
    //   todayBackgroundColor: WidgetStateProperty.resolveWith((states) {
    //     if (states.contains(WidgetState.selected)) {
    //       return AppColors.primaryColor;
    //     }
    //     return null;
    //   }),
    //   todayForegroundColor: WidgetStateProperty.resolveWith((states) {
    //     if (states.contains(WidgetState.selected)) {
    //       return Colors.white;
    //     }
    //     return AppColors.primaryColor;
    //   }),
    //   dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
    //     if (states.contains(WidgetState.selected)) {
    //       return AppColors.primaryColor;
    //     }
    //     return null;
    //   }),
    // ),
  );
}

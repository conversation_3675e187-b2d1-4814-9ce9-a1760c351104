class TokenError implements Exception {
  final String? status;
  final String? message;
  final int? statusCode;
  final String? error;

  const TokenError({this.status, this.message, this.statusCode, this.error});

  factory TokenError.fromJson(Map<String, dynamic> json) {
    return TokenError(
      status: json['status']?.toString(),
      message: json['message']?.toString(),
      statusCode: json['statusCode'] is int
          ? json['statusCode']
          : int.tryParse(json['statusCode']?.toString() ?? ''),
      error: json['error']?.toString(),
    );
  }

  @override
  String toString() {
    return 'TokenError: $message (statusCode: $statusCode, error: $error)';
  }
}

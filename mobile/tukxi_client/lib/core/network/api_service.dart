import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:tukxi/core/config/env_config.dart';
import 'package:tukxi/core/constants/api_constants.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/network/api_exception.dart';
import 'package:tukxi/core/network/network_service.dart';

class ApiService {
  late Dio _dio;
  final NetworkService _networkService;

  // Inject NetworkService via constructor for better flexibility
  ApiService({NetworkService? networkService})
    : _networkService = networkService ?? NetworkService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: EnvConfig.baseUrl,
        headers: EnvConfig.defaultHeaders,
      ),
    );
    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          request: true,
          requestBody: true,
          requestHeader: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          logPrint: (object) => print(object),
        ),
      );
    }
  }

  // Check network before making any request
  Future<bool> _checkNetworkConnection() async {
    final hasConnection = await _networkService.checkConnection();
    if (!hasConnection) {
      throw NoInternetException();
    }
    return true;
  }

  // General method for GET requests
  Future<T> get<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    Map<String, dynamic>? params,
  }) async {
    return _handleRequest(Endpoint.fromValue(endpoint), () async {
      await _checkNetworkConnection();
      final headers = await ApiConstants.headers(endpoint);
      try {
        Response response = await _dio.get(
          endpoint,
          queryParameters: params,
          options: Options(headers: headers),
        );
        return fromJson(response.data);
      } catch (error) {
        rethrow;
      }
    });
  }

  // General method for POST requests
  Future<T> post<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? queryParameters,
    bool isFileUpload = false,
    Map<String, String>? extraHeaders,
  }) async {
    print(EnvConfig.baseUrl);
    return _handleRequest(Endpoint.fromValue(endpoint), () async {
      await _checkNetworkConnection();
      final headers = await ApiConstants.headers(endpoint);
      if (isFileUpload) {
        headers.remove('Content-Type');
      }

      if (extraHeaders != null) {
        headers.addAll(extraHeaders);
      }

      try {
        Response response = await _dio.post(
          endpoint,
          data: body,
          queryParameters: queryParameters,
          options: Options(headers: headers),
        );
        return fromJson(response.data);
      } catch (error) {
        rethrow;
      }
    });
  }

  // General method for PUT requests
  Future<T> update<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? queryParameters,
  }) async {
    return _handleRequest(Endpoint.fromValue(endpoint), () async {
      await _checkNetworkConnection();
      final headers = await ApiConstants.headers(endpoint);
      try {
        Response response = await _dio.put(
          endpoint,
          data: body,
          queryParameters: queryParameters,
          options: Options(headers: headers),
        );
        return fromJson(response.data);
      } catch (error) {
        rethrow;
      }
    });
  }

  //General method for Put requests

  Future<T> put<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? queryParameters,
  }) async {
    return _handleRequest(Endpoint.fromValue(endpoint), () async {
      await _checkNetworkConnection();
      final headers = await ApiConstants.headers(endpoint);
      try {
        Response response = await _dio.put(
          endpoint,
          data: body,
          queryParameters: queryParameters,
          options: Options(headers: headers),
        );
        return fromJson(response.data);
      } catch (error) {
        rethrow;
      }
    });
  }

  // General method for PATCH requests

  Future<T> patch<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? queryParameters,
  }) async {
    return _handleRequest(Endpoint.fromValue(endpoint), () async {
      await _checkNetworkConnection();
      final headers = await ApiConstants.headers(endpoint);
      try {
        Response response = await _dio.patch(
          endpoint,
          data: body,
          queryParameters: queryParameters,
          options: Options(headers: headers),
        );
        return fromJson(response.data);
      } catch (error) {
        rethrow;
      }
    });
  }

  // General method for Download requests

  Future<String> downloadFile({
    required String endpoint,
    required String path,
    required String savePath,
    Map<String, dynamic>? queryParameters,
  }) async {
    return _handleRequest(Endpoint.fromValue(endpoint), () async {
      await _checkNetworkConnection();
      final headers = await ApiConstants.headers(endpoint);
      try {
        final url = '$endpoint/$path';

        final file = File(path);
        if (await file.exists()) {
          await file.delete();
        }

        await _dio.download(
          url,
          savePath,
          queryParameters: queryParameters,
          options: Options(
            headers: headers,
            responseType: ResponseType.bytes,
            followRedirects: false,
          ),
          onReceiveProgress: (received, total) {
            if (total != -1) {
              debugPrint(
                "Downloading: ${(received / total * 100).toStringAsFixed(0)}%",
              );
            }
          },
        );
        return savePath;
      } catch (error) {
        rethrow;
      }
    });
  }

  // General method for DELETE requests
  Future<T> delete<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    Map<String, dynamic>? queryParameters,
  }) async {
    return _handleRequest(Endpoint.fromValue(endpoint), () async {
      await _checkNetworkConnection();
      final headers = await ApiConstants.headers(endpoint);

      try {
        Response response = await _dio.delete(
          endpoint,
          queryParameters: queryParameters,
          options: Options(headers: headers),
        );
        return fromJson(response.data);
      } catch (error) {
        rethrow;
      }
    });
  }

  // Handle API requests and error handling
  Future<T> _handleRequest<T>(
    Endpoint? endPoint,
    Future<T> Function() request,
  ) async {
    try {
      return await request();
    } on DioException catch (error) {
      final apiError = _handleError(error);

      //TODO: Handle token errors if needed

      // if (apiError is TokenError) {
      //   return await TokenHandler.handleTokenError(
      //     error: apiError,
      //     retryRequest: request,
      //     endpoint: endPoint,
      //   );
      // } else if (apiError is ApiException) {
      throw apiError;
      // }
      // throw apiError;
    } catch (error) {
      debugPrint("Unexpected error: $error");
      rethrow;
    }
  }

  // Handle different types of API errors
  dynamic _handleError(DioException error) {
    // Check if it's a network connectivity issue first
    if (error.error is SocketException ||
        error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.receiveTimeout) {
      return ApiException(message: AppConstants.noInternet);
    }

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiException(
          message: 'Connection timeout. Please check your internet connection.',
          statusCode: error.response?.statusCode,
        );

      case DioExceptionType.badResponse:
        return _handleResponseError(error.response);

      case DioExceptionType.cancel:
        return ApiException(
          message: 'Request cancelled',
          statusCode: error.response?.statusCode,
        );

      case DioExceptionType.badCertificate:
        return ApiException(
          message: 'SSL certificate verification failed',
          statusCode: error.response?.statusCode,
        );

      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return ApiException(
            message: AppConstants.noInternet,
            statusCode: error.response?.statusCode,
          );
        }
        return ApiException(
          message: 'An unexpected error occurred',
          statusCode: error.response?.statusCode,
          data: error.response?.data,
        );

      default:
        return ApiException(
          message: AppConstants.noInternet,
          statusCode: error.response?.statusCode,
          data: error.response?.data,
        );
    }
  }

  // Handle specific response errors
  dynamic _handleResponseError(Response? response) {
    final data = response?.data;

    if (response?.statusCode == 401) {
      //TODO: - Handle unauthorized access, e.g., token expiration
      // if (response?.statusCode == 401 && data['message'] == 'Unauthorized') {
      //   return TokenError(
      //     message: response?.data['message'],
      //     statusCode: response?.statusCode,
      //   );
      // }

      return ApiException.fromJson(data);
    }

    switch (response?.statusCode) {
      case 400:
        return ApiException(
          message: data?['message'] ?? data?['error'] ?? 'Bad request',
          statusCode: 400,
        );
      case 403:
        return ApiException(message: 'Access forbidden', statusCode: 403);
      case 404:
        return ApiException(message: data['message'], statusCode: 404);
      case 500:
      case 502:
      case 503:
        return ApiException(
          message: 'Server error',
          statusCode: response?.statusCode,
        );
      default:
        return ApiException(
          message: 'An unexpected error occurred',
          statusCode: response?.statusCode,
        );
    }
  }
}

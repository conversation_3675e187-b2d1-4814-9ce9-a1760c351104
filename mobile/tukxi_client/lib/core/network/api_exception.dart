import 'package:tukxi/core/constants/app_constants.dart';

class ApiException implements Exception {
  final String? message;
  final int? statusCode;
  final dynamic data;
  final String? error;

  ApiException({required this.message, this.statusCode, this.data, this.error});

  factory ApiException.fromJson(Map<String, dynamic> json) {
    final dynamic rawMessage = json['message'];
    String? parsedMessage;
    if (rawMessage is String) {
      parsedMessage = rawMessage;
    } else if (rawMessage is List) {
      // Join list of strings into a single message
      parsedMessage = rawMessage.whereType<String>().join(', ');
    } else {
      parsedMessage = rawMessage?.toString();
    }

    return ApiException(
      message: parsedMessage,
      statusCode: json['statusCode'] is int
          ? json['statusCode']
          : int.tryParse(json['statusCode']?.toString() ?? ''),
      data: json['data'],
      error: json['error']?.toString(),
    );
  }

  @override
  String toString() {
    return 'ApiException: $message (statusCode: $statusCode, error: $error)';
  }
}

class NoInternetException extends ApiException {
  NoInternetException() : super(message: AppConstants.noInternet);
}

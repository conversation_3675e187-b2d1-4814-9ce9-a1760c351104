import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

class NetworkService {
  // Singleton pattern
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  // Connectivity instance
  final Connectivity _connectivity = Connectivity();

  // Stream controller to broadcast network status changes
  final _connectivityStreamController = StreamController<bool>.broadcast();

  // Current connection status
  bool _hasConnection = true;
  bool get hasConnection => _hasConnection;

  // Stream that other classes can listen to
  Stream<bool> get connectionStream => _connectivityStreamController.stream;

  // Initialize the service
  Future<void> initialize() async {
    // Check initial connection status
    await checkConnection();

    // Listen for connectivity changes
    _connectivity.onConnectivityChanged.listen((_) async {
      await checkConnection();
    });
  }

  // Check if device has internet connection
  Future<bool> checkConnection() async {
    bool previousConnection = _hasConnection;

    try {
      final connectivityResults =
          await _connectivity.checkConnectivity(); // Now returns a List
      _hasConnection =
          connectivityResults.isNotEmpty &&
          !connectivityResults.contains(
            ConnectivityResult.none,
          ); // Check if there's at least one valid connection
    } catch (e) {
      _hasConnection = false;
    }

    // Only emit event if connection status changed
    if (previousConnection != _hasConnection) {
      _connectivityStreamController.add(_hasConnection);
    }

    return _hasConnection;
  }

  // Dispose resources
  void dispose() {
    _connectivityStreamController.close();
  }
}

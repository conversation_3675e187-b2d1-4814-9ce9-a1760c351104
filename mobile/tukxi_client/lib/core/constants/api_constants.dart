import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/services/shared_preference_service.dart';

class ApiConstants {
  static Future<Map<String, String>> headers(String endPoint) async {
    var headers = {'Content-Type': 'application/json'};

    final sharedPreferenceService = SharedPreferenceService();
    await sharedPreferenceService.init();

    final refreshToken = sharedPreferenceService.refreshToken;
    final token = sharedPreferenceService.token;

    if (endPoint == Endpoint.phoneSignup.value ||
        endPoint == Endpoint.emailSignup.value ||
        endPoint == Endpoint.emailOtpVerify.value ||
        endPoint == Endpoint.phoneOtpVerify.value ||
        endPoint == Endpoint.userProfile.value ||
        endPoint == Endpoint.userPhoneVerify.value ||
        endPoint == Endpoint.userEmailVerify.value ||
        endPoint == Endpoint.acceptTermsAndConditions.value) {
      headers['x-app-type'] = 'rider';
    }

    if (endPoint == Endpoint.refreshToken.value) {
      headers['Authorization'] = 'Bearer $refreshToken';
    } else if (endPoint != Endpoint.phoneSignup.value ||
        endPoint != Endpoint.emailSignup.value ||
        endPoint != Endpoint.emailOtpVerify.value ||
        endPoint != Endpoint.phoneOtpVerify.value ||
        endPoint != Endpoint.emailResendOtp.value ||
        endPoint != Endpoint.phoneResendOtp.value) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }
}

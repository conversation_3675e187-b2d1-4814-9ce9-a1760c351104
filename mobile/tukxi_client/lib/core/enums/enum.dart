enum SnackBarType { success, error, info }

enum AuthType { phone, google, apple, email }

enum ErrorType { apiInprogress, noInternet, tokenError, apiError, unknownError }

enum Endpoint {
  phoneSignup,
  emailSignup,
  phoneResendOtp,
  emailResendOtp,
  phoneOtpVerify,
  emailOtpVerify,
  userProfile,
  userPhoneVerify,
  userEmailVerify,
  acceptTermsAndConditions,
  refreshToken,
  logout;

  String get value {
    switch (this) {
      case phoneSignup:
        return 'auth/phone/signup';
      case emailSignup:
        return 'auth/email/signup';
      case phoneResendOtp:
        return 'auth/phone/resend-otp';
      case emailResendOtp:
        return 'auth/email/resend-otp';
      case phoneOtpVerify:
        return 'auth/phone/verify';
      case emailOtpVerify:
        return 'auth/email/verify';
      case userProfile:
        return 'user-profile';
      case userPhoneVerify:
        return 'user-profile/verify-phone';
      case userEmailVerify:
        return 'user-profile/verify-email';
      case acceptTermsAndConditions:
        return 'user-profile/terms-conditions';
      case refreshToken:
        return 'refresh-token';
      case logout:
        return 'auth/logout';
    }
  }

  static Endpoint? fromValue(String path) {
    for (var endpoint in Endpoint.values) {
      try {
        if (endpoint.value == path) {
          return endpoint;
        }
      } catch (_) {
        // Skip ones that require id
      }
    }
    return null;
  }
}

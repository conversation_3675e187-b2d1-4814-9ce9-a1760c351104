import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/errors/token_error.dart';
import 'package:tukxi/core/network/api_exception.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/features/no_internet/presentation/screens/no_internet_screen.dart';

Future<Either<Failure, T>> handleApiCall<T>(
  Future<T> Function() apiCall, {
  String apiErrorMessage = 'An error occurred.',
}) async {
  try {
    final response = await apiCall();
    return Right(response);
  } on NoInternetException catch (error) {
    return Left(
      ApiFailure(
        message: error.message ?? 'no_internet',
        type: ErrorType.noInternet,
      ),
    );
  } on TokenError catch (error) {
    return Left(
      ApiFailure(
        message: error.message ?? 'Token error',
        type: ErrorType.tokenError,
      ),
    );
  } on ApiException catch (error) {
    return Left(
      ApiFailure(
        message: error.message ?? apiErrorMessage,
        type: ErrorType.apiError,
      ),
    );
  } catch (error) {
    return Left(
      ApiFailure(message: error.toString(), type: ErrorType.unknownError),
    );
  }
}

void handleApiError({
  required BuildContext context,
  required Failure failure,
  required VoidCallback onRetry,
  String? errorMessage,
}) {
  if (failure.type == ErrorType.noInternet) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => NoInternetScreen(onRetry: onRetry),
      ),
    );
  } else if (failure.type != ErrorType.tokenError &&
      failure.type != ErrorType.apiInprogress) {
    SnackbarUtils.showSnackBar(
      context: context,
      message: (errorMessage != null && errorMessage.isNotEmpty)
          ? errorMessage
          : failure.message,
      type: SnackBarType.error,
    );
  }
}

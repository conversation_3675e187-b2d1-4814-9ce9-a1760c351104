import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/enums/enum.dart';

class SnackbarUtils {
  /// Shows a customizable snackbar
  ///
  /// [context] - BuildContext for showing the snackbar
  /// [message] - Message to display in the snackbar
  /// [isError] - If true, snackbar will have error styling
  /// [duration] - How long the snackbar should be displayed
  static void showSnackBar({
    required BuildContext context,
    required String message,
    required SnackBarType type,
    int? duration,
    bool disableDismiss = false,
  }) {
    final snackBar = SnackBar(
      content: Text(
        message,
        style: GoogleFonts.inter(fontSize: 16, fontWeight: FontWeight.w500),
      ),
      backgroundColor: _backgroundColor(type),
      duration: Duration(seconds: duration ?? 2),
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      action: disableDismiss
          ? null
          : SnackBarAction(
              label: 'Dismiss',
              textColor: Colors.white,
              onPressed: () {
                if (!context.mounted) return;
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  static Color _backgroundColor(SnackBarType type) {
    switch (type) {
      case SnackBarType.success:
        return const Color.fromARGB(255, 63, 164, 68);
      case SnackBarType.error:
        return const Color.fromARGB(255, 218, 45, 45);
      case SnackBarType.info:
        return Colors.grey.shade800;
    }
  }
}

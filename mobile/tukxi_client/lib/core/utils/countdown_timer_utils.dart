import 'dart:async';
import 'package:flutter/material.dart';
import 'package:tukxi/core/constants/app_constants.dart';

class CountdownTimerUtils {
  final ValueNotifier<int> remainingTime = ValueNotifier<int>(
    AppConstants.timer,
  );
  final ValueNotifier<bool> canResend = ValueNotifier<bool>(false);
  Timer? _timer;

  CountdownTimerUtils();

  void start([int seconds = AppConstants.timer]) {
    canResend.value = false;
    remainingTime.value = seconds;

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainingTime.value == 0) {
        canResend.value = true;
        timer.cancel();
      } else {
        remainingTime.value--;
      }
    });
  }

  void dispose() {
    _timer?.cancel();
    remainingTime.dispose();
    canResend.dispose();
  }
}

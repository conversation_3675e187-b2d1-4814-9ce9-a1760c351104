// lib/core/config/env_config.dart
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'secrets.dart';

enum AppEnvironment { staging, production }

class EnvConfig {
  static late AppEnvironment _environment;

  static AppEnvironment get environment => _environment;
  static bool get isStaging => _environment == AppEnvironment.staging;
  static bool get isProd => _environment == AppEnvironment.production;

  static Future<void> initialize(AppEnvironment env) async {
    _environment = env;

    // Load environment-specific .env file
    String envFile = switch (env) {
      AppEnvironment.staging => '.env.staging',
      AppEnvironment.production => '.env.prod',
    };

    await dotenv.load(fileName: envFile);
  }

  // === PUBLIC CONFIGURATION (from .env files) ===
  static String get baseUrl => dotenv.env['BASE_URL'] ?? '';
  // static String get socketUrl => dotenv.env['SOCKET_URL'] ?? '';
  // static String get appName => dotenv.env['APP_NAME'] ?? '';
  // static bool get debugMode => dotenv.env['DEBUG_MODE'] == 'true';
  // static String get googleMapsApiUrl => dotenv.env['GOOGLE_MAPS_API_URL'] ?? '';
  // static String get paymentGatewayUrl => dotenv.env['PAYMENT_GATEWAY_URL'] ?? '';
  // static int get socketTimeout => int.parse(dotenv.env['SOCKET_TIMEOUT'] ?? '10000');
  // static int get requestTimeout => int.parse(dotenv.env['REQUEST_TIMEOUT'] ?? '20000');

  // === SENSITIVE SECRETS (from envied) ===
  static String get apiKey => Secrets.apiKey;
  // static String get socketAuthToken => Secrets.socketAuthToken;
  // static String get googleMapsApiKey => Secrets.googleMapsApiKey;
  // static String get googlePlacesApiKey => Secrets.googlePlacesApiKey;
  // static String get stripePublishableKey => Secrets.stripePublishableKey;
  // static String get razorpayApiKey => Secrets.razorpayApiKey;
  // static String get twilioAccountSid => Secrets.twilioAccountSid;
  // static String get firebaseMessagingKey => Secrets.firebaseMessagingKey;
  // static String get encryptKey => Secrets.encryptKey;
  // static String get locationIqApiKey => Secrets.locationIqApiKey;
  // static String get oneSignalAppId => Secrets.oneSignalAppId;

  // === FEATURE FLAGS ===
  static bool get enableRideSharing => !isStaging;
  static bool get enableAdvancedAnalytics => isProd;
  static bool get showDebugInfo => isStaging || isStaging;
  static bool get enableCrashReporting => isProd;
  static bool get enableDetailedLogging => isStaging;

  // === TAXI APP SPECIFIC SETTINGS ===
  static double get driverSearchRadius =>
      double.parse(dotenv.env['DRIVER_SEARCH_RADIUS'] ?? '5.0');
  // static int get maxWaitTime => int.parse(dotenv.env['MAX_WAIT_TIME'] ?? '300');
  // static double get surgeMultiplier => double.parse(dotenv.env['SURGE_MULTIPLIER'] ?? '1.0');
  // static bool get enableSurgeMode => dotenv.env['ENABLE_SURGE_MODE'] == 'true';
  // static int get bookingTimeout => int.parse(dotenv.env['BOOKING_TIMEOUT'] ?? '120');
  // static double get cancellationFee => double.parse(dotenv.env['CANCELLATION_FEE'] ?? '0.0');
  static double get locationUpdateInterval => isStaging ? 5.0 : 10.0;
  static int get maxRetryAttempts => isStaging ? 3 : 5;

  // === UTILITY METHODS ===
  // TODO: - google maps url builder
  // Uncomment and implement if needed

  // static String buildGoogleMapsUrl(String endpoint) {
  //   return '$googleMapsApiUrl/$endpoint';
  // }

  // static String buildPlacesUrl(String query) {
  //   return '$googleMapsApiUrl/place/textsearch/json?query=$query&key=$googlePlacesApiKey';
  // }

  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
  };
}

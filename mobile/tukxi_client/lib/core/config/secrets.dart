import 'package:envied/envied.dart';

part 'secrets.g.dart';

@Envied(path: '.env.secrets')
abstract class Secrets {
  @EnviedField(varName: 'API_KEY', obfuscate: true)
  static final String apiKey = _Secrets.apiKey;

  // @EnviedField(varName: 'SOCKET_AUTH_TOKEN', obfuscate: true)
  // static const String socketAuthToken = _Secrets.socketAuthToken;

  // @EnviedField(varName: 'GOOGLE_MAPS_API_KEY', obfuscate: true)
  // static const String googleMapsApiKey = _Secrets.googleMapsApiKey;

  // @EnviedField(varName: 'GOOGLE_PLACES_API_KEY', obfuscate: true)
  // static const String googlePlacesApiKey = _Secrets.googlePlacesApiKey;

  // @EnviedField(varName: 'STRIPE_PUBLISHABLE_KEY', obfuscate: true)
  // static const String stripePublishableKey = _Secrets.stripePublishableKey;

  // @EnviedField(varName: 'RAZORPAY_API_KEY', obfuscate: true)
  // static const String razorpayApiKey = _Secrets.razorpayApiKey;

  // @EnviedField(varName: 'TWILIO_ACCOUNT_SID', obfuscate: true)
  // static const String twilioAccountSid = _Secrets.twilioAccountSid;

  // @EnviedField(varName: 'FIREBASE_MESSAGING_KEY', obfuscate: true)
  // static const String firebaseMessagingKey = _Secrets.firebaseMessagingKey;

  // @EnviedField(varName: 'ENCRYPT_KEY', obfuscate: true)
  // static const String encryptKey = _Secrets.encryptKey;

  // @EnviedField(varName: 'LOCATION_IQ_API_KEY', obfuscate: true)
  // static const String locationIqApiKey = _Secrets.locationIqApiKey;

  // @EnviedField(varName: 'ONESIGNAL_APP_ID', obfuscate: true)
  // static const String oneSignalAppId = _Secrets.oneSignalAppId;
}

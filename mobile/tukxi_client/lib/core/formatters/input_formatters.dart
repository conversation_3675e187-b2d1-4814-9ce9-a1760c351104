import 'package:flutter/services.dart';

class InputFormatters {
  static TextInputFormatter capitalizeFirstLetter() {
    return TextInputFormatter.withFunction((oldValue, newValue) {
      if (newValue.text.trim().isEmpty) return newValue;

      return TextEditingValue(
        text: newValue.text
            .split(' ')
            .map((word) {
              if (word.trim().isEmpty) return word;
              return word[0].toUpperCase() + word.substring(1).toLowerCase();
            })
            .join(' '),
        selection: newValue.selection,
      );
    });
  }

  static TextInputFormatter nameFormatter() {
    return FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z\s]'));
  }

  static TextInputFormatter phoneNumberFormatter() {
    return FilteringTextInputFormatter.digitsOnly;
  }

  static TextInputFormatter englishWithSpaceFormatter() {
    return FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z\s]'));
  }
}

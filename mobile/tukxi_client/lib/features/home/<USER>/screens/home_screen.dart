import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/services/shared_preference_service.dart';
import 'package:tukxi/routes/app_routes.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() {
    return _HomeScreenState();
  }
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    // final size = MediaQuery.of(context).size;
    // final mapHeight = 0.32 * size.height;

    return Scaffold(
      body: SafeArea(
        top: false,
        child: Align(
          alignment: Alignment.center,
          child: ElevatedButton.icon(
            onPressed: _logoutTapped,
            label: Text('Logout'),
            icon: Icon(Icons.logout),
          ),
        ),
        // child: SizedBox.expand(
        //   child: Stack(
        //   children: [
        //     Container(
        //       color: const Color.fromARGB(255, 227, 227, 229),
        //       height: mapHeight,
        //     ),
        //     Positioned.fill(
        //       bottom: 0,
        //       left: 0,
        //       right: 0,
        //       child: Container(
        //         padding: const EdgeInsets.all(UIConstants.kDefaultPadding),
        //         decoration: BoxDecoration(
        //           color: Colors.white,
        //           borderRadius: BorderRadius.only(
        //             topLeft: Radius.circular(
        //               UIConstants.kHomeContainerBorderRadius,
        //             ),
        //             topRight: Radius.circular(
        //               UIConstants.kHomeContainerBorderRadius,
        //             ),
        //           ),
        //         ),
        //         child: Column(
        //           children: [
        //             IconButton(
        //               onPressed: _logoutTapped,
        //               icon: Icon(Icons.logout),
        //             ),
        //             // SearchBox(),

        //             // SizedBox(height: 20),

        //             // SuggestedRides(),
        //           ],
        //         ),
        //       ),
        //     ),
        //   ],
        // ),
        // ),
      ),
    );
  }

  void _logoutTapped() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'Confirm Logout',
            style: GoogleFonts.inter(fontWeight: FontWeight.w500, fontSize: 25),
          ),
          content: Text(
            'Are you sure you want to log out?',
            style: GoogleFonts.inter(fontWeight: FontWeight.w500, fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                final prefService = SharedPreferenceService();
                prefService.clearSharedPreference();

                context.go(AppRoutes.login);
                // Navigator.pushAndRemoveUntil(
                //   context,
                //   MaterialPageRoute(builder: (context) => const LoginScreen()),
                //   (route) => false,
                // );
              },
              child: Text(
                'Logout',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';

class SuggestedRides extends StatefulWidget {
  const SuggestedRides({super.key});

  @override
  State<StatefulWidget> createState() {
    return _SuggestedRidesState();
  }
}

class _SuggestedRidesState extends State<SuggestedRides> {
  final _rideArray = [
    ('Sedan', AssetPaths.sedan),
    ('Auto', AssetPaths.auto),
    ('Rental', AssetPaths.rental),
    ('Outstation', AssetPaths.outstation),
    ('Intercity', AssetPaths.intercity),
  ];
  static const _spacing = 8.0;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Suggested Rides',
          style: GoogleFonts.inter(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        LayoutBuilder(
          builder: (context, constraints) {
            final maxWidth = constraints.maxWidth;
            final firstRowItemWidth = (maxWidth - _spacing) / 2;
            final otherRowItemWidth = (maxWidth - (2 * _spacing)) / 3;

            return Wrap(
              spacing: _spacing,
              runSpacing: _spacing,
              children: _rideArray.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isFirstRow = index < 2;
                final width = isFirstRow
                    ? firstRowItemWidth
                    : otherRowItemWidth;

                return _rideOption(item.$1, item.$2, width);
              }).toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _rideOption(String title, String asset, double width) {
    return Container(
      width: width,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade100,
      ),
      child: Row(
        children: [
          Image.asset(asset, height: 30),

          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.inter(fontSize: 12, fontWeight: FontWeight.w600),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

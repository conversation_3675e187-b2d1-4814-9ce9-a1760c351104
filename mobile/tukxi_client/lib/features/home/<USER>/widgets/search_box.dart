import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/custom_textfield.dart';

class SearchBox extends StatelessWidget {
  const SearchBox({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 15),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: AppColors.homeSearchBarBg,
      ),
      child: Row(
        children: [
          SvgPicture.asset(AssetPaths.search, height: 30, width: 30),
          Expanded(
            child: SizedBox(
              height: 52,
              child: CustomFormTextField(
                hintText: 'Where to?',
                backgroundColor: Colors.transparent,
                autocorrect: false,
                keyboardType: TextInputType.name,
                validator: (value) {
                  return null;
                },
              ),
            ),
          ),
          const SizedBox(width: 10),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Si<PERSON>B<PERSON>(
              height: 40,
              child: ElevatedButton.icon(
                onPressed: () {},
                icon: const Icon(Icons.access_time),
                label: Text(
                  'Now',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                ).merge(Theme.of(context).elevatedButtonTheme.style),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

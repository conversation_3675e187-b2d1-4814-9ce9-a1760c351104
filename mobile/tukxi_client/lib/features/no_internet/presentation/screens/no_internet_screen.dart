import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/network/network_service.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class NoInternetScreen extends StatefulWidget {
  const NoInternetScreen({super.key, required this.onRetry});

  final void Function() onRetry;

  @override
  State<NoInternetScreen> createState() {
    return _NoInternetScreenState();
  }
}

class _NoInternetScreenState extends State<NoInternetScreen> {
  bool _isLoading = false;
  final _networkService = NetworkService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Replace with your actual SVG asset path
                    SvgPicture.asset(AssetPaths.noInternet, height: 200),
                    const SizedBox(height: 24),
                    Text(
                      'Oops!',
                      style: GoogleFonts.dmSans(
                        fontWeight: FontWeight.w500,
                        fontSize: 26,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'It looks like you\'re offline. Please check your internet connection and try again.',
                      textAlign: TextAlign.center,
                      style: GoogleFonts.dmSans(
                        color: Colors.black.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 40),
                    SizedBox(
                      width: 225,
                      child: ElevatedButton(
                        onPressed: () async {
                          if (!mounted) return;
                          setState(() {
                            _isLoading = true;
                          });
                          bool hasInternet = await _networkService
                              .checkConnection();

                          if (hasInternet) {
                            if (!context.mounted) return;
                            Navigator.of(context).pop();
                            widget.onRetry();
                          }
                          if (!mounted) return;
                          setState(() {
                            _isLoading = false;
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ).merge(Theme.of(context).elevatedButtonTheme.style),
                        child: Text(
                          'Retry',
                          style: GoogleFonts.dmSans(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (_isLoading)
                Positioned.fill(
                  child: Center(
                    child: CircularProgressIndicator(color: AppColors.primary),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class AuthResponseEntity {
  final bool success;
  final AuthDataResponseEntity? data;
  final String? message;

  const AuthResponseEntity({this.success = false, this.data, this.message});
}

class AuthDataResponseEntity {
  final String? accessToken;
  final String? refreshToken;
  final int? expiresIn;
  final bool isProfileUpdated;
  final bool isPolicyAllowed;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final String? email;
  final String? phone;

  const AuthDataResponseEntity({
    this.accessToken,
    this.refreshToken,
    this.expiresIn,
    this.isProfileUpdated = false,
    this.isPolicyAllowed = false,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.email,
    this.phone,
  });
}

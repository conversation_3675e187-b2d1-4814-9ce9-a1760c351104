import 'package:tukxi/features/auth/data/models/auth_user.dart';

class AuthUserEntity {
  final String id;
  final String? userId;
  final String? roleId;
  final String? firstName;
  final String? lastName;
  final String? cityId;
  final String? referralCode;
  final String? profilePictureUrl;
  final String? languageId;
  final String? gender;
  final String? dob;
  final String? createdAt;
  final String? updatedAt;
  final String? email;
  final String? phone;
  final bool emailVerified;
  final bool phoneVerified;

  AuthUserEntity({
    required this.id,
    this.userId,
    this.roleId,
    this.firstName,
    this.lastName,
    this.cityId,
    this.referralCode,
    this.profilePictureUrl,
    this.languageId,
    this.gender,
    this.dob,
    this.createdAt,
    this.updatedAt,
    this.email,
    this.phone,
    this.emailVerified = false,
    this.phoneVerified = false,
  });
}

class ProfileEntity {
  final AuthUser? profile;

  ProfileEntity({this.profile});
}

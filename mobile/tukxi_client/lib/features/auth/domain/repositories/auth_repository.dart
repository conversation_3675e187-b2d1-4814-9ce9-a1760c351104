import 'package:dartz/dartz.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/auth/data/models/auth_response.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';

abstract class AuthRepository {
  Future<Either<Failure, AuthResponse>> phoneOrEmailSignup({
    required AuthType authType,
    String? phone,
    String? email,
  });

  Future<Either<Failure, AuthResponse>> resendOtp({
    required AuthType authType,
    bool isPhonVerificationforEmail = false,
    String? phone,
    String? email,
  });

  Future<Either<Failure, AuthResponse>> verifyOtp({
    required AuthType authType,
    required String otp,
    String? phone,
    String? email,
  });

  Future<Either<Failure, AuthUser>> fetchUserDetails();

  Future<Either<Failure, Profile>> updateUserDetails({
    required String firstName,
    required String lastName,
    String? email,
    String? phone,
    String? profilePictureUrl,
    String? gender,
    String? dob,
    String? cityId,
    String? languageId,
    String? referralCode,
  });

  Future<Either<Failure, AuthResponse>> userVerification({
    required String otp,
    String? phone,
    String? email,
  });

  Future<Either<Failure, AuthResponse>> acceptTermsAndConditions();
}

import 'package:tukxi/core/enums/enum.dart';

class AuthScreenParams {
  final AuthType authType;
  final String? phoneNumber;
  final String? email;
  final String? firstName;
  final String? lastName;
  final bool isPhonVerificationforEmail;
  final bool isLoginFlow;

  const AuthScreenParams({
    required this.authType,
    this.phoneNumber,
    this.email,
    this.firstName,
    this.lastName,
    this.isPhonVerificationforEmail = false,
    this.isLoginFlow = false,
  });

  AuthScreenParams copyWith({
    AuthType? authType,
    String? phoneNumber,
    String? email,
    String? countryCode,
    String? firstName,
    String? lastName,
    bool? isPhonVerificationforEmail,
    bool? isLoginFlow,
  }) {
    return AuthScreenParams(
      authType: authType ?? this.authType,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      isPhonVerificationforEmail:
          isPhonVerificationforEmail ?? this.isPhonVerificationforEmail,
      isLoginFlow: isLoginFlow ?? this.isLoginFlow,
    );
  }
}

import 'package:dartz/dartz.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/auth/data/models/auth_response.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/auth/domain/repositories/auth_repository.dart';

class AuthUsecase {
  AuthUsecase({required this.authRepository});

  final AuthRepository authRepository;

  Future<Either<Failure, AuthResponse>> execute({
    required AuthType authType,
    String? phoneNumber,
    String? email,
  }) async {
    return await authRepository.phoneOrEmailSignup(
      authType: authType,
      phone: phoneNumber,
      email: email,
    );
  }

  Future<Either<Failure, AuthResponse>> executeResendOtp({
    required AuthType authType,
    bool isPhonVerificationforEmail = false,
    String? phoneNumber,
    String? email,
  }) async {
    return await authRepository.resendOtp(
      authType: authType,
      isPhonVerificationforEmail: isPhonVerificationforEmail,
      phone: phoneNumber,
      email: email,
    );
  }

  Future<Either<Failure, AuthResponse>> executeVerifyOtp({
    required AuthType authType,
    required String otp,
    String? phoneNumber,
    String? email,
  }) async {
    return await authRepository.verifyOtp(
      authType: authType,
      otp: otp,
      phone: phoneNumber,
      email: email,
    );
  }

  Future<Either<Failure, AuthUser>> executeUserDetailsFetch() async {
    return await authRepository.fetchUserDetails();
  }

  Future<Either<Failure, Profile>> executeUserProfileUpdate({
    required String firstName,
    required String lastName,
    String? email,
    String? phone,
    String? profilePictureUrl,
    String? gender,
    String? dob,
    String? cityId,
    String? languageId,
    String? referralCode,
  }) async {
    return await authRepository.updateUserDetails(
      firstName: firstName,
      lastName: lastName,
      email: email,
      phone: phone,
      profilePictureUrl: profilePictureUrl,
      gender: gender,
      dob: dob,
      cityId: cityId,
      languageId: languageId,
      referralCode: referralCode,
    );
  }

  Future<Either<Failure, AuthResponse>> executeUserVerification({
    required String otp,
    String? phone,
    String? email,
  }) async {
    return await authRepository.userVerification(
      otp: otp,
      phone: phone,
      email: email,
    );
  }

  Future<Either<Failure, AuthResponse>>
  executeAcceptTermsAndConditions() async {
    return await authRepository.acceptTermsAndConditions();
  }
}

import 'package:tukxi/features/auth/data/models/auth_response.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';

abstract class AuthState {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthSuccess extends AuthState {
  final AuthResponse? authResponse;
  final AuthUser? authUser;
  final bool isSuccess;

  AuthSuccess({this.authResponse, this.authUser, this.isSuccess = false});
}

class AuthError extends AuthState {
  final String message;
  AuthError(this.message);
}

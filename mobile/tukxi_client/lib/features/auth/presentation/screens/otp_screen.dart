import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/countdown_timer_utils.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/auth_header.dart';
import 'package:tukxi/core/widgets/loading_button.dart';
import 'package:tukxi/features/auth/data/models/auth_response.dart';
import 'package:tukxi/features/auth/domain/params/auth_screen_params.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/features/auth/presentation/providers/auth_provider.dart';
import 'package:tukxi/features/auth/presentation/states/auth_state.dart';
import 'package:tukxi/features/auth/presentation/widgets/otp_input_field.dart';
import 'package:tukxi/features/auth/presentation/widgets/resend_otp_button.dart';
import 'package:tukxi/routes/app_routes.dart';

class OtpScreen extends ConsumerStatefulWidget {
  const OtpScreen({super.key, required this.params});

  final AuthScreenParams params;
  @override
  ConsumerState<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends ConsumerState<OtpScreen> {
  final _errorNotifier = ValueNotifier<String?>(null);
  final _countdownTimer = CountdownTimerUtils();

  String _otpCode = '';
  List<TextEditingController?> _otpControllers = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _countdownTimer.start();
  }

  @override
  void dispose() {
    _errorNotifier.dispose();
    _countdownTimer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    return Scaffold(
      appBar: _buildAppBar(),
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: UIConstants.kDefaultPadding,
          ),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10),

                    // Title
                    AuthHeader(params: widget.params),

                    const SizedBox(height: 30),

                    // OTP Input
                    OtpInputField(
                      onControllersReady: (controllers) {
                        _otpControllers = controllers;
                      },
                      onOtpSubmitted: (value) {
                        _otpCode = value.trim();
                        _validateAndSubmitOtp();
                      },
                      onOtpChanged: (value) {
                        setState(() {
                          _errorMessage = null;
                        });
                      },
                      errorMessage: _errorMessage,
                    ),

                    SizedBox(height: 20),

                    ResendOtpButton(
                      onResend: () {
                        clearOtpFields();

                        _countdownTimer.start();
                        debugPrint("Resend tapped");
                        FocusScope.of(context).unfocus();
                        _resendOtp();
                      },
                      timerUtils: _countdownTimer,
                      authType: widget.params.authType,
                    ),
                  ],
                ),
              ),

              SizedBox(height: 10),

              // Next Button
              LoadingButton(
                isLoading: authState is AuthLoading,
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                onPressed: _otpCode.length < 4
                    ? () {
                        SnackbarUtils.showSnackBar(
                          context: context,
                          message: 'Please enter a valid OTP',
                          type: SnackBarType.error,
                        );
                      }
                    : () {
                        print("Entered OTP: $_otpCode");
                        _validateAndSubmitOtp();
                      },
                icon: SvgPicture.asset(AssetPaths.next),
                text: 'Next',
              ),

              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }

  ///MARK: - Other methods
  void clearOtpFields() {
    for (var controller in _otpControllers) {
      if (controller == null) return;
      controller.clear();
    }
    setState(() {
      _otpCode = '';
    });
  }

  void _validateAndSubmitOtp() {
    _errorNotifier.value = null;

    if (_otpCode.isEmpty || _otpCode.length < 4) {
      _errorNotifier.value = 'Please enter the 4-digit code';
      return;
    }

    if (_errorMessage != null && _errorMessage!.isNotEmpty) {
      return;
    }
    if (widget.params.isPhonVerificationforEmail) {
      _userVerification();
    } else {
      _verifyOtp();
    }
  }

  void _navigateToNextScreen(AuthResponse response) {
    final isProfileUpdated = response.data?.isProfileUpdated ?? false;

    final isPolicyAllowed = response.data?.isPolicyAllowed ?? false;

    final isPhoneVerified = response.data?.isPhoneVerified ?? false;

    final params = AuthScreenParams(
      authType: widget.params.authType,
      phoneNumber: widget.params.phoneNumber,
      email: widget.params.email,
    );

    if (isProfileUpdated && isPhoneVerified && !isPolicyAllowed) {
      context.push(
        AppRoutes.accept,
        extra: params.copyWith(
          firstName: widget.params.firstName,
          lastName: widget.params.lastName,
          isLoginFlow: true,
        ),
      );
    } else if (isProfileUpdated && isPolicyAllowed) {
      context.push(AppRoutes.tabbar);
    } else {
      context.push(AppRoutes.onboarding, extra: params);
    }
  }

  ///MARK: - Widgets
  PreferredSizeWidget _buildAppBar() {
    final authType = widget.params.authType;
    final isPhoneVerificationForEmail =
        widget.params.isPhonVerificationforEmail;

    return AppBar(
      title: Text(
        (authType == AuthType.phone || isPhoneVerificationForEmail)
            ? 'Confirm number'
            : 'Confirm email id',
        style: GoogleFonts.inter(fontSize: 14, fontWeight: FontWeight.w500),
      ),
      centerTitle: false,
      automaticallyImplyLeading: true,
      leading: Container(
        padding: const EdgeInsets.only(left: 16),
        child: IconButton(
          padding: EdgeInsets.zero,
          onPressed: () => context.pop(),
          icon: SvgPicture.asset(
            AssetPaths.back,
            height: 40,
            width: 40,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }

  ///MARK: - API Call
  void _resendOtp({
    bool? isPhoneVerificationForEmail,
    String? phone,
    String? email,
  }) async {
    final result = await ref
        .read(authProvider.notifier)
        .resendOtp(
          widget.params.authType,
          isPhoneVerificationForEmail ??
              widget.params.isPhonVerificationforEmail,
          widget.params.phoneNumber,
          widget.params.email,
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          errorMessage: 'Unable to resend otp. Please try again later',
          onRetry: () async => _resendOtp(
            isPhoneVerificationForEmail: isPhoneVerificationForEmail,
            phone: phone,
            email: email,
          ),
        );
      },
      (response) {
        if (isPhoneVerificationForEmail != null &&
            isPhoneVerificationForEmail) {
          context.push(
            AppRoutes.otp,
            extra: widget.params.copyWith(
              isPhonVerificationforEmail: true,
              email: widget.params.email ?? email,
              phoneNumber: widget.params.phoneNumber ?? phone,
            ),
          );
        } else {
          SnackbarUtils.showSnackBar(
            context: context,
            message: 'OTP has been sent.',
            type: SnackBarType.success,
          );
        }
      },
    );
  }

  void _userVerification() async {
    final result = await ref
        .read(authProvider.notifier)
        .userVerification(
          _otpCode,
          widget.params.phoneNumber,
          widget.params.email,
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _userVerification(),
        );
      },
      (response) {
        context.push(
          AppRoutes.accept,
          extra: widget.params.copyWith(
            firstName: widget.params.firstName,
            lastName: widget.params.lastName,
          ),
        );
      },
    );
  }

  void _verifyOtp() async {
    final result = await ref
        .read(authProvider.notifier)
        .verifyOtp(
          widget.params.authType,
          _otpCode,
          widget.params.phoneNumber,
          widget.params.email,
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _verifyOtp(),
        );
      },
      (response) {
        clearOtpFields();
        _otpCode = '';
        _navigateToNextScreen(response);
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/services/location_service.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/widgets/next_button.dart';
import 'package:tukxi/features/auth/domain/params/auth_screen_params.dart';
import 'package:tukxi/features/auth/presentation/providers/auth_provider.dart';
import 'package:tukxi/features/auth/presentation/widgets/onboarding_form.dart';
import 'package:tukxi/routes/app_routes.dart';

class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key, required this.params});

  final AuthScreenParams params;

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _phoneController = TextEditingController(
    text: '',
  );
  final _phoneNumberNotifier = ValueNotifier<String>('');
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  // final _emailController = TextEditingController();

  String _initialDialCode = 'IN';

  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      await _initCountryCode();
      await _fetchUserProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Your ${AppConstants.appName} Account',
          style: GoogleFonts.inter(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        centerTitle: false,
        automaticallyImplyLeading: true,
        // leading: Container(
        //   padding: const EdgeInsets.only(left: 16),
        //   child: IconButton(
        //     padding: EdgeInsets.zero,
        //     onPressed: () => context.pop(),
        //     icon: SvgPicture.asset(
        //       AssetPaths.back,
        //       height: 40,
        //       width: 40,
        //       fit: BoxFit.contain,
        //     ),
        //   ),
        // ),
      ),
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: UIConstants.kDefaultPadding,
          ),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 5),
                      Text(
                        'What’s your name?',
                        textAlign: TextAlign.left,
                        style: GoogleFonts.inter(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          height: 28 / 20,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tell us more about yourself. So that we can  address you properly.',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          height: 22 / 15,
                        ),
                      ),
                      const SizedBox(height: 20),

                      OnboardingForm(
                        formKey: _formKey,
                        params: widget.params,
                        phoneController: _phoneController,
                        phoneNumberNotifier: _phoneNumberNotifier,
                        initialDialCode: _initialDialCode,
                        firstNameController: _firstNameController,
                        lastNameController: _lastNameController,
                        // emailController: _emailController,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 10),

              NextButton(onPressed: _onNextButtonPressed),

              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }

  ///MARK: - Methods

  Future<void> _initCountryCode() async {
    final dialCode = await LocationService.getUserDialCode();
    if (dialCode != null) {
      setState(() {
        _initialDialCode = dialCode;
      });
    }
  }

  void _navigateToNextScreen(
    bool emailVerified,
    bool phoneVerified,
    String firstName,
    String lastName,
    String? email,
    String? phone,
  ) {
    AuthScreenParams params = AuthScreenParams(
      authType: widget.params.authType,
      phoneNumber: phone,
      email: email,
      firstName: firstName,
      lastName: lastName,
    );

    if (!phoneVerified && widget.params.authType == AuthType.email) {
      context.push(
        AppRoutes.otp,
        extra: params.copyWith(isPhonVerificationforEmail: true),
      );
    } else {
      context.push(AppRoutes.accept, extra: params);
    }
  }

  ///MARK: - Button Actions
  void _onNextButtonPressed() {
    FocusScope.of(context).unfocus();

    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState?.save();

    final phoneNumber = widget.params.authType == AuthType.phone
        ? widget.params.phoneNumber
        : _phoneNumberNotifier.value;
    final firstName = _firstNameController.text.trim();
    final lastName = _lastNameController.text.trim();
    final email = widget.params.email ?? '';

    _updateUserProfile(firstName, lastName, email, phoneNumber);
  }

  ///MARK: - API Call
  Future _fetchUserProfile() async {
    final result = await ref.read(authProvider.notifier).fetchUserDetails();

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _fetchUserProfile(),
        );
      },
      (response) {
        setState(() {
          _firstNameController.text = response.firstName ?? '';
          _lastNameController.text = response.lastName ?? '';
          _phoneController.text = response.phone ?? '';
        });
      },
    );
  }

  Future _updateUserProfile(
    String firstName,
    String lastName,
    String? email,
    String? phone,
  ) async {
    final result = await ref
        .read(authProvider.notifier)
        .updateUserProfile(
          firstName: firstName,
          lastName: lastName,
          email: email,
          phone: phone,
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async =>
              _updateUserProfile(firstName, lastName, email, phone),
        );
      },
      (response) {
        _navigateToNextScreen(
          response.profile?.emailVerified ?? false,
          response.profile?.phoneVerified ?? false,
          firstName,
          lastName,
          email,
          phone,
        );
      },
    );
  }
}

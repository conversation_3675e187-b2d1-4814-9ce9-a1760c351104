import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/constants/url_constants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/dynamic_link_text.dart';
import 'package:tukxi/core/widgets/next_button.dart';
import 'package:tukxi/features/auth/domain/params/auth_screen_params.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/features/auth/presentation/providers/auth_provider.dart';
import 'package:tukxi/routes/app_routes.dart';

class AcceptScreen extends ConsumerStatefulWidget {
  const AcceptScreen({super.key, required this.params});

  final AuthScreenParams params;
  @override
  ConsumerState<AcceptScreen> createState() => _AcceptScreenState();
}

class _AcceptScreenState extends ConsumerState<AcceptScreen> {
  bool _isAccepted = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Terms & Conditions',
          style: GoogleFonts.inter(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        centerTitle: false,
        automaticallyImplyLeading: true,
        leading: widget.params.isLoginFlow
            ? null
            : Container(
                padding: const EdgeInsets.only(left: 16),
                child: IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () => context.pop(),
                  icon: SvgPicture.asset(
                    AssetPaths.back,
                    height: 40,
                    width: 40,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
      ),

      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: UIConstants.kDefaultPadding,
          ),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    const SizedBox(height: 10),
                    Text(
                      'Accept Tukxi’s Terms & Review Privacy Notice',
                      style: GoogleFonts.inter(
                        fontWeight: FontWeight.w600,
                        fontSize: 20,
                        height: 28 / 20,
                      ),
                    ),
                    DynamicLinkText(
                      fullText: AppConstants.termsConsentString,
                      linkTexts: ['Terms of Use', 'Privacy Notice'],
                      linkUrls: [
                        UrlConstants.termsOfUseUrl,
                        UrlConstants.privacyPolicyUrl,
                      ],
                    ),

                    SizedBox(height: 20),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isAccepted = !_isAccepted;
                        });
                      },
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              'I Agree to the T&C.',
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          SvgPicture.asset(
                            _isAccepted
                                ? AssetPaths.checked
                                : AssetPaths.unchecked,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              Column(
                children: [
                  NextButton(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    icon: SvgPicture.asset(AssetPaths.next),
                    onPressed: !_isAccepted
                        ? () {
                            SnackbarUtils.showSnackBar(
                              context: context,
                              message: 'Please accept the Terms and Conditions',
                              type: SnackBarType.error,
                            );
                          }
                        : _acceptTermsAndConditions,
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///MARK: - API Call
  void _acceptTermsAndConditions() async {
    final result = await ref
        .read(authProvider.notifier)
        .acceptTermsAndConditions();

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _acceptTermsAndConditions(),
        );
      },
      (response) {
        context.push(AppRoutes.tabbar);
      },
    );
  }
}

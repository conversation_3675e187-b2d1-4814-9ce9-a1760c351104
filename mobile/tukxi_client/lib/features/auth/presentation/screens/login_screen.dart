import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/services/location_service.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/loading_button.dart';
import 'package:tukxi/features/auth/domain/params/auth_screen_params.dart';
import 'package:tukxi/features/auth/presentation/providers/auth_provider.dart';
import 'package:tukxi/features/auth/presentation/states/auth_state.dart';
import 'package:tukxi/features/auth/presentation/widgets/auth_form.dart';
import 'package:tukxi/features/auth/presentation/widgets/gray_icon_button.dart';
import 'package:tukxi/routes/app_routes.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _phoneController = TextEditingController(
    text: '',
  );
  final _phoneNumberNotifier = ValueNotifier<String>('');
  final ValueNotifier<AuthType> _authTypeNotifier = ValueNotifier(
    AuthType.phone,
  );
  final FocusNode _phoneFocusNode = FocusNode();
  final GlobalKey _bottomSectionKey = GlobalKey();
  double _bottomSectionHeight = 0;
  String _email = '';
  String _initialDialCode = 'IN';
  bool isLoading = false;

  KeyboardActionsConfig _buildKeyboardActionsConfig(BuildContext context) {
    return KeyboardActionsConfig(
      keyboardActionsPlatform: KeyboardActionsPlatform.IOS,
      actions: [
        KeyboardActionsItem(
          focusNode: _phoneFocusNode,
          displayArrows: false,
          toolbarButtons: [
            (node) => TextButton(
              onPressed: () => node.unfocus(),
              child: Text(
                'Done',
                style: GoogleFonts.inter(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  //MARK: - Dispose
  @override
  void dispose() {
    _phoneController.removeListener(_onPhoneChanged);
    _phoneController.dispose();
    _phoneNumberNotifier.dispose();
    _phoneFocusNode.dispose();
    super.dispose();
  }

  //MARK: - init state

  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      await _initCountryCode();
    });
    _phoneController.addListener(_onPhoneChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureBottomSectionHeight();
    });
  }

  //MARK:- build methods

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    return ValueListenableBuilder<AuthType>(
      valueListenable: _authTypeNotifier,
      builder: (context, authType, child) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: KeyboardVisibilityBuilder(
            builder: (context, isKeyboardVisible) {
              return SafeArea(
                top: false,
                bottom: false,
                left: false,
                right: false,
                child: Stack(
                  children: [
                    Positioned.fill(
                      top: 0,
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Image.asset(AssetPaths.loginBg, fit: BoxFit.cover),
                    ),

                    KeyboardActions(
                      config: _buildKeyboardActionsConfig(context),
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: AnimatedContainer(
                          width: double.infinity,
                          duration: const Duration(milliseconds: 1),
                          padding: EdgeInsets.fromLTRB(16, 20, 16, 24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(
                                UIConstants.kLoginBorderRadius,
                              ),
                              topRight: Radius.circular(
                                UIConstants.kLoginBorderRadius,
                              ),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                authType == AuthType.phone
                                    ? 'Enter your mobile number'
                                    : 'Enter your email address',
                                textAlign: TextAlign.left,
                                style: GoogleFonts.inter(
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(height: 16),

                              AuthForm(
                                formKey: _formKey,
                                authType: authType,
                                phoneController: _phoneController,
                                phoneNumberNotifier: _phoneNumberNotifier,
                                initialDialCode: _initialDialCode,
                                phoneFocusNode: _phoneFocusNode,
                                onEmailSaved: (email) {
                                  _email = email?.trim() ?? '';
                                },
                              ),

                              Text(
                                'We’ll send you a one-time password (OTP) to verify.',
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.black,
                                ),
                              ),

                              SizedBox(height: 10),

                              LoadingButton(
                                onPressed: _onContinuePressed,
                                isLoading: authState is AuthLoading,
                                text: 'Continue',
                              ),

                              if (!isKeyboardVisible)
                                _buildOtherLoginOptions(authType),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // ),
                    if (isLoading)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(color: AppColors.black15),
                          child: Center(child: CircularProgressIndicator()),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  //MARK: - Widgets
  Widget _buildOtherLoginOptions(AuthType authType) {
    return Column(
      children: [
        const SizedBox(height: UIConstants.kButtonSpacing),

        /// Divider
        Row(
          children: [
            Expanded(child: Divider(thickness: 1)),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                'More ways to sign in',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w400,
                  fontSize: 10,
                  height: 1,
                ),
              ),
            ),

            Expanded(child: Divider(thickness: 1)),
          ],
        ),
        const SizedBox(height: UIConstants.kButtonSpacing),

        /// Social Login Buttons
        _buildButtons(authType),
        const SizedBox(height: UIConstants.kDefaultPadding),
      ],
    );
  }

  Widget _buildButtons(AuthType authType) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (GoogleSignIn.instance.supportsAuthenticate())
          GrayIconButton(
            onPressed: () {
              // TODO: Google sign-in
            },
            isWithspaceAfter: true,
            icon: SvgPicture.asset(
              AssetPaths.gmail,
              height: 28,
              width: 28,
              fit: BoxFit.contain,
            ),
          ),

        if (Platform.isIOS)
          GrayIconButton(
            onPressed: () {
              // TODO: Apple sign-in
            },
            isWithspaceAfter: true,
            icon: const Icon(Icons.apple, size: 25),
          ),

        if (authType == AuthType.phone)
          GrayIconButton(
            onPressed: () {
              _authTypeNotifier.value = AuthType.email;
            },
            icon: const Icon(Icons.mail_outline),
          ),

        if (authType == AuthType.email)
          GrayIconButton(
            onPressed: () {
              _authTypeNotifier.value = AuthType.phone;
              _phoneController.clear();
              _phoneNumberNotifier.value = '';
            },
            icon: SvgPicture.asset(AssetPaths.phone),
          ),
      ],
    );
  }

  ///MARK: - Methods

  void _onPhoneChanged() {
    _phoneNumberNotifier.value = _phoneController.text;
  }

  Future<void> _initCountryCode() async {
    setState(() {
      isLoading = true;
    });
    final dialCode = await LocationService.getUserDialCode();
    if (dialCode != null) {
      setState(() {
        _initialDialCode = dialCode;
      });
    }
    setState(() {
      isLoading = false;
    });
  }

  String _validate() {
    final phoneNumber = _phoneNumberNotifier.value.trim();
    debugPrint(phoneNumber);
    debugPrint(_email);
    if (_authTypeNotifier.value == AuthType.phone && phoneNumber.isEmpty) {
      return 'Please enter your phone number';
    }

    if (_authTypeNotifier.value == AuthType.email && _email.isEmpty) {
      return 'Please enter your email address';
    }
    return '';
  }

  void _measureBottomSectionHeight() {
    final context = _bottomSectionKey.currentContext;
    if (context != null) {
      final renderBox = context.findRenderObject() as RenderBox;
      final height = renderBox.size.height;
      if (height != _bottomSectionHeight) {
        setState(() {
          _bottomSectionHeight = height;
        });
      }
    }
  }

  ///MARK: - Button actions
  void _onContinuePressed() {
    FocusScope.of(context).unfocus();

    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState?.save();
    // Proceed with login logic
    final phoneNumber = _phoneNumberNotifier.value;
    debugPrint(phoneNumber);
    debugPrint(_email);

    final errorMessage = _validate();
    if (errorMessage.isNotEmpty) {
      SnackbarUtils.showSnackBar(
        context: context,
        message: errorMessage,
        type: SnackBarType.error,
      );
      return;
    }
    _phoneOrEmailSignup(phoneNumber);
  }

  ///MARK: - API Call
  void _phoneOrEmailSignup(String phoneNumber) async {
    final result = await ref
        .read(authProvider.notifier)
        .phoneOrEmailSignup(_authTypeNotifier.value, phoneNumber, _email);

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          errorMessage: 'Unable to send otp. Please try again later',
          onRetry: () async => _phoneOrEmailSignup(phoneNumber),
        );
      },
      (response) {
        context.push(
          AppRoutes.otp,
          extra: AuthScreenParams(
            authType: _authTypeNotifier.value,
            phoneNumber: phoneNumber,
            email: _authTypeNotifier.value == AuthType.phone ? '' : _email,
          ),
        );
      },
    );
  }
}

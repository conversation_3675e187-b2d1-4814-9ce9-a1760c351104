import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/extensions/string_extensions.dart';
import 'package:tukxi/core/formatters/input_formatters.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/custom_textfield.dart';
import 'package:tukxi/core/widgets/field_mandatory_title.dart';
import 'package:tukxi/features/auth/domain/params/auth_screen_params.dart';

class OnboardingForm extends StatefulWidget {
  const OnboardingForm({
    super.key,
    required this.formKey,
    required this.params,
    required this.phoneController,
    required this.phoneNumberNotifier,
    required this.initialDialCode,
    required this.firstNameController,
    required this.lastNameController,
    // required this.emailController,
  });

  final GlobalKey<FormState> formKey;
  final AuthScreenParams params;
  final TextEditingController phoneController;
  final ValueNotifier<String> phoneNumberNotifier;
  final String initialDialCode;
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;
  // final TextEditingController emailController;

  @override
  State<OnboardingForm> createState() {
    return _OnboardingFormState();
  }
}

class _OnboardingFormState extends State<OnboardingForm> {
  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// First Name
          FieldMandatoryTitle(title: 'First Name'),

          const SizedBox(height: 8),
          CustomFormTextField(
            hintText: 'First Name',
            labelText: 'First Name',
            controller: widget.firstNameController,
            autocorrect: false,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              InputFormatters.capitalizeFirstLetter(),
              InputFormatters.englishWithSpaceFormatter(),
            ],
            maxLength: UIConstants.kMaxNameLength,
            keyboardType: TextInputType.name,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter your first name';
              } else if (!value.isEnglishWithSpaces) {
                return AppConstants.englishWithSpaceAllowed;
              } else if (value.length < UIConstants.kMinFirstNameLength) {
                return 'Minimum ${UIConstants.kMinFirstNameLength} characters required';
              }
              return null;
            },
          ),

          const SizedBox(height: 8),
          FieldMandatoryTitle(title: 'Last Name'),

          const SizedBox(height: 8),
          CustomFormTextField(
            hintText: 'Last Name',
            labelText: 'Last Name',
            autocorrect: false,
            controller: widget.lastNameController,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              InputFormatters.capitalizeFirstLetter(),
              InputFormatters.englishWithSpaceFormatter(),
            ],
            keyboardType: TextInputType.name,
            maxLength: UIConstants.kMaxNameLength,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter your last name';
              } else if (!value.isEnglishWithSpaces) {
                return AppConstants.englishWithSpaceAllowed;
              } else if (value.length < UIConstants.kMinLastNameLength) {
                return 'Minimum ${UIConstants.kMinLastNameLength} characters required';
              }
              return null;
            },
          ),

          const SizedBox(height: 8),

          if (widget.params.authType == AuthType.email) _buildPhoneForm(),

          // if (widget.params.authType == AuthType.phone) _buildEmailForm(),
        ],
      ),
    );
  }

  // Widget _buildEmailForm() {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       FieldTitle(title: 'Email Address'),
  //       const SizedBox(height: 8),
  //       CustomFormTextField(
  //         hintText: 'Email Address',
  //         labelText: 'Email Address',
  //         autocorrect: false,
  //         controller: widget.emailController,
  //         keyboardType: TextInputType.emailAddress,
  //         validator: (value) {
  //           if (widget.params.authType == AuthType.phone &&
  //               (value != null && value.trim().isNotEmpty) &&
  //               !value.isValidEmail) {
  //             return 'Please enter a valid email address';
  //           }
  //           return null;
  //         },
  //       ),
  //     ],
  //   );
  // }

  Widget _buildPhoneForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FieldMandatoryTitle(title: 'Phone number'),
        FormField<String>(
          validator: (value) {
            final phone = widget.phoneController.text;
            if (widget.params.authType == AuthType.email) {
              if (phone.isEmpty) {
                return 'Please enter your phone number';
              }
              if (phone.length < 8) {
                return 'Please enter a valid phone number';
              }
            }
            return null;
          },
          builder: (fieldState) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                IntlPhoneField(
                  key: ValueKey(widget.initialDialCode),
                  controller: widget.phoneController,
                  initialCountryCode: widget.initialDialCode,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: AppColors.textfieldBg,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                    errorText: fieldState.hasError ? fieldState.errorText : '',
                    hintText: 'Mobile Number',
                    hintStyle: GoogleFonts.inter(
                      color: AppColors.hintTextColor,
                    ),
                  ),
                  dropdownDecoration: BoxDecoration(
                    color: const Color(0xFFF3F3F3),
                  ),
                  flagsButtonPadding: const EdgeInsets.only(left: 12),
                  dropdownIconPosition: IconPosition.trailing,
                  style: GoogleFonts.inter(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                  ),
                  inputFormatters: [InputFormatters.phoneNumberFormatter()],
                  textInputAction: TextInputAction.done,
                  onChanged: (phone) {
                    widget.phoneNumberNotifier.value = phone.completeNumber
                        .trim();
                    fieldState.didChange(
                      phone.completeNumber.trim(),
                    ); // Mark field as changed
                  },
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}

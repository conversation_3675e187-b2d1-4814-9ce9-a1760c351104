import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/extensions/string_extensions.dart';
import 'package:tukxi/core/formatters/input_formatters.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/custom_textfield.dart';
import 'package:tukxi/core/widgets/field_title.dart';

class AuthForm extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final AuthType authType;
  final TextEditingController phoneController;
  final ValueNotifier<String> phoneNumberNotifier;
  final String initialDialCode;
  final FocusNode phoneFocusNode;
  final void Function(String? email) onEmailSaved;

  const AuthForm({
    super.key,
    required this.formKey,
    required this.authType,
    required this.phoneController,
    required this.phoneNumberNotifier,
    required this.initialDialCode,
    required this.onEmailSaved,
    required this.phoneFocusNode,
  });

  @override
  State<AuthForm> createState() {
    return _AuthFormState();
  }
}

class _AuthFormState extends State<AuthForm> {
  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: widget.authType == AuthType.phone
          ? _buildPhoneForm()
          : _buildEmailForm(),
    );
  }

  Widget _buildPhoneForm() {
    return FormField<String>(
      validator: (value) {
        final phone = widget.phoneController.text.trim();
        if (widget.authType == AuthType.phone) {
          if (phone.isEmpty) {
            return 'Please enter your phone number';
          }
        }
        return null;
      },
      builder: (fieldState) {
        return SizedBox(
          height: 80,
          child: IntlPhoneField(
            key: ValueKey(widget.initialDialCode),
            decoration: InputDecoration(
              fillColor: AppColors.textfieldBg,
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              errorText: fieldState.hasError ? fieldState.errorText : '',
              hintStyle: GoogleFonts.inter(color: AppColors.hintTextColor),
              errorStyle: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              labelStyle: GoogleFonts.inter(
                fontSize: 15,
                fontWeight: FontWeight.w400,
              ),
              hintText: 'Mobile Number',
            ),
            focusNode: widget.phoneFocusNode,
            // initialValue: '7736304930',
            autovalidateMode: AutovalidateMode.onUserInteraction,
            controller: widget.phoneController,
            dropdownDecoration: BoxDecoration(color: Color(0xFFF3F3F3)),
            initialCountryCode: widget.initialDialCode,
            flagsButtonPadding: const EdgeInsets.only(left: 12),
            dropdownIconPosition: IconPosition.trailing,
            style: GoogleFonts.inter(fontSize: 15, fontWeight: FontWeight.w500),
            inputFormatters: [InputFormatters.phoneNumberFormatter()],
            textInputAction: TextInputAction.done,
            onChanged: (phone) {
              widget.phoneNumberNotifier.value = phone.completeNumber.trim();
              widget.formKey.currentState?.validate();
              fieldState.didChange(phone.completeNumber.trim());
            },
          ),
        );
      },
    );
  }

  Widget _buildEmailForm() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: FieldTitle(title: 'Email Address'),
        ),
        SizedBox(height: 8),
        CustomFormTextField(
          hintText: 'Email Address',
          labelText: 'Email Address',
          // initialValue: '<EMAIL>',
          autocorrect: false,
          keyboardType: TextInputType.emailAddress,
          onSaved: widget.onEmailSaved,
          validator: (value) {
            if (widget.authType == AuthType.email) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter your email address';
              }
              if (!value.isValidEmail) {
                return 'Please enter a valid email address';
              }
            }

            return null;
          },
        ),

        SizedBox(height: 15),
      ],
    );
  }
}

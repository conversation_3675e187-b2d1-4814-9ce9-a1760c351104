import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/utils/countdown_timer_utils.dart';

class ResendOtpButton extends StatelessWidget {
  final VoidCallback onResend;
  final CountdownTimerUtils timerUtils;
  final AuthType authType;

  const ResendOtpButton({
    super.key,
    required this.onResend,
    required this.timerUtils,
    required this.authType,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 32,
      child: Row(
        children: [
          Text(
            'Haven\'t received a code?',
            style: GoogleFonts.inter(fontSize: 12, fontWeight: FontWeight.w500),
          ),
          ValueListenableBuilder<int>(
            valueListenable: timerUtils.remainingTime,
            builder: (context, remainingTime, _) {
              final canResend = remainingTime == 0;
              final minutes = (remainingTime ~/ 60).toString().padLeft(2, '0');
              final seconds = (remainingTime % 60).toString().padLeft(2, '0');
              final formattedTime = '$minutes:$seconds';

              final text = canResend
                  ? 'Resend OTP'
                  : authType == AuthType.phone
                  ? 'Send again ($formattedTime)'
                  : 'Send again ($formattedTime)';

              return TextButton(
                onPressed: canResend ? onResend : null,
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  text,
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                    decoration: TextDecoration.underline,
                    decorationColor: canResend ? Colors.black : Colors.grey,
                    color: canResend ? Colors.black : Colors.grey,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

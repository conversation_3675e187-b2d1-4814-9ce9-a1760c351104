import 'package:flutter/material.dart';
import 'package:flutter_otp_text_field/flutter_otp_text_field.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class OtpInputField extends StatelessWidget {
  final Function(List<TextEditingController?>) onControllersReady;
  final Function(String) onOtpSubmitted;
  final Function(String) onOtpChanged;
  final String? errorMessage;

  const OtpInputField({
    super.key,
    required this.onControllersReady,
    required this.onOtpSubmitted,
    required this.onOtpChanged,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        OtpTextField(
          fieldWidth: AppConstants.otpFieldWidth,
          fieldHeight: AppConstants.otpFieldHeight,
          handleControllers: onControllersReady,
          numberOfFields: AppConstants.otpLength,
          showFieldAsBox: true,
          borderWidth: 1,
          borderRadius: BorderRadius.circular(8),
          filled: true,
          fillColor: AppColors.textfieldBg,
          disabledBorderColor: Colors.transparent,
          cursorColor: AppColors.black50,
          enabledBorderColor: Colors.transparent,
          focusedBorderColor: Colors.transparent,
          autoFocus: false,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          keyboardType: TextInputType.number,
          showCursor: true,
          textStyle: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          onSubmit: onOtpSubmitted,
          onCodeChanged: onOtpChanged,
        ),
        if (errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              errorMessage!,
              style: GoogleFonts.inter(
                fontSize: 12,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }
}

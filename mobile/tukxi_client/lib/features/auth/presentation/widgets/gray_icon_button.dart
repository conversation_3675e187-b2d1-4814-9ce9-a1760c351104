import 'package:flutter/material.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class GrayIconButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget icon;
  final double width;
  final double? height;
  final double radius;
  final bool isWithspaceAfter;
  final double space;
  final TextStyle? textStyle;

  const GrayIconButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.width = 48,
    this.height,
    this.radius = 8.0,
    this.isWithspaceAfter = false,
    this.space = UIConstants.kDefaultPadding,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: width,
          height: height ?? width,
          child: IconButton(
            onPressed: () {
              FocusScope.of(context).unfocus();
              onPressed();
            },
            icon: icon,
            style: IconButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(radius),
              ),
              padding: EdgeInsets.zero,
              backgroundColor: AppColors.grayButtonBg,
              foregroundColor: Colors.black,
            ),
          ),
        ),
        if (isWithspaceAfter) SizedBox(width: space),
      ],
    );
  }
}

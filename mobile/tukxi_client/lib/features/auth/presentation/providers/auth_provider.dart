import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/services/shared_preference_service.dart';
import 'package:tukxi/features/auth/data/data_sources/auth_remote_data_source_impl.dart';
import 'package:tukxi/features/auth/data/models/auth_response.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/auth/data/repositories/auth_repository.dart';
import 'package:tukxi/features/auth/domain/usecases/auth_usecase.dart';
import 'package:tukxi/features/auth/presentation/states/auth_state.dart';

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final dataSource = AuthRemoteDataSourceImpl();
  final repository = AuthRepositoryImpl(authRemoteDataSource: dataSource);
  final useCase = AuthUsecase(authRepository: repository);
  return AuthNotifier(useCase);
});

class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier(this.usecase) : super(AuthInitial());

  final AuthUsecase usecase;
  Future<Either<Failure, AuthResponse>> phoneOrEmailSignup(
    AuthType authType,
    String? phone,
    String? email,
  ) async {
    state = AuthLoading();

    final result = await usecase.execute(
      authType: authType,
      phoneNumber: phone,
      email: email,
    );

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = AuthError('no_internet');
        } else {
          state = AuthError(failure.message);
        }
      },
      (response) {
        state = AuthSuccess(authResponse: response);
      },
    );
    return result;
  }

  Future<Either<Failure, AuthResponse>> resendOtp(
    AuthType authType,
    bool isPhonVerificationforEmail,
    String? phone,
    String? email,
  ) async {
    state = AuthLoading();

    final result = await usecase.executeResendOtp(
      authType: authType,
      isPhonVerificationforEmail: isPhonVerificationforEmail,
      phoneNumber: phone,
      email: email,
    );

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = AuthError('no_internet');
        } else {
          state = AuthError(failure.message);
        }
      },
      (response) {
        state = AuthSuccess(authResponse: response);
      },
    );
    return result;
  }

  Future<Either<Failure, AuthResponse>> verifyOtp(
    AuthType authType,
    String otp,
    String? phone,
    String? email,
  ) async {
    state = AuthLoading();

    final result = await usecase.executeVerifyOtp(
      authType: authType,
      otp: otp,
      phoneNumber: phone,
      email: email,
    );

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = AuthError('no_internet');
        } else {
          state = AuthError(failure.message);
        }
      },
      (response) {
        state = AuthSuccess(authResponse: response);
        _saveTokens(response);
      },
    );
    return result;
  }

  Future<Either<Failure, AuthUser>> fetchUserDetails() async {
    state = AuthLoading();

    final result = await usecase.executeUserDetailsFetch();

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = AuthError('no_internet');
        } else {
          state = AuthError(failure.message);
        }
      },
      (response) {
        state = AuthSuccess(authUser: response);
      },
    );
    return result;
  }

  Future<Either<Failure, Profile>> updateUserProfile({
    required String firstName,
    required String lastName,
    String? email,
    String? phone,
    String? profilePictureUrl,
    String? gender,
    String? dob,
    String? cityId,
    String? languageId,
    String? referralCode,
  }) async {
    state = AuthLoading();

    final result = await usecase.executeUserProfileUpdate(
      firstName: firstName,
      lastName: lastName,
      email: email,
      phone: phone,
      profilePictureUrl: profilePictureUrl,
      gender: gender,
      dob: dob,
      cityId: cityId,
      languageId: languageId,
      referralCode: referralCode,
    );

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = AuthError('no_internet');
        } else {
          state = AuthError(failure.message);
        }
      },
      (response) {
        state = AuthSuccess(authUser: response.profile);
      },
    );
    return result;
  }

  Future<Either<Failure, AuthResponse>> userVerification(
    String otp,
    String? phone,
    String? email,
  ) async {
    state = AuthLoading();

    final result = await usecase.executeUserVerification(
      otp: otp,
      phone: phone,
      email: email,
    );

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = AuthError('no_internet');
        } else {
          state = AuthError(failure.message);
        }
      },
      (response) {
        state = AuthSuccess(authResponse: response);
      },
    );
    return result;
  }

  Future<Either<Failure, AuthResponse>> acceptTermsAndConditions() async {
    state = AuthLoading();

    final result = await usecase.executeAcceptTermsAndConditions();

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = AuthError('no_internet');
        } else {
          state = AuthError(failure.message);
        }
      },
      (response) {
        state = AuthSuccess(authResponse: response);
        _saveLoginStatus();
      },
    );
    return result;
  }

  void _saveLoginStatus() {
    final prefService = SharedPreferenceService();
    prefService.isLoggedIn = true;
  }

  void _saveTokens(AuthResponse response) {
    final prefService = SharedPreferenceService();
    if (response.data != null &&
        response.data!.accessToken != null &&
        response.data!.accessToken!.isNotEmpty) {
      prefService.token = response.data!.accessToken!;
    }
    if (response.data!.refreshToken != null &&
        response.data!.refreshToken!.isNotEmpty) {
      prefService.refreshToken = response.data!.refreshToken!;
    }
  }
}

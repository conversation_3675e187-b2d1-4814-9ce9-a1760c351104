import 'package:tukxi/features/auth/domain/entities/auth_user_entity.dart';

/// Represents the authenticated user across the app.
class AuthUser extends AuthUserEntity {
  AuthUser({
    required super.id,
    super.userId,
    super.roleId,
    super.firstName,
    super.lastName,
    super.cityId,
    super.referralCode,
    super.profilePictureUrl,
    super.languageId,
    super.gender,
    super.dob,
    super.createdAt,
    super.updatedAt,
    super.email,
    super.phone,
    super.emailVerified,
    super.phoneVerified,
  });

  factory AuthUser.fromJson(Map<String, dynamic> json) {
    return AuthUser(
      id: json['id'] == null ? '' : json['id'] as String,
      userId: json['userId'] == null ? '' : json['userId'] as String? ?? '',
      roleId: json['roleId'] == null ? '' : json['roleId'] as String? ?? '',
      firstName: json['firstName'] == null
          ? ''
          : json['firstName'] as String? ?? '',
      lastName: json['lastName'] == null
          ? ''
          : json['lastName'] as String? ?? '',
      cityId: json['cityId'] == null ? '' : json['cityId'] as String? ?? '',
      referralCode: json['referralCode'] == null
          ? ''
          : json['referralCode'] as String? ?? '',
      profilePictureUrl: json['profilePictureUrl'] == null
          ? ''
          : json['profilePictureUrl'] as String? ?? '',
      gender: json['gender'] == null ? '' : json['gender'] as String? ?? '',
      dob: json['dob'] == null ? '' : json['dob'] as String? ?? '',
      languageId: json['languageId'] == null
          ? ''
          : json['languageId'] as String? ?? '',
      createdAt: json['createdAt'] == null
          ? ''
          : json['createdAt'] as String? ?? '',
      updatedAt: json['updatedAt'] == null
          ? ''
          : json['updatedAt'] as String? ?? '',
      email: json['email'] == null ? '' : json['email'] as String? ?? '',
      phone: json['phone'] == null ? '' : json['phone'] as String? ?? '',
      phoneVerified: json['phoneVerified'] as bool? ?? false,
      emailVerified: json['emailVerified'] as bool? ?? false,
    );
  }
}

class Profile extends ProfileEntity {
  Profile({super.profile});

  factory Profile.fromJson(Map<String, dynamic> json) {
    return Profile(
      profile: json['profile'] == null
          ? null
          : AuthUser.fromJson(json['profile']),
    );
  }
}

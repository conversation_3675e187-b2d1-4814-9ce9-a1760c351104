import 'package:tukxi/features/auth/domain/entities/auth_response_entity.dart';

class AuthResponse extends AuthResponseEntity {
  AuthResponse({super.success, super.message, super.data});

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] as bool,
      data: json['data'] == null
          ? null
          : AuthDataResponse.fromJson(json['data']),
      message: json['message'] as String?,
    );
  }
}

class AuthDataResponse extends AuthDataResponseEntity {
  AuthDataResponse({
    super.accessToken,
    super.refreshToken,
    super.expiresIn,
    super.isProfileUpdated,
    super.isPolicyAllowed,
    super.isEmailVerified,
    super.isPhoneVerified,
    super.email,
    super.phone,
  });

  factory AuthDataResponse.fromJson(Map<String, dynamic> json) {
    return AuthDataResponse(
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      expiresIn: json['expiresIn'] as int?,
      isProfileUpdated: json['isProfileUpdated'] as bool? ?? false,
      isPolicyAllowed: json['isPolicyAllowed'] as bool? ?? false,
      isEmailVerified: json['emailVerified'] as bool? ?? false,
      isPhoneVerified: json['phoneVerified'] as bool? ?? false,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
    );
  }
}

import 'package:dartz/dartz.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/features/auth/data/data_sources/auth_remote_data_sources.dart';
import 'package:tukxi/features/auth/data/models/auth_response.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/auth/domain/repositories/auth_repository.dart';

class AuthRepositoryImpl implements AuthRepository {
  AuthRepositoryImpl({required this.authRemoteDataSource});

  final AuthRemoteDataSources authRemoteDataSource;

  @override
  Future<Either<Failure, AuthResponse>> phoneOrEmailSignup({
    required AuthType authType,
    String? phone,
    String? email,
  }) async {
    return handleApiCall(
      () => authRemoteDataSource.phoneOrEmailSignup(
        authType: authType,
        phone: phone,
        email: email,
      ),
      apiErrorMessage: 'Failed to send OTP.',
    );
  }

  @override
  Future<Either<Failure, AuthResponse>> resendOtp({
    required AuthType authType,
    bool isPhonVerificationforEmail = false,
    String? phone,
    String? email,
  }) {
    return handleApiCall(
      () => authRemoteDataSource.resendOtp(
        authType: authType,
        isPhonVerificationforEmail: isPhonVerificationforEmail,
        phone: phone,
        email: email,
      ),
      apiErrorMessage: 'Failed to resend OTP.',
    );
  }

  @override
  Future<Either<Failure, AuthResponse>> verifyOtp({
    required AuthType authType,
    required String otp,
    String? phone,
    String? email,
  }) {
    return handleApiCall(
      () => authRemoteDataSource.verifyOtp(
        authType: authType,
        otp: otp,
        phone: phone,
        email: email,
      ),
      apiErrorMessage: 'Failed to verify OTP.',
    );
  }

  @override
  Future<Either<Failure, AuthUser>> fetchUserDetails() {
    return handleApiCall(
      () => authRemoteDataSource.fetchUserDetails(),
      apiErrorMessage: 'Failed fetch user details',
    );
  }

  @override
  Future<Either<Failure, Profile>> updateUserDetails({
    required String firstName,
    required String lastName,
    String? email,
    String? phone,
    String? profilePictureUrl,
    String? gender,
    String? dob,
    String? cityId,
    String? languageId,
    String? referralCode,
  }) {
    return handleApiCall(
      () => authRemoteDataSource.updateUserDetails(
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: phone,
        profilePictureUrl: profilePictureUrl,
        gender: gender,
        dob: dob,
        cityId: cityId,
        languageId: languageId,
        referralCode: referralCode,
      ),
      apiErrorMessage: 'Failed update user details',
    );
  }

  @override
  Future<Either<Failure, AuthResponse>> userVerification({
    required String otp,
    String? phone,
    String? email,
  }) {
    return handleApiCall(
      () => authRemoteDataSource.userVerification(
        otp: otp,
        phone: phone,
        email: email,
      ),
      apiErrorMessage: 'Failed to verify OTP.',
    );
  }

  @override
  Future<Either<Failure, AuthResponse>> acceptTermsAndConditions() {
    return handleApiCall(
      () => authRemoteDataSource.acceptTermsAndConditions(),
      apiErrorMessage: 'Failed accept terms and conditions.',
    );
  }
}

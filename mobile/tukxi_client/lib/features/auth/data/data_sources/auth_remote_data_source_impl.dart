import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/network/api_service.dart';
import 'package:tukxi/features/auth/data/data_sources/auth_remote_data_sources.dart';
import 'package:tukxi/features/auth/data/models/auth_response.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';

class AuthRemoteDataSourceImpl implements AuthRemoteDataSources {
  // final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  final _apiService = ApiService();

  @override
  Future<AuthResponse> phoneOrEmailSignup({
    required AuthType authType,
    String? phone,
    String? email,
  }) async {
    try {
      final response = await _apiService.post(
        authType == AuthType.phone
            ? Endpoint.phoneSignup.value
            : Endpoint.emailSignup.value,
        (json) => AuthResponse.fromJson(json),
        body: authType == AuthType.phone
            ? {'phoneNumber': phone}
            : {'email': email},
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<AuthResponse> resendOtp({
    required AuthType authType,
    bool isPhonVerificationforEmail = false,
    String? phone,
    String? email,
  }) async {
    try {
      final response = await _apiService.post(
        (isPhonVerificationforEmail || authType == AuthType.phone)
            ? Endpoint.phoneResendOtp.value
            : Endpoint.emailResendOtp.value,
        (json) => AuthResponse.fromJson(json),
        body: authType == AuthType.phone || isPhonVerificationforEmail
            ? {'phoneNumber': phone}
            : {'email': email},
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<AuthResponse> verifyOtp({
    required AuthType authType,
    required String otp,
    String? phone,
    String? email,
  }) async {
    try {
      Map<String, String> body = {'otp': otp};
      if (authType == AuthType.phone) {
        body['phoneNumber'] = phone!;
      } else {
        body['email'] = email!;
      }
      final response = await _apiService.post(
        authType == AuthType.phone
            ? Endpoint.phoneOtpVerify.value
            : Endpoint.emailOtpVerify.value,
        (json) => AuthResponse.fromJson(json),
        body: body,
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<AuthUser> fetchUserDetails() async {
    try {
      final response = await _apiService.get(
        Endpoint.userProfile.value,
        (json) => AuthUser.fromJson(json),
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<Profile> updateUserDetails({
    required String firstName,
    required String lastName,
    String? email,
    String? phone,
    String? profilePictureUrl,
    String? gender,
    String? dob,
    String? cityId,
    String? languageId,
    String? referralCode,
  }) async {
    Map<String, String> body = {'firstName': firstName, 'lastName': lastName};

    if (email != null && email.isNotEmpty) {
      body['email'] = email;
    }

    if (phone != null) {
      body['phone'] = phone;
    }

    if (profilePictureUrl != null) {
      body['profilePictureUrl'] = profilePictureUrl;
    }

    if (gender != null) {
      body['gender'] = gender;
    }

    if (dob != null) {
      body['dob'] = dob;
    }

    if (cityId != null) {
      body['cityId'] = cityId;
    }

    if (languageId != null) {
      body['languageId'] = languageId;
    }

    if (referralCode != null) {
      body['referralCode'] = referralCode;
    }

    final response = await _apiService.patch(
      Endpoint.userProfile.value,
      (json) => Profile.fromJson(json),
      body: body,
    );
    return response;
  }

  @override
  Future<AuthResponse> userVerification({
    required String otp,
    String? phone,
    String? email,
  }) async {
    try {
      Endpoint endpoint = Endpoint.userPhoneVerify;
      Map<String, String> body = {'otp': otp};

      if (phone != null) {
        body['phone'] = phone;
      } else if (email != null) {
        body['email'] = email;
        endpoint = Endpoint.userEmailVerify;
      }

      final response = await _apiService.post(
        endpoint.value,
        (json) => AuthResponse.fromJson(json),
        body: body,
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<AuthResponse> acceptTermsAndConditions() async {
    try {
      final response = await _apiService.patch(
        Endpoint.acceptTermsAndConditions.value,
        (json) => AuthResponse.fromJson(json),
        body: {"isPolicyAllowed": true},
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  // @override
  // Future<AuthUser> loginWithPhone(AuthScreenParams params) async {
  //   return AuthUser.fromJson({
  //     'email': params.email,
  //     'firstName': params.firstName,
  //     'lastName': params.lastName,
  //     'phoneNumber': params.phoneNumber,
  //   });
  // }

  // @override
  // Future<AuthUser> loginWithGoogle() async {
  //   try {
  //     final googleUser = await _googleSignIn.authenticate();

  //     return AuthUser.fromJson({
  //       'email': googleUser.email,
  //       'firstName': googleUser.displayName,
  //       'lastName': googleUser.displayName,
  //       'phoneNumber': 'phone',
  //     });
  //   } catch (error) {
  //     print(error);
  //     rethrow;
  //   }
  // }

  // @override
  // Future<AuthUser> loginWithApple() async {
  //   final appleCredential = await SignInWithApple.getAppleIDCredential(
  //     scopes: [
  //       AppleIDAuthorizationScopes.email,
  //       AppleIDAuthorizationScopes.fullName,
  //     ],
  //   );

  //   final credential = OAuthProvider("apple.com").credential(
  //     idToken: appleCredential.identityToken,
  //     accessToken: appleCredential.authorizationCode,
  //   );

  //   return AuthUser.fromJson({
  //     'email': credential.appleFullPersonName,
  //     'firstName': credential.appleFullPersonName,
  //     'lastName': credential.appleFullPersonName,
  //     'phoneNumber': credential.appleFullPersonName,
  //   });
  // }

  @override
  Future<void> logout() async {}
}

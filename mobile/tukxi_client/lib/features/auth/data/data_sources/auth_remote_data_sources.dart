import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/features/auth/data/models/auth_response.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';

abstract class AuthRemoteDataSources {
  // Future<AuthUser> loginWithEmail(AuthScreenParams parms);
  // Future<AuthUser> loginWithPhone(AuthScreenParams parms);
  // Future<AuthUser> loginWithGoogle();
  // Future<AuthUser> loginWithApple();
  Future<AuthResponse> phoneOrEmailSignup({
    required AuthType authType,
    String? phone,
    String? email,
  });

  Future<AuthResponse> resendOtp({
    required AuthType authType,
    bool isPhonVerificationforEmail = false,
    String? phone,
    String? email,
  });

  Future<AuthResponse> verifyOtp({
    required AuthType authType,
    required String otp,
    String? phone,
    String? email,
  });

  Future<AuthUser> fetchUserDetails();

  Future<Profile> updateUserDetails({
    required String firstName,
    required String lastName,
    String? email,
    String? phone,
    String? profilePictureUrl,
    String? gender,
    String? dob,
    String? cityId,
    String? languageId,
    String? referralCode,
  });

  Future<AuthResponse> userVerification({
    required String otp,
    String? phone,
    String? email,
  });

  Future<AuthResponse> acceptTermsAndConditions();

  Future<void> logout();
}

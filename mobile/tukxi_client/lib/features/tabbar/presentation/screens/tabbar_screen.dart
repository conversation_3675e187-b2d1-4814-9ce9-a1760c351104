import 'package:flutter/material.dart';
import 'package:tukxi/core/enums/enum_mappers.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/features/home/<USER>/screens/home_screen.dart';
import 'package:tukxi/features/profile/presentation/screens/profile_screen.dart';
import 'package:tukxi/features/services/presentation/screens/services_screen.dart';
import 'package:tukxi/features/tabbar/data/models/tabbar_item.dart';
import 'package:tukxi/features/trips/presentation/screens/trips_screen.dart';
import 'package:tukxi/main_prod.dart';

class TabbarScreen extends StatefulWidget {
  const TabbarScreen({super.key});

  @override
  State<TabbarScreen> createState() => _TabbarScreenState();
}

class _TabbarScreenState extends State<TabbarScreen> {
  int _selectedTabIndex = 0;

  //MARK: - Build Method
  @override
  Widget build(BuildContext context) {
    Widget activePageContent = HomeScreen();

    final seletedTab = tabItems[_selectedTabIndex];
    final currentNavItem =
        BottomNavItem.fromTabBarItem(seletedTab) ?? BottomNavItem.home;

    switch (currentNavItem) {
      case BottomNavItem.home:
        activePageContent = HomeScreen();
      case BottomNavItem.services:
        activePageContent = ServicesScreen();
      case BottomNavItem.trip:
        activePageContent = TripsScreen();
      case BottomNavItem.profile:
        activePageContent = ProfileScreen();
    }

    return Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: true,
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(color: AppColors.tabbarSeparatorColor, width: 1),
          ),
          color: Colors.white,
        ),
        child: BottomNavigationBar(
          key: homeTabKey,
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          elevation: 10,
          items: tabItems.asMap().entries.map((item) {
            final index = item.key;
            final tabItem = item.value;
            return BottomNavigationBarItem(
              icon: index == _selectedTabIndex
                  ? tabItem.selectedSvgAsset
                  : tabItem.unselectedSvgAsset,
              label: tabItem.title,
            );
          }).toList(),
          currentIndex: _selectedTabIndex,
          onTap: _onTabSelected,
        ),
      ),
      body: SafeArea(
        top: currentNavItem == BottomNavItem.home ? false : true,
        child: activePageContent,
      ),
    );
  }

  ///MARK: - Tab selection
  void _onTabSelected(int tabIndex) async {
    final selectedNavItem = BottomNavItem.fromTabBarItem(tabItems[tabIndex]);

    if (selectedNavItem == null) return;
    _navigatedToTabs(selectedNavItem);
  }

  void _navigatedToTabs(BottomNavItem navItem) {
    final int index = tabItems.indexWhere((tabitem) {
      return BottomNavItem.fromTabBarItem(tabitem) == navItem;
    });
    if (!mounted) return;
    setState(() {
      _selectedTabIndex = index;
    });
  }
}

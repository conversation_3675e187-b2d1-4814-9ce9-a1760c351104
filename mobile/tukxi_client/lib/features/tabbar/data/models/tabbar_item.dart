import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class TabBarItem {
  const TabBarItem._({
    required this.selectedSvgAsset,
    required this.unselectedSvgAsset,
    required this.title,
  });
  final SvgPicture selectedSvgAsset;
  final SvgPicture unselectedSvgAsset;
  final String title;

  // Factory constructors for creating instances
  factory TabBarItem.withColorFilter({
    required String assetPath,
    required String title,
  }) {
    return TabBarItem._(
      selectedSvgAsset: SvgPicture.asset(
        assetPath,
        colorFilter: const ColorFilter.mode(
          AppColors.selectedTabColor,
          BlendMode.srcIn,
        ),
      ),
      unselectedSvgAsset: SvgPicture.asset(
        assetPath,
        colorFilter: const ColorFilter.mode(
          AppColors.unselectedTabColor,
          BlendMode.srcIn,
        ),
      ),
      title: title,
    );
  }

  factory TabBarItem.withAssets({
    required String selectedPath,
    required String unselectedPath,
    required String title,
  }) {
    return TabBarItem._(
      selectedSvgAsset: SvgPicture.asset(selectedPath),
      unselectedSvgAsset: SvgPicture.asset(unselectedPath),
      title: title,
    );
  }
}

enum BottomNavItem {
  home,
  services,
  trip,
  profile;

  TabBarItem get value {
    switch (this) {
      case BottomNavItem.home:
        return TabBarItem.withAssets(
          selectedPath: AssetPaths.home,
          unselectedPath: AssetPaths.home,
          title: 'Home',
        );
      case BottomNavItem.services:
        return TabBarItem.withAssets(
          selectedPath: AssetPaths.services,
          unselectedPath: AssetPaths.services,
          title: 'Services',
        );
      case BottomNavItem.trip:
        return TabBarItem.withAssets(
          selectedPath: AssetPaths.trips,
          unselectedPath: AssetPaths.trips,
          title: 'Trips',
        );
      case BottomNavItem.profile:
        return TabBarItem.withAssets(
          selectedPath: AssetPaths.profile,
          unselectedPath: AssetPaths.profile,
          title: 'Profile',
        );
    }
  }

  static BottomNavItem? fromTabBarItem(TabBarItem tabBarItem) {
    return BottomNavItem.values.firstWhere(
      (item) => item.value.title == tabBarItem.title,
      orElse: () => BottomNavItem.home, //default
    );
  }
}

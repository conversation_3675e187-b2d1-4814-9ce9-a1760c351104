name: tukxi
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  connectivity_plus: ^6.1.4
  flutter_riverpod: ^2.6.1
  dio: ^5.8.0+1
  flutter_svg: ^2.2.0
  google_fonts: ^6.2.1
  intl: ^0.20.2
  shared_preferences: ^2.5.3
  flutter_launcher_icons: ^0.14.4
  flutter_otp_text_field: ^1.5.1+1
  flutter_native_splash: ^2.4.6
  auto_size_text: ^3.0.0
  go_router: ^16.0.0
  sign_in_with_apple: ^7.0.1
  intl_phone_number_input_perci: ^0.7.5
  intl_phone_field: ^3.2.0
  geocoding: ^4.0.0
  country_codes: ^3.3.0
  firebase_core: ^3.15.1
  firebase_auth: ^5.6.2
  url_launcher: ^6.3.2
  envied: ^1.1.1
  flutter_dotenv: ^5.2.1
  geolocator: ^14.0.2
  google_sign_in: ^7.1.1
  keyboard_actions: ^4.2.0
  flutter_keyboard_visibility: ^6.0.0
  google_maps_flutter: ^2.12.3
  dartz: ^0.10.1
  firebase_crashlytics: ^4.3.10
  firebase_analytics: ^11.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  envied_generator: ^1.1.1
  build_runner: ^2.5.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/home/
    - assets/images/splash/splash_screen_logo.png
    - assets/images/splash/splash_logo.png
    - assets/images/splash/android12_splash_logo.png
    - assets/images/login/gmail.svg
    - assets/images/login/phone.svg
    - assets/images/login/checked.svg
    - assets/images/login/unchecked.svg
    - assets/images/common/back.svg
    - assets/images/common/next.svg
    - assets/images/login/login_bg.png
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/common/no_internet.svg
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>

    - .env.staging
    - .env.prod

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

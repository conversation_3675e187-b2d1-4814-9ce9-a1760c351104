# Flutter/Dart
.dart_tool/
.packages
.pub/
build/
flutter_*.png
.melos_tool/

# Flutter dependencies
.pub-cache/
pubspec.lock

# IDEs
.idea/
.vscode/
*.iml

# Android
android/.gradle/
android/.idea/
android/app/build/
android/local.properties
android/.settings/
*.jks

# iOS & macOS
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/Flutter/Generated.xcconfig
ios/Flutter/ephemeral/
ios/Pods/
ios/.symlinks/
ios/Runner.xcworkspace/
ios/build/
ios/.flutter-plugins
ios/.flutter-plugins-dependencies
macos/Flutter/ephemeral/
macos/build/

# Web
web/.dart_tool/
web/build/

# Windows/Linux
windows/flutter/ephemeral/
windows/build/
linux/flutter/ephemeral/
linux/build/

# Misc
*.log
*.tmp
*.lock
*.DS_Store
.env
.env.*

# Firebase & Generated Files
firebase_app_id_file.json
google-services.json
GoogleService-Info.plist

# Old/Legacy iOS project files (from your existing file)
**/dgph
*.mode1v3
*.mode2v3
*.moved-aside
*.pbxuser
*.perspectivev3
**/*sync/
.sconsign.dblite
.tags*
**/.vagrant/
**/DerivedData/
Icon?
profile
xcuserdata
**/.generated/
Flutter/App.framework
Flutter/app.flx
Flutter/app.zip
Flutter/flutter_assets/
Flutter/flutter_export_environment.sh
ServiceDefinitions.json
Runner/GeneratedPluginRegistrant.*

# Exceptions
!default.mode1v3
!default.mode2v3
!default.pbxuser
!default.perspectivev3

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Tukxi</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>tukxi_client</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>http</string>
		<string>https</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs access to your location even when running in the background.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs access to your location to detect your country.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>location</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
    <!-- Google Sign-In Config -->
    <key>CFBundleURLTypes</key>
    <array>
      <dict>
        <key>CFBundleTypeRole</key>
        <string>Editor</string>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>com.googleusercontent.apps.391822358603-tcqstktd8c6j0k23f3vp9j1f2lo6eo76</string>
        </array>
      </dict>
    </array>

    <key>GoogleService-Info</key>
    <dict>
	<key>GIDServerClientID</key>
	<string>391822358603-ueno313fqnufu0lf1j17ma6mmnrea0do.apps.googleusercontent.com</string>

      <key>GIDClientID</key>
      <string>391822358603-tcqstktd8c6j0k23f3vp9j1f2lo6eo76.apps.googleusercontent.com</string>
    </dict>

    <key>LSApplicationQueriesSchemes</key>
    <array>
      <string>google</string>
      <string>com.googleusercontent.apps.391822358603-tcqstktd8c6j0k23f3vp9j1f2lo6eo76</string>
    </array>

</dict>
</plist>

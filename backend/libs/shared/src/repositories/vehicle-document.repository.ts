import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { VehicleDocument } from './models/vehicleDocument.model';

@Injectable()
export class VehicleDocumentRepository extends BaseRepository<VehicleDocument> {
  protected readonly modelName = 'vehicleDocument';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a vehicle document with the given name, or fetch if it already exists
   */
  async findOneByIdentifier(
    identifier: string,
  ): Promise<VehicleDocument | null> {
    // Try to find by identifier
    return await this.findOne({ where: { identifier } });
  }
}

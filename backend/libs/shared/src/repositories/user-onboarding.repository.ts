import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { UserOnboarding } from './models/userOnboard.model';

@Injectable()
export class UserOnboardingRepository extends BaseRepository<UserOnboarding> {
  protected readonly modelName = 'userOnboarding';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async createUserOnboarding(
    data: Omit<UserOnboarding, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<UserOnboarding> {
    return this.create(data);
  }

  async findAllUserOnboardings(): Promise<UserOnboarding[]> {
    return this.findMany();
  }

  async findUserOnboardingById(id: string): Promise<UserOnboarding | null> {
    return this.findById(id);
  }

  async updateUserOnboarding(
    id: string,
    data: Partial<UserOnboarding>,
  ): Promise<UserOnboarding> {
    return this.updateById(id, data);
  }

  async deleteUserOnboarding(id: string): Promise<UserOnboarding> {
    return this.softDeleteById(id);
  }

  async findOneByUserIdAndRoleId(
    userId: string,
    roleId: string,
  ): Promise<UserOnboarding | null> {
    return this.findOne({ where: { userId, roleId } });
  }
}

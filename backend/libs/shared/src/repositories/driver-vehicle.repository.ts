import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { DriverVehicle } from './models/driverVehicle.model';

@Injectable()
export class DriverVehicleRepository extends BaseRepository<DriverVehicle> {
  protected readonly modelName = 'driverVehicle';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async createDriverVehicle(
    data: Omit<DriverVehicle, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<DriverVehicle> {
    return this.create(data);
  }

  async findAllDriverVehicles(): Promise<DriverVehicle[]> {
    return this.findMany();
  }

  async findDriverVehicleById(id: string): Promise<DriverVehicle | null> {
    return this.findById(id);
  }

  async updateDriverVehicle(
    id: string,
    data: Partial<DriverVehicle>,
  ): Promise<DriverVehicle> {
    return this.updateById(id, data);
  }

  async deleteDriverVehicle(id: string): Promise<DriverVehicle> {
    return this.softDeleteById(id);
  }

  async paginateDriverVehicles(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, options);
  }
}

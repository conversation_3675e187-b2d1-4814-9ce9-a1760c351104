import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { City } from './models/city.model';

@Injectable()
export class CityRepository extends BaseRepository<City> {
  protected readonly modelName = 'city';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async createCity(
    data: Omit<City, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<City> {
    return this.create(data);
  }

  async findAllCities(): Promise<City[]> {
    return this.findMany();
  }

  async findCityById(id: string): Promise<City | null> {
    return this.findById(id);
  }

  async updateCity(id: string, data: Partial<City>): Promise<City> {
    return this.updateById(id, data);
  }

  async deleteCity(id: string): Promise<City> {
    return this.softDeleteById(id);
  }

  async paginateCities(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, options);
  }
}

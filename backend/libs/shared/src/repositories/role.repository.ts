import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { AuthRole } from '../common/constants/constants';
import { Role, UserRole } from '@prisma/client';

@Injectable()
export class RoleRepository extends BaseRepository<Role> {
  protected readonly modelName = 'role';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Find role by name
   */
  async findByName(name: string): Promise<Role | null> {
    return this.findOne({
      where: { name },
    });
  }

  /**
   * Find role by AuthRole enum
   */
  async findByAuthRole(authRole: AuthRole): Promise<Role | null> {
    return this.findByName(authRole);
  }

  /**
   * Get all available roles
   */
  async getAllRoles(): Promise<Role[]> {
    return this.findMany({
      orderBy: { name: 'asc' },
    });
  }

  /**
   * Create role if not exists
   */
  async createIfNotExists(name: string, description?: string): Promise<Role> {
    const existingRole = await this.findByName(name);
    if (existingRole) {
      return existingRole;
    }

    return this.create({
      name,
      description,
    } as Omit<Role, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>);
  }

  /**
   * Get roles with their permissions
   */
  async getRolesWithPermissions(): Promise<Role[]> {
    return this.findMany({
      include: {
        rolePermissions: {
          include: {
            permission: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    });
  }

  /**
   * Get role with permissions by name
   */
  async getRoleWithPermissionsByName(name: string): Promise<Role | null> {
    return this.findOne({
      where: { name },
      include: {
        rolePermissions: {
          include: {
            permission: true,
          },
        },
      },
    });
  }
}

@Injectable()
export class UserRoleRepository extends BaseRepository<UserRole> {
  protected readonly modelName = 'userRole';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Assign role to user
   */
  async assignRole(userId: string, roleId: string): Promise<UserRole> {
    // Check if user already has this role
    const existing = await this.findOne({
      where: { userId, roleId },
    });

    if (existing) {
      return existing;
    }

    return this.create({
      userId,
      roleId,
    } as Omit<UserRole, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>);
  }

  /**
   * Assign role to user by role name
   */
  async assignRoleByName(userId: string, roleName: string): Promise<UserRole> {
    return this.transaction(async (prisma) => {
      // Find role by name
      const role = await prisma.role.findUnique({
        where: { name: roleName },
      });

      if (!role) {
        throw new Error(`Role '${roleName}' not found`);
      }

      // Check if user already has this role
      const existing = await prisma.userRole.findFirst({
        where: {
          userId,
          roleId: role.id,
          deletedAt: null,
        },
      });

      if (existing) {
        return existing;
      }

      // Assign role
      return prisma.userRole.create({
        data: {
          userId,
          roleId: role.id,
        },
      });
    });
  }

  /**
   * Remove role from user
   */
  async removeRole(userId: string, roleId: string): Promise<UserRole> {
    const userRole = await this.findOne({
      where: { userId, roleId },
    });

    if (!userRole) {
      throw new Error(`User does not have this role`);
    }

    return this.softDeleteById(userRole.id);
  }

  /**
   * Remove role from user by role name
   */
  async removeRoleByName(userId: string, roleName: string): Promise<UserRole> {
    return this.transaction(async (prisma) => {
      const userRole = await prisma.userRole.findFirst({
        where: {
          userId,
          deletedAt: null,
          role: {
            name: roleName,
          },
        },
      });

      if (!userRole) {
        throw new Error(`User does not have role '${roleName}'`);
      }

      return prisma.userRole.update({
        where: { id: userRole.id },
        data: {
          deletedAt: new Date(),
          updatedAt: new Date(),
        },
      });
    });
  }

  /**
   * Get all roles for a user
   */
  async getUserRoles(userId: string): Promise<Role[]> {
    const userRoles = (await this.findMany({
      where: { userId },
      include: {
        role: true,
      },
    })) as (UserRole & { role: Role })[];

    return userRoles.map((ur) => ur.role);
  }

  /**
   * Get user role names
   */
  async getUserRoleNames(userId: string): Promise<string[]> {
    const roles = await this.getUserRoles(userId);
    return roles.map((role) => role.name);
  }

  /**
   * Check if user has specific role
   */
  async userHasRole(userId: string, roleName: string): Promise<boolean> {
    const userRole = await this.findOne({
      where: {
        userId,
        role: {
          name: roleName,
        },
      },
    });

    return !!userRole;
  }

  /**
   * Check if user has any of the specified roles
   */
  async userHasAnyRole(userId: string, roleNames: string[]): Promise<boolean> {
    const count = await this.count({
      userId,
      role: {
        name: {
          in: roleNames,
        },
      },
    });

    return count > 0;
  }

  /**
   * Get users with specific role
   */
  async getUsersByRole(roleName: string): Promise<any[]> {
    return this.findMany({
      where: {
        role: {
          name: roleName,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            phoneNumber: true,
            createdAt: true,
          },
        },
      },
    });
  }

  /**
   * Replace user roles (remove all existing and add new ones)
   */
  async replaceUserRoles(
    userId: string,
    roleNames: string[],
  ): Promise<UserRole[]> {
    return this.transaction(async (prisma) => {
      // Soft delete existing roles
      await prisma.userRole.updateMany({
        where: {
          userId,
          deletedAt: null,
        },
        data: {
          deletedAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Add new roles
      const newUserRoles = [];
      for (const roleName of roleNames) {
        const role = await prisma.role.findUnique({
          where: { name: roleName },
        });

        if (!role) {
          throw new Error(`Role '${roleName}' not found`);
        }

        const userRole = await prisma.userRole.create({
          data: {
            userId,
            roleId: role.id,
          },
        });

        newUserRoles.push(userRole);
      }

      return newUserRoles;
    });
  }

  /**
   * Get role statistics
   */
  async getRoleStatistics(): Promise<
    {
      roleName: string;
      userCount: number;
      description?: string;
    }[]
  > {
    return this.raw(`
      SELECT 
        r.name as "roleName",
        r.description,
        COUNT(DISTINCT ur.user_id) as "userCount"
      FROM roles r
      LEFT JOIN user_roles ur ON r.id = ur.role_id AND ur.deleted_at IS NULL
      WHERE r.deleted_at IS NULL
      GROUP BY r.id, r.name, r.description
      ORDER BY "userCount" DESC, r.name ASC
    `);
  }
}

import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { VehicleType } from './models/vehicleType.model';

@Injectable()
export class VehicleTypeRepository extends BaseRepository<VehicleType> {
  protected readonly modelName = 'vehicleType';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Find all vehicles associated with a given city_id.
   * @param cityId - The ID of the city
   */
  async findActiveVehicleTypesByCityId(cityId: string): Promise<VehicleType[]> {
    return this.findMany({
      where: {
        cityProducts: {
          some: {
            cityId: cityId,
            isEnabled: true, // Assuming you want only active vehicles
          },
        },
      },
    });
  }
  /**
   * Create a new vehicle record.
   * @param data - Vehicle data excluding id and timestamps
   */

  async createVehicleType(
    data: Omit<VehicleType, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<VehicleType> {
    return this.create(data);
  }

  async findAllVehicleTypes(): Promise<VehicleType[]> {
    return this.findMany();
  }

  async findVehicleTypeById(id: string): Promise<VehicleType | null> {
    return this.findById(id);
  }

  async updateVehicleType(
    id: string,
    data: Partial<VehicleType>,
  ): Promise<VehicleType> {
    return this.updateById(id, data);
  }

  async deleteVehicleType(id: string): Promise<VehicleType> {
    return this.softDeleteById(id);
  }

  async paginateVehicleTypes(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, options);
  }
}

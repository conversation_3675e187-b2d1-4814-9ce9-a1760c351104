import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { RefreshToken } from '@prisma/client';

@Injectable()
export class RefreshTokenRepository extends BaseRepository<RefreshToken> {
  protected readonly modelName = 'refreshToken';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Find refresh token by token string
   */
  async findByToken(token: string): Promise<RefreshToken | null> {
    return this.findOne({
      where: { token, revoked: false },
      include: { user: true },
    });
  }

  /**
   * Find all active refresh tokens for a user
   */
  async findActiveTokensByUserId(userId: string): Promise<RefreshToken[]> {
    return this.findMany({
      where: {
        userId,
        revoked: false,
        expiresAt: {
          gt: new Date(),
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Create a new refresh token
   */
  async createToken(
    userId: string,
    token: string,
    expiresAt: Date,
  ): Promise<RefreshToken> {
    return this.create({
      userId,
      token,
      expiresAt,
      revoked: false,
    } as Omit<RefreshToken, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>);
  }

  /**
   * Revoke a refresh token
   */
  async revokeToken(token: string): Promise<RefreshToken> {
    const refreshToken = await this.findByToken(token);
    if (!refreshToken) {
      throw new Error('Refresh token not found');
    }

    return this.updateById(refreshToken.id, {
      revoked: true,
    } as Partial<RefreshToken>);
  }

  /**
   * Revoke all tokens for a user
   */
  async revokeAllUserTokens(userId: string): Promise<{ count: number }> {
    return this.updateMany(
      {
        userId,
        revoked: false,
      },
      {
        revoked: true,
      } as Partial<RefreshToken>,
    );
  }

  /**
   * Clean up expired tokens
   */
  async cleanupExpiredTokens(): Promise<{ count: number }> {
    return this.updateMany(
      {
        expiresAt: {
          lt: new Date(),
        },
        revoked: false,
      },
      {
        revoked: true,
      } as Partial<RefreshToken>,
    );
  }

  /**
   * Get token statistics for a user
   */
  async getUserTokenStats(userId: string): Promise<{
    total: number;
    active: number;
    expired: number;
    revoked: number;
  }> {
    const now = new Date();

    const [total, active, expired, revoked] = await Promise.all([
      this.count({ userId }),
      this.count({
        userId,
        revoked: false,
        expiresAt: { gt: now },
      }),
      this.count({
        userId,
        revoked: false,
        expiresAt: { lte: now },
      }),
      this.count({
        userId,
        revoked: true,
      }),
    ]);

    return { total, active, expired, revoked };
  }

  /**
   * Find tokens expiring soon
   */
  async findTokensExpiringSoon(
    hoursFromNow: number = 24,
  ): Promise<RefreshToken[]> {
    const expiryThreshold = new Date(
      Date.now() + hoursFromNow * 60 * 60 * 1000,
    );

    return this.findMany({
      where: {
        revoked: false,
        expiresAt: {
          lte: expiryThreshold,
          gt: new Date(),
        },
      },
      include: { user: true },
    });
  }

  /**
   * Transaction-safe token rotation
   */
  async rotateToken(
    oldToken: string,
    newToken: string,
    newExpiresAt: Date,
  ): Promise<RefreshToken> {
    return this.transaction(async (prisma) => {
      // Find and revoke old token
      const oldRefreshToken = await prisma.refreshToken.findUnique({
        where: { token: oldToken },
      });

      if (!oldRefreshToken || oldRefreshToken.revoked) {
        throw new Error('Invalid or revoked refresh token');
      }

      if (oldRefreshToken.expiresAt < new Date()) {
        throw new Error('Refresh token has expired');
      }

      // Revoke old token
      await prisma.refreshToken.update({
        where: { id: oldRefreshToken.id },
        data: { revoked: true, updatedAt: new Date() },
      });

      // Create new token
      return prisma.refreshToken.create({
        data: {
          userId: oldRefreshToken.userId,
          token: newToken,
          expiresAt: newExpiresAt,
          revoked: false,
        },
      });
    });
  }

  /**
   * Check if token is valid and not expired
   */
  async isTokenValid(token: string): Promise<boolean> {
    const refreshToken = await this.findOne({
      where: {
        token,
        revoked: false,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    return !!refreshToken;
  }

  /**
   * Get user ID from valid token
   */
  async getUserIdFromToken(token: string): Promise<string | null> {
    const refreshToken = await this.findByToken(token);
    return refreshToken?.userId || null;
  }

  /**
   * Revoke tokens by user ID with optional device filtering
   */
  async revokeUserTokensExcept(
    userId: string,
    keepTokenId?: string,
  ): Promise<{ count: number }> {
    const whereClause: any = {
      userId,
      revoked: false,
    };

    if (keepTokenId) {
      whereClause.id = {
        not: keepTokenId,
      };
    }

    return this.updateMany(whereClause, {
      revoked: true,
    } as Partial<RefreshToken>);
  }
}

import { BaseEntity } from '../base.repository';
import { KycDocument } from './kycDocument.model';
import { UserProfile } from './userProfile.model';

export enum KycStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export interface DriverKyc extends BaseEntity {
  userProfileId: string;
  kycDocumentId: string;
  documentNumber?: string | null;
  documentUrl?: string | null;
  documentFields?: any | null;
  expiryDate?: Date | null;
  fromDigilocker: boolean;
  status: KycStatus;
  rejectionNote?: string | null;

  // Relations
  userProfile?: UserProfile;
  kycDocument?: KycDocument;
}

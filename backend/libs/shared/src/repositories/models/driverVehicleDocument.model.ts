import { BaseEntity } from '../base.repository';

export interface DriverVehicleDocument extends BaseEntity {
  driverVehicleId: string;
  vehicleDocumentId: string;
  details: any;
  documentUrl?: string | null;
  documentFields?: any | null;
  // Relations
  driverVehicle?: any; // Replace 'any' with DriverVehicle if available
  vehicleDocument?: any; // Replace 'any' with VehicleDocument if available
}

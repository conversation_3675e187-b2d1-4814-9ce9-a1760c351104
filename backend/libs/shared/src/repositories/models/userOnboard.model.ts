import { BaseEntity } from '../base.repository';
import { Role } from './role.model';

export enum OnboardingStep {
  PHONE_VERIFICATION = 'PHONE_VERIFICATION',
  LANGUAGE_UPDATE = 'LANGUAGE_UPDATE',
  PROFILE_SETUP = 'PROFILE_SETUP',
  VEHICLE_REGISTRATION = 'VEHICLE_REGISTRATION',
  VEHICLE_DOCUMENTS_VERIFICATION = 'VEHICLE_DOCUMENTS_VERIFICATION',

  KYC_DOCUMENT_UPLOAD = 'KYC_DOCUMENT_UPLOAD',
  PROFILE_PHOTO_UPLOAD = 'PROFILE_PHOTO_UPLOAD',

  BACKGROUND_CHECK = 'BACKGROUND_CHECK',
  TRAINING_COMPLETION = 'TRAINING_COMPLETION',
  ACCOUNT_ACTIVATION = 'ACCOUNT_ACTIVATION',
  COMPLETED = 'COMPLETED',
}

export interface UserOnboarding extends BaseEntity {
  userId: string;
  roleId: string;
  currentStep: OnboardingStep;
  lastActiveAt?: Date;
  role?: Role;
  // Optionally add user relation if needed
}

import { BaseEntity } from '../base.repository';
import { City } from './city.model';
import { DriverVehicle } from './driverVehicle.model';
import { Product } from './product.model';

export interface CityProduct extends BaseEntity {
  cityId: string;
  productId: string;
  isEnabled: boolean;
  vehicleTypeId: string;
  // Relations
  city?: City; // Replace 'any' with City if available
  product?: Product; // Replace 'any' with Product if available
  driverVehicles?: DriverVehicle[]; // Replace 'any' with DriverVehicle[] if available
}

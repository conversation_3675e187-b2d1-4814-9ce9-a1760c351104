import { BaseEntity } from '../base.repository';
import { Role } from './role.model';

export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}

export interface UserProfile extends BaseEntity {
  userId: string;
  roleId: string;
  firstName: string;
  lastName: string;
  cityId?: string;
  referralCode: string;
  profilePictureUrl?: string;
  gender?: Gender;
  dob?: Date;
  role?: Role;
  // Optionally add city and user relations if needed
}

import { BaseEntity } from '../base.repository';
import { CityProduct } from './cityProduct.model';
import { DriverVehicle } from './driverVehicle.model';
import { Product } from './product.model';

export interface VehicleType extends BaseEntity {
  name: string;
  description?: string | null;
  image?: string | null;

  // Relations
  cityProducts?: CityProduct[];
  driverVehicles?: DriverVehicle[]; // Replace 'any' with DriverVehicle[] if available
  products?: Product[]; // Replace 'any' with Product[] if available
}

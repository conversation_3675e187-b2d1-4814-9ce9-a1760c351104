export enum DriverVehicleStatus {
  created = 'created',
  pending = 'pending',
  verified = 'verified',
  rejected = 'rejected',
}
import { BaseEntity } from '../base.repository';
import { CityProduct } from './cityProduct.model';
import { DriverVehicleDocument } from './driverVehicleDocument.model';
import { UserProfile } from './userProfile.model';
import { VehicleType } from './vehicleType.model';

export interface DriverVehicle extends BaseEntity {
  userProfileId: string;
  cityProductId?: string | null;
  vehicleTypeId: string;
  vehicleNumber?: string | null;
  isNocRequired: boolean;
  isPrimary: boolean;
  status: DriverVehicleStatus;

  // Relations
  userProfile?: UserProfile;
  vehicleType?: VehicleType;
  cityProduct?: CityProduct;
  driverVehicleDocuments?: DriverVehicleDocument[];
}

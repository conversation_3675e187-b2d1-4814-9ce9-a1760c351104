import { BaseEntity } from '../base.repository';
import { Country } from './country.model';
import { DriverKyc } from './driverKyc.model';

export interface KycDocument extends BaseEntity {
  countryId: string;
  name: string;
  identifier: string;
  requiredFields?: any | null;
  isMandatory: boolean;

  // Relations
  country?: Country;
  driverKycs?: DriverKyc[]; // Replace 'any' with DriverKyc[] if available
}

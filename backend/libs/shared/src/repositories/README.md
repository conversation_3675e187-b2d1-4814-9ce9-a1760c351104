# Base Repository

A comprehensive base repository implementation for NestJS with Prisma that provides CRUD operations, soft delete functionality, and restore capabilities with dynamic Prisma models.

## Features

- ✅ **Full CRUD Operations**: Create, Read, Update, Delete
- ✅ **Soft Delete Support**: Implements soft delete with `deletedAt` field
- ✅ **Restore Functionality**: Restore soft-deleted records
- ✅ **Hard Delete**: Permanent deletion when needed
- ✅ **Dynamic Prisma Models**: Works with any Prisma model
- ✅ **Pagination**: Built-in pagination support
- ✅ **Transaction Support**: Execute operations within transactions
- ✅ **Raw Query Support**: Execute custom SQL queries
- ✅ **TypeScript Support**: Fully typed with generics
- ✅ **Flexible Filtering**: Advanced filtering and querying options

## Prerequisites

Your Prisma models should have the following base fields for full functionality:

```prisma
model YourModel {
  id          String    @id @default(uuid()) @db.Uuid
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at") @db.Timestamptz
  
  // Your custom fields here
  
  @@map("your_table_name")
}
```

## Usage

### 1. Create a Repository

```typescript
import { Injectable } from '@nestjs/common';
import { BaseRepository, BaseEntity } from '@shared/shared/repositories';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';

// Define your entity interface
export interface User extends BaseEntity {
  phoneNumber: string;
  email?: string | null;
}

@Injectable()
export class UserRepository extends BaseRepository<User> {
  protected readonly modelName = 'user'; // Must match your Prisma model name

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  // Add custom methods specific to your model
  async findByPhoneNumber(phoneNumber: string): Promise<User | null> {
    return this.findOne({
      where: { phoneNumber },
    });
  }
}
```

### 2. Register in Module

```typescript
import { Module } from '@nestjs/common';
import { UserRepository } from './user.repository';
import { PrismaModule } from '@shared/shared/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  providers: [UserRepository],
  exports: [UserRepository],
})
export class UserModule {}
```

### 3. Use in Services

```typescript
import { Injectable } from '@nestjs/common';
import { UserRepository, User } from './user.repository';

@Injectable()
export class UserService {
  constructor(private readonly userRepository: UserRepository) {}

  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>): Promise<User> {
    return this.userRepository.create(userData);
  }

  async getUsers(): Promise<User[]> {
    return this.userRepository.findMany();
  }

  async getUserById(id: string): Promise<User | null> {
    return this.userRepository.findById(id);
  }

  async updateUser(id: string, data: Partial<User>): Promise<User> {
    return this.userRepository.updateById(id, data);
  }

  async deleteUser(id: string): Promise<User> {
    return this.userRepository.softDeleteById(id);
  }

  async restoreUser(id: string): Promise<User> {
    return this.userRepository.restoreById(id);
  }
}
```

## Available Methods

### Create Operations
- `create(data, options?)` - Create a new record
- `upsert(where, create, update, options?)` - Create or update a record

### Read Operations
- `findMany(options?)` - Find multiple records
- `findOne(options)` - Find a single record
- `findById(id, options?)` - Find by ID
- `findUnique(options)` - Find unique record
- `findSoftDeleted(options?)` - Find soft-deleted records only
- `count(where?, includeSoftDeleted?)` - Count records
- `exists(where, includeSoftDeleted?)` - Check if record exists
- `paginate(page, limit, options?)` - Get paginated results

### Update Operations
- `update(options)` - Update a record
- `updateById(id, data, options?)` - Update by ID
- `updateMany(where, data)` - Update multiple records

### Delete Operations
- `softDelete(options)` - Soft delete a record
- `softDeleteById(id)` - Soft delete by ID
- `softDeleteMany(where)` - Soft delete multiple records
- `hardDelete(options)` - Permanently delete a record
- `hardDeleteById(id)` - Permanently delete by ID
- `hardDeleteMany(where)` - Permanently delete multiple records

### Restore Operations
- `restore(options)` - Restore a soft-deleted record
- `restoreById(id)` - Restore by ID
- `restoreMany(where)` - Restore multiple records

### Utility Operations
- `transaction(fn)` - Execute operations in a transaction
- `raw(query, ...values)` - Execute raw SQL query

## Examples

### Basic CRUD Operations

```typescript
// Create
const user = await userRepository.create({
  phoneNumber: '+1234567890',
  email: '<EMAIL>'
});

// Read
const users = await userRepository.findMany({
  where: { email: { contains: '@gmail.com' } },
  orderBy: { createdAt: 'desc' },
  take: 10
});

// Update
const updatedUser = await userRepository.updateById(userId, {
  email: '<EMAIL>'
});

// Soft Delete
const deletedUser = await userRepository.softDeleteById(userId);

// Restore
const restoredUser = await userRepository.restoreById(userId);

// Hard Delete (permanent)
const permanentlyDeleted = await userRepository.hardDeleteById(userId);
```

### Advanced Filtering

```typescript
// Find with complex conditions
const activeUsers = await userRepository.findMany({
  where: {
    AND: [
      { email: { not: null } },
      { phoneNumber: { startsWith: '+1' } }
    ]
  },
  include: {
    profile: true,
    orders: {
      where: { status: 'active' }
    }
  },
  orderBy: [
    { createdAt: 'desc' },
    { email: 'asc' }
  ]
});
```

### Pagination

```typescript
const paginatedUsers = await userRepository.paginate(1, 20, {
  where: { email: { not: null } },
  orderBy: { createdAt: 'desc' }
});

console.log(paginatedUsers.data); // User records
console.log(paginatedUsers.meta); // Pagination info
```

### Transactions

```typescript
const result = await userRepository.transaction(async (prisma) => {
  const user = await prisma.user.create({
    data: { phoneNumber: '+1234567890', email: '<EMAIL>' }
  });

  await prisma.userProfile.create({
    data: { userId: user.id, firstName: 'John', lastName: 'Doe' }
  });

  return user;
});
```

### Including Soft-Deleted Records

```typescript
// Include soft-deleted records in results
const allUsers = await userRepository.findMany({
  includeSoftDeleted: true
});

// Find only soft-deleted records
const deletedUsers = await userRepository.findSoftDeleted();

// Count including soft-deleted
const totalCount = await userRepository.count({}, true);
```

### Raw Queries

```typescript
// Execute raw SQL
const result = await userRepository.raw(
  'SELECT COUNT(*) as total FROM users WHERE created_at > $1',
  new Date('2024-01-01')
);
```

## TypeScript Support

The base repository is fully typed and provides excellent IntelliSense support:

```typescript
interface User extends BaseEntity {
  phoneNumber: string;
  email?: string | null;
  // TypeScript will enforce these fields in all operations
}

// TypeScript will infer the correct return types
const user: User | null = await userRepository.findById('123');
const users: User[] = await userRepository.findMany();
```

## Error Handling

The repository methods will throw Prisma errors which can be caught and handled appropriately:

```typescript
try {
  const user = await userRepository.create(userData);
  return user;
} catch (error) {
  if (error.code === 'P2002') {
    throw new ConflictException('User already exists');
  }
  throw error;
}
```

## Best Practices

1. **Model Name**: Ensure the `modelName` property matches your Prisma model name exactly
2. **Entity Interface**: Always define an interface extending `BaseEntity`
3. **Error Handling**: Implement proper error handling for database operations
4. **Transactions**: Use transactions for operations that need to be atomic
5. **Indexes**: Add appropriate database indexes for frequently queried fields
6. **Soft Delete**: Use soft delete for user data that might need to be restored
7. **Hard Delete**: Only use hard delete for cleanup operations or when legally required

import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { Country } from './models/country.model';

@Injectable()
export class CountryRepository extends BaseRepository<Country> {
  protected readonly modelName = 'country';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async createCountry(
    data: Omit<Country, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Country> {
    return this.create(data);
  }

  async findAllCountries(): Promise<Country[]> {
    return this.findMany();
  }

  async findCountryById(id: string): Promise<Country | null> {
    return this.findById(id);
  }

  async updateCountry(id: string, data: Partial<Country>): Promise<Country> {
    return this.updateById(id, data);
  }

  async deleteCountry(id: string): Promise<Country> {
    return this.softDeleteById(id);
  }

  async paginateCountries(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, options);
  }
}

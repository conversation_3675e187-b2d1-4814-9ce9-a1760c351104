import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { KycDocument } from './models/kycDocument.model';

@Injectable()
export class KycDocumentRepository extends BaseRepository<KycDocument> {
  protected readonly modelName = 'kycDocument';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new KYC document
   */
  async createKycDocument(
    data: Omit<KycDocument, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<KycDocument> {
    return this.create(data);
  }

  /**
   * Find all KYC documents
   */
  async findAllKycDocuments(): Promise<KycDocument[]> {
    return this.findMany({
      include: {
        country: true,
      },
    });
  }

  /**
   * Find KYC document by ID
   */
  async findKycDocumentById(id: string): Promise<KycDocument | null> {
    return this.findById(id, {
      include: {
        country: true,
      },
    });
  }

  /**
   * Find KYC documents by country ID
   */
  async findKycDocumentsByCountryId(countryId: string): Promise<KycDocument[]> {
    return this.findMany({
      where: {
        countryId,
      },
      include: {
        country: true,
      },
    });
  }

  /**
   * Find mandatory KYC documents by country ID
   */
  async findMandatoryKycDocumentsByCountryId(
    countryId: string,
  ): Promise<KycDocument[]> {
    return this.findMany({
      where: {
        countryId,
        isMandatory: true,
      },
      include: {
        country: true,
      },
    });
  }

  /**
   * Update KYC document
   */
  async updateKycDocument(
    id: string,
    data: Partial<KycDocument>,
  ): Promise<KycDocument> {
    return this.updateById(id, data);
  }

  /**
   * Soft delete KYC document
   */
  async deleteKycDocument(id: string): Promise<KycDocument> {
    return this.softDeleteById(id);
  }

  /**
   * Paginate KYC documents with optional filtering
   */
  async paginateKycDocuments(page: number, limit: number, options: any = {}) {
    const defaultOptions = {
      include: {
        country: true,
      },
      ...options,
    };

    return this.paginate(page, limit, defaultOptions);
  }
}

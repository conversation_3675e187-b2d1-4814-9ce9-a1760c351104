import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { DriverVehicleDocument } from './models/driverVehicleDocument.model';

@Injectable()
export class DriverVehicleDocumentRepository extends BaseRepository<DriverVehicleDocument> {
  protected readonly modelName = 'driverVehicleDocument';

  constructor(prisma: PrismaService) {
    super(prisma);
  }
}

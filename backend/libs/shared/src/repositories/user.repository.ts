import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { User } from '@prisma/client';

@Injectable()
export class UserRepository extends BaseRepository<User> {
  protected readonly modelName = 'user';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async findByPhoneNumber(phoneNumber: string): Promise<User | null> {
    return this.findOne({
      where: { phoneNumber },
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.findOne({
      where: { email },
    });
  }

  async findDriverByEmail(email: string, roleId: string): Promise<User | null> {
    return this.findOne({
      where: {
        email,
        userRoles: {
          some: { roleId },
        },
      },
    });
  }

  async findActiveUsers(): Promise<User[]> {
    return this.findMany({
      where: {},
      orderBy: { createdAt: 'desc' },
    });
  }

  async findSoftDeletedUsers(): Promise<User[]> {
    return this.findSoftDeleted({
      orderBy: { deletedAt: 'desc' },
    });
  }

  async createUserWithTransaction(
    userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<User> {
    return this.transaction(async (prisma) => {
      // You can perform multiple operations here
      const user = await prisma.user.create({
        data: userData,
      });

      return user;
    });
  }

  async updateUserEmail(userId: string, email: string): Promise<User> {
    return this.updateById(userId, { email });
  }

  async updateTermsAndConditionsAcceptance(
    userId: string,
    accepted: boolean,
  ): Promise<User> {
    return this.prisma.user.update({
      where: { id: userId },
      data: {
        isPolicyAllowed: accepted,
      },
    });
  }
}

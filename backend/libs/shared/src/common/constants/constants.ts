/**
 * Auth providers for the application.
 *  Currently user's can login to the application using one of the following providers.
 *  Each provider corresponds to a specific authentication method.
 *  This enum is used to identify the authentication method used by the user.
 */
export enum AuthProvider {
  GOOGLE = 'google',
  APPLE = 'apple',
  PHONE = 'phone',
  EMAIL = 'email',
}

/**
 * Auth roles for the application.
 *  Currently user's having one of the following roles can  only login to the application.
 */
export enum AuthRole {
  RIDER = 'rider',
  DRIVER = 'driver',
  SUPER_ADMIN = 'super_admin',
}

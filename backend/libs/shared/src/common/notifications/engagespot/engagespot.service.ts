import { EngagespotClient } from '@engagespot/node';
import { Injectable, Logger } from '@nestjs/common';
import { AppConfigService } from '@shared/shared/config';

interface Recipient {
  identifier: string;
  email?: string;
  phoneNumber?: string;
}

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  private client: any;

  constructor(private appConfigService: AppConfigService) {
    this.client = EngagespotClient({
      apiKey: this.appConfigService.engagespotApiKey,
      apiSecret: this.appConfigService.engagespotApiSecret,
    });
  }

  /**
   * Send a notification to one or more recipients
   * @param workflowIdentifier - The workflow identifier for the notification
   * @param recipients - Array of recipient identifiers (usually email addresses)
   * @param data - Optional data to be included in the notification
   * @returns Promise resolving to the send result
   * @throws Error if notification sending fails
   */
  async sendNotification(
    workflowIdentifier: string,
    recipients: Recipient[],
    data: any | null = null,
  ): Promise<any> {
    try {
      this.logger.debug(
        `Sending notification with workflow ${workflowIdentifier} to ${recipients.length} recipients`,
      );

      const response = await this.client.send({
        notification: {
          workflow: {
            identifier: workflowIdentifier,
          },
        },
        sendTo: {
          recipients,
        },
        data,
      });

      this.logger.debug(
        `Successfully sent notification with workflow ${workflowIdentifier}`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Failed to send notification with workflow ${workflowIdentifier}:`,
        error,
      );
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error(`Notification sending failed: ${errorMessage}`);
    }
  }
}

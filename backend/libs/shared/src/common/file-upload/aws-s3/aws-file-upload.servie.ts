import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';

import { AppConfigService } from '@shared/shared/config';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

interface FileDetails {
  key: string;
  url: string;
  contentType: string;
  size: number;
}

@Injectable()
export class AwsFileUploadService {
  private s3Client: S3Client;

  constructor(private appConfigService: AppConfigService) {
    this.s3Client = new S3Client({
      region: this.appConfigService.awsRegion,
      credentials: {
        accessKeyId: this.appConfigService.awsAccessKeyId,
        secretAccessKey: this.appConfigService.awsSecretAccessKey,
      },
    });
  }

  async uploadFile(file: Express.Multer.File): Promise<FileDetails> {
    const key = `uploads/${Date.now()}-${file.originalname}`;

    const putCommand = new PutObjectCommand({
      Bucket: this.appConfigService.awsBucketName,
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
      ACL: 'private',
    });

    try {
      await this.s3Client.send(putCommand);

      // Generate the URL
      const getCommand = new GetObjectCommand({
        Bucket: this.appConfigService.awsBucketName,
        Key: key,
      });
      const url = await getSignedUrl(this.s3Client, getCommand, {
        expiresIn: 3600,
      });

      return {
        key: key,
        url: url,
        contentType: file.mimetype,
        size: file.size,
      };
    } catch (error) {
      const err = error as Error;
      throw new Error(`Failed to upload file to S3: ${err.message}`);
    }
  }

  async deleteFile(key: string): Promise<boolean> {
    const deleteCommand = new DeleteObjectCommand({
      Bucket: this.appConfigService.awsBucketName,
      Key: key,
    });

    try {
      await this.s3Client.send(deleteCommand);
      return true;
    } catch (error) {
      const err = error as Error;
      throw new Error(`Failed to delete file from S3: ${err.message}`);
    }
  }

  async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    const getCommand = new GetObjectCommand({
      Bucket: this.appConfigService.awsBucketName,
      Key: key,
    });

    try {
      return await getSignedUrl(this.s3Client, getCommand, { expiresIn });
    } catch (error) {
      const err = error as Error;
      throw new Error(`Failed to generate signed URL: ${err.message}`);
    }
  }
}

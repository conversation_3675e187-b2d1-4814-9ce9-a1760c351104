import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { AppConfigService } from '@shared/shared/config';
import axios, { AxiosResponse } from 'axios';

@Injectable()
export class CashfreeVehicleVerificationService {
  private readonly logger = new Logger(CashfreeVehicleVerificationService.name);

  constructor(private appConfigService: AppConfigService) {}

  /**
   * Verify a vehicle using Cashfree API with proper flow:
   * 1. Check if verification_id exists
   * 2. If exists and status is VALID, return details
   * 3. If not exists, create new verification
   * 4. Return details only if status is VALID
   * @param referenceId - Unique reference/verification id
   * @param vehicleNumber - Vehicle registration number
   */
  async verifyVehicle(
    referenceId: string,
    vehicleNumber: string,
  ): Promise<any> {
    // Log environment and configuration for debugging
    this.logger.debug(
      `Cashfree Environment: ${this.appConfigService.cashfreeEnvironment}`,
    );
    this.logger.debug(
      `Using Client ID: ${this.appConfigService.cashfreeApiId?.substring(0, 8)}...`,
    );

    try {
      this.logger.debug(
        `Creating new verification for vehicle: ${vehicleNumber}`,
      );
      const newVerification = await this.createNewVerification(
        referenceId,
        vehicleNumber,
      );

      if (newVerification.status !== 'VALID') {
        throw new BadRequestException(
          `Vehicle verification failed: invalid or unregistered number.`,
        );
      }

      return newVerification;
    } catch (error: any) {
      this.logger.error('Vehicle verification error:', {
        message: error?.message,
        status: error?.response?.status,
        data: error?.response?.data,
        config: {
          url: error?.config?.url,
          method: error?.config?.method,
        },
      });
      throw new BadRequestException(
        `Vehicle verification failed: Invalid or unregistered number.`,
      );
    }
  }

  /**
   * Create new verification using POST endpoint
   * @param verificationId - Unique verification ID
   * @param vehicleNumber - Vehicle registration number
   */
  private async createNewVerification(
    verificationId: string,
    vehicleNumber: string,
  ): Promise<any> {
    try {
      // Use the correct API endpoint
      const baseUrl =
        this.appConfigService.cashfreeEnvironment === 'production'
          ? 'https://api.cashfree.com/verification/vehicle-rc'
          : 'https://sandbox.cashfree.com/verification/vehicle-rc';

      const headers = {
        'X-Client-Id': this.appConfigService.cashfreeApiId,
        'X-Client-Secret': this.appConfigService.cashfreeApiSecret,
        'Content-Type': 'application/json',
      };

      const body = {
        verification_id: verificationId,
        vehicle_number: vehicleNumber,
      };

      this.logger.debug(`API URL: ${baseUrl}`);
      this.logger.debug(
        `Creating new vehicle verification: ${JSON.stringify(body)}`,
      );
      this.logger.debug(`Creating new vehicle headders: ${headers}`);

      const response: AxiosResponse = await axios.post(baseUrl, body, {
        headers,
        timeout: 30000, // 30 second timeout
      });

      this.logger.debug('New verification response:', response.data);
      return response.data;
    } catch (error: any) {
      this.logger.error('Error creating new verification:', {
        message: error?.message,
        status: error?.response?.status,
        statusText: error?.response?.statusText,
        data: error?.response?.data,
        url: error?.config?.url,
        headers: error?.config?.headers,
      });

      throw error;
    }
  }

  /**
   * Get current server IP for debugging IP whitelisting issues
   */
  async getCurrentServerIP(): Promise<string> {
    try {
      const response = await axios.get('https://api.ipify.org?format=json');
      return response.data.ip;
    } catch (error) {
      this.logger.warn('Could not fetch server IP:', error);
      return 'unknown';
    }
  }
}

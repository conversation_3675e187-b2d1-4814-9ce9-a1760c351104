import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ProductRepository } from '../../repositories/product.repository';
import { Product } from '../../repositories/models/product.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';
import { VehicleTypeService } from '../vehicle-type/vehicle-type.service';

@Injectable()
export class ProductService {
  constructor(
    private readonly productRepository: ProductRepository,
    private readonly vehicleTypeService: VehicleTypeService,
  ) {}

  /**
   * Create a new product.
   * @param data - Product data excluding id and timestamps
   */
  async createProduct(
    data: Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Product> {
    // Validate that the vehicle type exists
    const vehicleType = await this.vehicleTypeService.findVehicleTypeById(
      data.vehicleTypeId,
    );
    if (!vehicleType) {
      throw new BadRequestException(
        `Vehicle type with ID ${data.vehicleTypeId} not found`,
      );
    }

    return this.productRepository.createProduct(data);
  }

  /**
   * Find all products.
   */
  async findAllProducts(): Promise<Product[]> {
    return this.productRepository.findAllProducts();
  }

  /**
   * Find product by ID.
   * @param id - Product ID
   */
  async findProductById(id: string): Promise<Product> {
    const product = await this.productRepository.findProductById(id);
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    return product;
  }

  /**
   * Update product by ID.
   * @param id - Product ID
   * @param data - Partial product data
   */
  async updateProduct(id: string, data: Partial<Product>): Promise<Product> {
    // Check if product exists
    await this.findProductById(id);

    // If vehicleTypeId is being updated, validate it exists
    if (data.vehicleTypeId) {
      const vehicleType = await this.vehicleTypeService.findVehicleTypeById(
        data.vehicleTypeId,
      );
      if (!vehicleType) {
        throw new BadRequestException(
          `Vehicle type with ID ${data.vehicleTypeId} not found`,
        );
      }
    }

    return this.productRepository.updateProduct(id, data);
  }

  /**
   * Delete product by ID (soft delete).
   * @param id - Product ID
   */
  async deleteProduct(id: string): Promise<Product> {
    // Check if product exists
    await this.findProductById(id);

    return this.productRepository.deleteProduct(id);
  }

  /**
   * Get paginated products.
   * @param paginationDto - Pagination parameters
   */
  async paginateProducts(paginationDto: PaginationDto) {
    const { page = 1, limit = 10 } = paginationDto;
    return this.productRepository.paginateProducts(page, limit);
  }

  /**
   * Find products by vehicle type ID.
   * @param vehicleTypeId - Vehicle type ID
   */
  async findProductsByVehicleTypeId(vehicleTypeId: string): Promise<Product[]> {
    // Validate that the vehicle type exists
    await this.vehicleTypeService.findVehicleTypeById(vehicleTypeId);

    return this.productRepository.findProductsByVehicleTypeId(vehicleTypeId);
  }

  /**
   * Find products by city ID.
   * @param cityId - City ID
   */
  async findProductsByCityId(cityId: string): Promise<Product[]> {
    return this.productRepository.findProductsByCityId(cityId);
  }

  /**
   * Find active products by city and vehicle type.
   * @param cityId - City ID
   * @param vehicleTypeId - Vehicle type ID
   */
  async findActiveProductsByCityAndVehicleType(
    cityId: string,
    vehicleTypeId: string,
  ): Promise<Product[]> {
    // Validate that the vehicle type exists
    await this.vehicleTypeService.findVehicleTypeById(vehicleTypeId);

    return this.productRepository.findActiveProductsByCityAndVehicleType(
      cityId,
      vehicleTypeId,
    );
  }

  /**
   * Search products by name.
   * @param searchTerm - Search term
   */
  async searchProductsByName(searchTerm: string): Promise<Product[]> {
    if (!searchTerm || searchTerm.trim().length === 0) {
      throw new BadRequestException('Search term cannot be empty');
    }

    return this.productRepository.findMany({
      where: {
        name: {
          contains: searchTerm,
          mode: 'insensitive',
        },
      },
      include: {
        vehicleType: true,
        cityProducts: true,
      },
    });
  }

  /**
   * Get product statistics.
   */
  async getProductStatistics() {
    const [totalProducts, productsByVehicleType] = await Promise.all([
      this.productRepository.count(),
      this.productRepository.findMany({
        select: {
          vehicleTypeId: true,
          vehicleType: {
            select: {
              name: true,
            },
          },
        },
      }),
    ]);

    // Group products by vehicle type
    const vehicleTypeStats = productsByVehicleType.reduce(
      (acc, product) => {
        const vehicleTypeName = product.vehicleType?.name || 'Unknown';
        acc[vehicleTypeName] = (acc[vehicleTypeName] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    return {
      totalProducts,
      productsByVehicleType: vehicleTypeStats,
    };
  }
}

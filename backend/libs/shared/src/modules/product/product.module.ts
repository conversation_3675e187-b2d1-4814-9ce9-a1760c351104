import { Module } from '@nestjs/common';
import { ProductService } from './product.service';
import { ProductRepository } from '../../repositories/product.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { VehicleTypeModule } from '../vehicle-type/vehicle-type.module';

@Module({
  imports: [VehicleTypeModule],
  providers: [ProductService, ProductRepository, PrismaService],
  exports: [ProductService, ProductRepository],
})
export class ProductModule {}

import { Module } from '@nestjs/common';
import { PrismaService } from '../../database/prisma/prisma.service';
import { VehicleTypeService } from './vehicle-type.service';
import { VehicleTypeRepository } from '@shared/shared/repositories/vehicle-type.repository';

@Module({
  providers: [VehicleTypeService, PrismaService, VehicleTypeRepository],
  exports: [VehicleTypeService],
})
export class VehicleTypeModule {}

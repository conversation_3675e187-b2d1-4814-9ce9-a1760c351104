import { Injectable, NotFoundException } from '@nestjs/common';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';
import { VehicleType } from '@shared/shared/repositories/models/vehicleType.model';
import { VehicleTypeRepository } from '@shared/shared/repositories/vehicle-type.repository';

@Injectable()
export class VehicleTypeService {
  constructor(private readonly vehicleTypeRepository: VehicleTypeRepository) {}

  /**
   * Create a new vehicle record.
   * @param data - Vehicle data excluding id and timestamps
   */
  async createVehicleType(
    data: Omit<VehicleType, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<VehicleType> {
    return this.vehicleTypeRepository.createVehicleType(data);
  }

  /**
   * Retrieve all vehicles from the repository.
   */
  async findAllVehicleTypes(): Promise<VehicleType[]> {
    return this.vehicleTypeRepository.findAllVehicleTypes();
  }

  /**
   * Find a vehicle by its ID. Throws if not found.
   * @param id - Vehicle ID
   */
  async findVehicleTypeById(id: string): Promise<VehicleType> {
    const vehicle = await this.vehicleTypeRepository.findVehicleTypeById(id);
    if (!vehicle)
      throw new NotFoundException(`Vehicle with ID ${id} not found`);
    return vehicle;
  }

  /**
   * Update an existing vehicle by ID.
   * @param id - Vehicle ID
   * @param data - Partial vehicle data to update
   */
  async updateVehicleType(
    id: string,
    data: Partial<VehicleType>,
  ): Promise<VehicleType> {
    await this.findVehicleTypeById(id);
    return this.vehicleTypeRepository.updateVehicleType(id, data);
  }

  /**
   * Delete a vehicle by ID after ensuring it exists.
   * @param id - Vehicle ID
   */
  async deleteVehicleType(id: string): Promise<VehicleType> {
    await this.findVehicleTypeById(id);
    return this.vehicleTypeRepository.deleteVehicleType(id);
  }

  /**
   * Paginate vehicles with optional search and sorting.
   * @param page - Page number (default 1)
   * @param limit - Items per page (default 10)
   * @param dto - Pagination and filter options
   */
  async paginateVehicleTypes(page = 1, limit = 10, dto?: PaginationDto) {
    const options = this.buildPaginateOptions(dto);
    // console.log(options)
    return this.vehicleTypeRepository.paginateVehicleTypes(
      page,
      limit,
      options,
    );
  }

  /**
   * Build options for pagination, supporting search by name and sorting.
   * @param dto - PaginationDto containing search and sort options
   * @returns Options object for repository pagination
   */
  private buildPaginateOptions(dto?: PaginationDto) {
    const options: any = {};
    if (dto) {
      // Add search by vehicle name (case-insensitive)
      if (dto.search) {
        options.where = {
          name: {
            contains: dto.search,
            mode: 'insensitive',
          },
        };
      }
      // Add sorting if specified
      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      }
    }
    return options;
  }

  /**
   * Fetch all vehicles associated with a given city_id.
   * @param cityId - The ID of the city
   */
  async findActiveVehicleTypesByCityId(cityId: string): Promise<VehicleType[]> {
    return this.vehicleTypeRepository.findActiveVehicleTypesByCityId(cityId);
  }
}

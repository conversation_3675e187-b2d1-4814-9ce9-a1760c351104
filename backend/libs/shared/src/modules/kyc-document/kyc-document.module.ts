import { Module } from '@nestjs/common';
import { KycDocumentService } from './kyc-document.service';
import { KycDocumentRepository } from '../../repositories/kyc-document.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { CountryModule } from '../country/country.module';

@Module({
  imports: [CountryModule],
  providers: [KycDocumentService, KycDocumentRepository, PrismaService],
  exports: [KycDocumentService, KycDocumentRepository],
})
export class KycDocumentModule {}

import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../database/redis/redis.service';
import { ProfileOtpData } from './interfaces/profile-otp.interface';

/**
 * Service for handling Redis operations related to user profile OTP
 */
@Injectable()
export class UserProfileRedisService {
  private readonly logger = new Logger(UserProfileRedisService.name);
  private readonly keyPrefix = 'profile:otp:';

  constructor(private readonly redisService: RedisService) {}

  /**
   * Store profile OTP data in Redis
   * @param data Profile OTP data
   * @param expirationSeconds Expiration time in seconds (default: 900 seconds = 15 minutes)
   */
  async storeProfileOtpData(
    data: ProfileOtpData,
    expirationSeconds: number = 900,
  ): Promise<void> {
    try {
      const key = `${this.keyPrefix}${data.userId}`;
      await this.redisService.set(key, JSON.stringify(data), expirationSeconds);
      this.logger.debug(
        `Stored OTP data for user ${data.userId} with expiration ${expirationSeconds}s`,
      );
    } catch (error: any) {
      this.logger.error(
        `Failed to store OTP data for user ${data.userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get profile OTP data from Redis
   * @param userId User ID
   * @returns Profile OTP data or null if not found
   */
  async getProfileOtpData(userId: string): Promise<ProfileOtpData | null> {
    try {
      const key = `${this.keyPrefix}${userId}`;
      const data = await this.redisService.get(key);

      if (!data) {
        this.logger.debug(`No OTP data found for user ${userId}`);
        return null;
      }

      return JSON.parse(data) as ProfileOtpData;
    } catch (error: any) {
      this.logger.error(
        `Failed to get OTP data for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete profile OTP data from Redis
   * @param userId User ID
   */
  async deleteProfileOtpData(userId: string): Promise<void> {
    try {
      const key = `${this.keyPrefix}${userId}`;
      await this.redisService.del(key);
      this.logger.debug(`Deleted OTP data for user ${userId}`);
    } catch (error: any) {
      this.logger.error(
        `Failed to delete OTP data for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}

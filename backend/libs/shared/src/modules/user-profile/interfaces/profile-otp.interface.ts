/**
 * Interface for profile OTP data stored in Redis
 */
export interface ProfileOtpData {
  /**
   * User ID
   */
  userId: string;

  /**
   * Email address (optional, present when verifying email)
   */
  email?: string;

  /**
   * Phone number (optional, present when verifying phone)
   */
  phone?: string;

  /**
   * OTP secret used for verification
   */
  otpSecret: string;

  /**
   * Timestamp when the OTP was created
   */
  createdAt: Date;
}

import { Module } from '@nestjs/common';
import { CityRepository } from '../../repositories/city.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { CityService } from './city.service';
import { CountryModule } from '../country/country.module';

@Module({
  imports: [CountryModule],
  providers: [CityRepository, CityService, PrismaService],
  exports: [CityRepository, CityService],
})
export class CityModule {}

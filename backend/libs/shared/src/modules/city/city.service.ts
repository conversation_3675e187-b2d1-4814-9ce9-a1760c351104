import { Injectable, NotFoundException } from '@nestjs/common';
import { CityRepository } from '../../repositories/city.repository';
import { CountryService } from '../country/country.service';
import { City } from '@shared/shared/repositories/models/city.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';

@Injectable()
export class CityService {
  constructor(
    private readonly cityRepository: CityRepository,
    private readonly countryService: CountryService,
  ) {}

  async createCity(
    data: Omit<City, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<City> {
    if (data.countryId) {
      const country = await this.countryService.findCountryById(data.countryId);
      if (!country) {
        throw new NotFoundException('Country not found');
      }
    }
    return this.cityRepository.createCity(data);
  }

  async findAllCities(): Promise<City[]> {
    return this.cityRepository.findAllCities();
  }

  async findCityById(id: string): Promise<City> {
    const city = await this.cityRepository.findCityById(id);
    if (!city) throw new NotFoundException(`City with ID ${id} not found`);
    return city;
  }

  async updateCity(id: string, data: Partial<City>): Promise<City> {
    return this.cityRepository.updateCity(id, data);
  }

  async deleteCity(id: string): Promise<City> {
    return this.cityRepository.deleteCity(id);
  }

  async paginateCities(page = 1, limit = 10, dto?: PaginationDto) {
    const options = this.buildPaginateOptions(dto);
    return this.cityRepository.paginateCities(page, limit, options);
  }

  /**
   * Build options for pagination, supporting search by name
   */
  private buildPaginateOptions(dto?: PaginationDto) {
    const options: any = {};
    if (dto) {
      if (dto.search) {
        options.where = {
          name: {
            contains: dto.search,
            mode: 'insensitive',
          },
        };
      }
      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      }
    }
    return options;
  }
}

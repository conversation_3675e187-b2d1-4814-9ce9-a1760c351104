import { Injectable, NotFoundException } from '@nestjs/common';
import { CountryRepository } from '../../repositories/country.repository';
import { Country } from '@shared/shared/repositories/models/country.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';

@Injectable()
export class CountryService {
  constructor(private readonly countryRepository: CountryRepository) {}

  async createCountry(
    data: Omit<Country, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Country> {
    return this.countryRepository.createCountry(data);
  }

  async findAllCountries(): Promise<Country[]> {
    return this.countryRepository.findAllCountries();
  }

  async findCountryById(id: string): Promise<Country> {
    const country = await this.countryRepository.findCountryById(id);
    if (!country)
      throw new NotFoundException(`Country with ID ${id} not found`);
    return country;
  }

  async updateCountry(id: string, data: Partial<Country>): Promise<Country> {
    return this.countryRepository.updateCountry(id, data);
  }

  async deleteCountry(id: string): Promise<Country> {
    return this.countryRepository.deleteCountry(id);
  }

  async paginateCountries(page = 1, limit = 10, dto?: PaginationDto) {
    const options = this.buildPaginateOptions(dto);

    return this.countryRepository.paginateCountries(page, limit, options);
  }

  /**
   * Build options for pagination, supporting search by name
   */
  private buildPaginateOptions(dto?: PaginationDto) {
    const options: any = {};
    if (dto) {
      if (dto.search) {
        options.where = {
          name: {
            contains: dto.search,
            mode: 'insensitive',
          },
        };
      }
      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      }
    }
    return options;
  }
}

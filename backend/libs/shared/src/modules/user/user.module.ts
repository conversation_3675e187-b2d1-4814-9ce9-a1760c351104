import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserRepository } from '../../repositories/user.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { RoleRepository } from '@shared/shared/repositories';
import { AppConfigModule } from '@shared/shared/config';
import { AwsFileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';

@Module({
  imports: [AppConfigModule],
  providers: [
    UserRepository,
    UserService,
    PrismaService,
    RoleRepository,
    AwsFileUploadService,
  ],
  exports: [UserRepository, UserService],
})
export class UserModule {}

import { Injectable, NotFoundException } from '@nestjs/common';
import { UserRepository } from '../../repositories/user.repository';
import { RoleRepository } from '../../repositories/role.repository';
import { AwsFileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly roleRepository: RoleRepository,
    private readonly awsFileUploadService: AwsFileUploadService,
  ) {}

  /**
   * Find a user by id, including userRoles (driver), userProfile, and language.
   */
  async findUserWithProfileByIdAndRoleName(userId: string, roleName: string) {
    // Find the driver role
    const role = await this.roleRepository.findByName(roleName);
    if (!role) throw new NotFoundException('role not found');
    const user: any = await this.userRepository.findOne({
      where: {
        id: userId,
        userRoles: { some: { roleId: role.id } },
      },
      include: {
        userProfiles: {
          take: 1,
          where: { roleId: role.id },
          include: {
            language: true,
          },
        },
      },
    });
    if (!user)
      throw new NotFoundException(`User with ${roleName} role not found`);
    let userProfile = undefined;
    if (user?.userProfiles?.length) {
      userProfile = { ...user.userProfiles[0] };
      if (userProfile.profilePictureUrl) {
        userProfile.profilePictureUrl =
          await this.awsFileUploadService.getSignedUrl(
            userProfile.profilePictureUrl,
          );
      }
    }
    user.userProfiles = undefined;
    return {
      ...user,
      userProfile,
    };
  }
}

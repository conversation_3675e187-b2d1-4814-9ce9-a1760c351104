import { Injectable, NotFoundException } from '@nestjs/common';
import { UserOnboardingRepository } from '../../repositories/user-onboarding.repository';
import { OnboardingStep } from '../../repositories/models/userOnboard.model';
import { RoleRepository } from '@shared/shared/repositories';

@Injectable()
export class UserOnboardingService {
  constructor(
    private readonly userOnboardingRepository: UserOnboardingRepository,
    private readonly roleRepository: RoleRepository, // Assuming RoleRepository is defined and injected correctly
  ) {}

  /**
   * Upserts (creates or updates) onboarding step for a user and role.
   */
  async updateOrCreateOnboardingStepByUserAndRole(
    userId: string,
    roleId: string,
    currentStep: OnboardingStep,
  ) {
    // Try to find existing onboarding
    const existing =
      await this.userOnboardingRepository.findOneByUserIdAndRoleId(
        userId,
        roleId,
      );
    if (existing) {
      return this.userOnboardingRepository.updateUserOnboarding(existing.id, {
        currentStep,
      });
    } else {
      return this.userOnboardingRepository.createUserOnboarding({
        userId,
        roleId,
        currentStep,
      });
    }
  }

  /**
   * Finds the latest onboarding record for a user (by createdAt desc),
   * and includes the user's roles (e.g., driver roles).
   */
  /**
   * Finds the latest onboarding record for a user and role (by createdAt desc).
   */
  async findOnboardByUserIdAndRoleName(userId: string, roleName: string) {
    const role = await this.roleRepository.findByName(roleName);
    if (!role) {
      throw new NotFoundException('Driver role not found');
    }
    const roleId = role.id;
    const onboarding =
      await this.userOnboardingRepository.findOneByUserIdAndRoleId(
        userId,
        roleId,
      );
    return onboarding || null;
  }

  /**
   * Updates the onboarding step for a user and role.
   */
  async updateStepByUserAndRole(
    userId: string,
    roleId: string,
    currentStep: OnboardingStep,
  ) {
    const onboarding =
      await this.userOnboardingRepository.findOneByUserIdAndRoleId(
        userId,
        roleId,
      );
    if (!onboarding) {
      throw new NotFoundException('User onboarding record not found');
    }
    return this.userOnboardingRepository.updateUserOnboarding(onboarding.id, {
      currentStep,
    });
  }
}

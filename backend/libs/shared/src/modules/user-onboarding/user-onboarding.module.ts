import { Module } from '@nestjs/common';
import { UserOnboardingRepository } from '../../repositories/user-onboarding.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { UserOnboardingService } from './user-onboarding.service';
import { RoleRepository } from '@shared/shared/repositories';

@Module({
  providers: [
    UserOnboardingRepository,
    UserOnboardingService,
    PrismaService,
    RoleRepository,
  ],
  exports: [UserOnboardingRepository, UserOnboardingService],
})
export class UserOnboardingModule {}

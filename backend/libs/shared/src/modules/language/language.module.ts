import { Module } from '@nestjs/common';
import { LanguageRepository } from '../../repositories/language.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { LanguageService } from './language.service';

@Module({
  providers: [LanguageRepository, LanguageService, PrismaService],
  exports: [LanguageRepository, LanguageService],
})
export class LanguageModule {}

import { Module } from '@nestjs/common';
import { DriverVehicleService } from './driver-vehicle.service';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { UserProfileModule } from '../user-profile/user-profile.module';
import { UserOnboardingModule } from '../user-onboarding/user-onboarding.module';

@Module({
  imports: [UserProfileModule, UserOnboardingModule],
  providers: [DriverVehicleService, DriverVehicleRepository, PrismaService],
  exports: [DriverVehicleService, DriverVehicleRepository],
})
export class DriverVehicleModule {}

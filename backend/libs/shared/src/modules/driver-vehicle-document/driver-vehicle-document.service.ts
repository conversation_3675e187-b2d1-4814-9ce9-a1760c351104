import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { DriverVehicleDocumentRepository } from '../../repositories/driver-vehicle-document.repository';
import { CashfreeVehicleVerificationService } from '@shared/shared/common/verifications/cashfree/cashfree-vehicle-verification.service';
import { DriverVehicleDocument } from '@shared/shared/repositories/models/driverVehicleDocument.model';
import { VehicleDocumentRepository } from '@shared/shared/repositories/vehicle-document.repository';
import { UserOnboardingService } from '../user-onboarding/user-onboarding.service';
import { OnboardingStep } from '@shared/shared/repositories/models/userOnboard.model';
import { UserProfileService } from '../user-profile/user-profile.service';
import { AwsFileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';

@Injectable()
export class DriverVehicleDocumentService {
  private readonly logger = new Logger(DriverVehicleDocumentService.name);

  constructor(
    private readonly driverVehicleRepository: DriverVehicleRepository,
    private readonly driverVehicleDocumentRepository: DriverVehicleDocumentRepository,
    private readonly cashfreeVehicleVerificationService: CashfreeVehicleVerificationService,
    private readonly vehicleDocumentRepository: VehicleDocumentRepository,
    private readonly userOnboardingService: UserOnboardingService,
    private readonly userProfileService: UserProfileService,
    private readonly awsFileUploadService: AwsFileUploadService,
  ) {}

  /**
   * Finds a driver vehicle document by driverVehicleId
   */
  async findByDriverVehicleId(
    driverVehicleId: string,
  ): Promise<DriverVehicleDocument[]> {
    return this.driverVehicleDocumentRepository.findMany({
      where: { driverVehicleId },
    });
  }
  /**
   * Verifies a driver vehicle with Cashfree and creates/updates the document if valid
   * @param driverVehicleId
   * @returns the created/updated DriverVehicleDocument
   */
  async verifyAndSaveDocument(driverVehicleId: string): Promise<any> {
    // Find the driver vehicle
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(driverVehicleId);
    if (!driverVehicle) throw new NotFoundException('Driver vehicle not found');
    if (!driverVehicle.vehicleNumber)
      throw new ConflictException(
        'Vehicle number is required for verification',
      );

    //need to rewrite with country specific vehicle document
    const vehicleDocument =
      await this.vehicleDocumentRepository.findOneByIdentifier(
        'vehicle_registration',
      );
    if (!vehicleDocument) {
      throw new NotFoundException('Vehicle document not found');
    }
    const userProfile = await this.userProfileService.findUserProfileById(
      driverVehicle.userProfileId,
    );
    // if (!userProfile) {
    //   throw new NotFoundException(
    //     `User profile for user ID ${driverVehicle.userProfileId} not found`,
    //   );
    // }
    // Call Cashfree verification
    const verificationResult =
      await this.cashfreeVehicleVerificationService.verifyVehicle(
        driverVehicleId + new Date().getTime(), // Unique reference ID
        driverVehicle.vehicleNumber,
      );

    if (verificationResult.status !== 'VALID') {
      throw new ConflictException(
        'Vehicle verification failed or is not valid',
      );
    }
    let isNocRequired = false;
    // Check if NOC is required by comparing owner name with user profile name
    const ownerName = verificationResult.owner?.toLowerCase().trim();
    const userFullName = `${userProfile.firstName} ${userProfile.lastName}`
      .toLowerCase()
      .trim();

    if (ownerName && ownerName !== userFullName) {
      isNocRequired = true;

      // Update the driver vehicle to mark NOC as required
      await this.driverVehicleRepository.updateDriverVehicle(driverVehicle.id, {
        isNocRequired: true,
      });
    }

    // Upsert driver vehicle document
    const docData = {
      driverVehicleId: driverVehicle.id,
      details: verificationResult,
      vehicleDocumentId: vehicleDocument.id, //
    };

    // Try to find existing document
    const existing = await this.driverVehicleDocumentRepository.findOne({
      where: { driverVehicleId: driverVehicle.id },
    });
    let result;
    if (existing) {
      result = await this.driverVehicleDocumentRepository.update({
        where: { id: existing.id },
        data: docData,
      });
    } else {
      result = await this.driverVehicleDocumentRepository.create(docData);

      await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
        userProfile.userId,
        userProfile.roleId,
        OnboardingStep.VEHICLE_DOCUMENTS_VERIFICATION,
      );
    }

    // Extract and save insurance information if available
    await this.createInsuranceDocument(driverVehicle.id, verificationResult);

    return {
      isNocRequired,
      ...result,
    };
  }

  /**
   * Upload NOC document for a driver vehicle
   * @param driverVehicleId
   * @param documentUrl
   * @returns the created/updated DriverVehicleDocument
   */
  async uploadNocDocument(
    driverVehicleId: string,
    documentUrl: string,
  ): Promise<DriverVehicleDocument> {
    // Find the driver vehicle
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(driverVehicleId);
    if (!driverVehicle) {
      throw new NotFoundException('Driver vehicle not found');
    }

    // Find NOC vehicle document type
    const nocDocument =
      await this.vehicleDocumentRepository.findOneByIdentifier('noc');
    if (!nocDocument) {
      throw new NotFoundException('NOC document type not found');
    }

    // Check if NOC document already exists for this driver vehicle
    const existingNocDocument =
      await this.driverVehicleDocumentRepository.findOne({
        where: {
          driverVehicleId,
          vehicleDocumentId: nocDocument.id,
        },
      });

    const docData = {
      driverVehicleId,
      vehicleDocumentId: nocDocument.id,
      documentUrl,
      details: {
        uploadedAt: new Date(),
        documentType: 'noc',
      },
    };

    let result: DriverVehicleDocument;
    if (existingNocDocument) {
      // Update existing NOC document
      result = await this.driverVehicleDocumentRepository.update({
        where: { id: existingNocDocument.id },
        data: docData,
      });
    } else {
      // Create new NOC document
      result = await this.driverVehicleDocumentRepository.create(docData);
    }

    return result;
  }

  /**
   * Create or update insurance document from verification result
   * @param driverVehicleId
   * @param verificationResult
   */
  private async createInsuranceDocument(
    driverVehicleId: string,
    verificationResult: any,
  ): Promise<void> {
    try {
      // Find insurance vehicle document type
      const insuranceDocument =
        await this.vehicleDocumentRepository.findOneByIdentifier('insurance');
      if (!insuranceDocument) {
        this.logger.warn(
          'Insurance document type not found, skipping insurance document creation',
        );
        return;
      }

      // Extract insurance details from verification result
      const insuranceDetails = {
        vehicle_insurance_upto:
          verificationResult.vehicle_insurance_upto || null,
        vehicle_insurance_company_name:
          verificationResult.vehicle_insurance_company_name || null,
        vehicle_insurance_policy_number:
          verificationResult.vehicle_insurance_policy_number || null,
        extractedAt: new Date(),
        documentType: 'insurance',
      };

      // Only create insurance document if at least one insurance field is present
      if (
        insuranceDetails.vehicle_insurance_upto ||
        insuranceDetails.vehicle_insurance_company_name ||
        insuranceDetails.vehicle_insurance_policy_number
      ) {
        // Check if insurance document already exists for this driver vehicle
        const existingInsuranceDocument =
          await this.driverVehicleDocumentRepository.findOne({
            where: {
              driverVehicleId,
              vehicleDocumentId: insuranceDocument.id,
            },
          });

        const insuranceDocData = {
          driverVehicleId,
          vehicleDocumentId: insuranceDocument.id,
          details: insuranceDetails,
          documentFields: {
            insurance_upto: insuranceDetails.vehicle_insurance_upto,
            insurance_company_name:
              insuranceDetails.vehicle_insurance_company_name,
            insurance_policy_number:
              insuranceDetails.vehicle_insurance_policy_number,
          },
        };

        if (existingInsuranceDocument) {
          // Update existing insurance document
          await this.driverVehicleDocumentRepository.update({
            where: { id: existingInsuranceDocument.id },
            data: insuranceDocData,
          });
        } else {
          // Create new insurance document
          await this.driverVehicleDocumentRepository.create(insuranceDocData);
        }
      }
    } catch (error) {
      // Log error but don't fail the main verification process
      this.logger.error('Error creating insurance document:', error);
    }
  }

  /**
   * Get all vehicle documents with driver vehicle documents for a specific driver vehicle ID
   * Returns formatted data with signed URLs for document access
   */
  async getVehicleDocumentsWithDriverDocs(driverVehicleId: string) {
    //Fetch and validate driver vehicle
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(driverVehicleId);
    if (!driverVehicle) {
      throw new NotFoundException(
        `Driver vehicle with ID ${driverVehicleId} not found`,
      );
    }

    // Fetch all vehicle documents along with related driver vehicle documents
    let vehicleDocuments = await this.vehicleDocumentRepository.findMany({
      include: {
        driverVehicleDocuments: {
          where: { driverVehicleId },
        },
      },
    });

    // Remove 'noc' document if not required
    if (!driverVehicle.isNocRequired) {
      vehicleDocuments = vehicleDocuments.filter(
        (doc) => doc.identifier !== 'noc',
      );
    }

    //Format response with signed URLs
    return Promise.all(
      vehicleDocuments.map(async (vehicleDoc) => {
        const relatedDocs = vehicleDoc.driverVehicleDocuments || [];
        let driverDocument = null;

        if (relatedDocs.length > 0) {
          const [firstRelatedDoc] = relatedDocs;
          let signedUrl = firstRelatedDoc.documentUrl;

          if (firstRelatedDoc.documentUrl) {
            try {
              signedUrl = await this.awsFileUploadService.getSignedUrl(
                firstRelatedDoc.documentUrl,
                3600, // 1 hour expiry
              );
            } catch {
              signedUrl = firstRelatedDoc.documentUrl;
            }
          }

          driverDocument = {
            ...firstRelatedDoc,
            documentUrl: signedUrl,
          };
        }

        // Exclude driverVehicleDocuments from the response
        const { driverVehicleDocuments, ...vehicleDocWithoutRelation } =
          vehicleDoc;

        return {
          ...vehicleDocWithoutRelation,
          driverDocument,
        };
      }),
    );
  }
}

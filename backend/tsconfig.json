{"compilerOptions": {"module": "commonjs", "target": "ES2023", "declaration": true, "sourceMap": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": false, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictBindCallApply": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noPropertyAccessFromIndexSignature": true, "forceConsistentCasingInFileNames": true, "exactOptionalPropertyTypes": true, "paths": {"@shared/shared": ["libs/shared/src"], "@shared/shared/*": ["libs/shared/src/*"]}}, "exclude": ["node_modules", "dist"]}
model UserRole {
  id        String    @id @default(uuid()) @map("id") @db.Uuid
  userId    String    @map("user_id") @db.Uuid
  roleId    String    @map("role_id") @db.Uuid
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId], name: "unique_user_role")
  @@index([userId], name: "idx_user_role_user_id")
  @@index([roleId], name: "idx_user_role_role_id")
  @@map("user_roles")
}

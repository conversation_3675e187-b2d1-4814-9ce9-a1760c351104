model Role {
  id          String    @id @default(uuid()) @map("id") @db.Uuid
  name        String    @unique @map("name")
  description String?   @map("description")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  rolePermissions RolePermission[]
  userRoles       UserRole[]
  userProfiles    UserProfile[]
  userOnboardings UserOnboarding[]

  @@index([name], name: "idx_role_name")
  @@map("roles")
}

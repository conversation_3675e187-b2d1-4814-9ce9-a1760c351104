model RefreshToken {
  id        String    @id @default(uuid()) @map("id") @db.Uuid
  token     String    @unique @map("token")
  userId    String    @map("user_id") @db.Uuid
  expiresAt DateTime  @map("expires_at") @db.Timestamptz
  revoked   <PERSON><PERSON><PERSON>   @default(false) @map("revoked")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([token], name: "idx_refresh_token_token")
  @@index([userId], name: "idx_refresh_token_user_id")
  @@index([expiresAt], name: "idx_refresh_token_expires_at")
  @@map("refresh_tokens")
}

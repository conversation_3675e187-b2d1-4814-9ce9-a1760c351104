model VehicleDocument {
  id         String    @id @default(uuid()) @db.Uuid
  name       String    @unique @db.VarChar
  identifier String
  countryId  String    @map("country_id") @db.Uuid
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  country                Country                 @relation(fields: [countryId], references: [id])
  driverVehicleDocuments DriverVehicleDocument[]

  @@map("vehicle_documents")
}

model DriverKyc {
  id             String    @id @default(uuid()) @map("id") @db.Uuid
  userProfileId  String    @map("user_profile_id") @db.Uuid
  kycDocumentId  String    @map("kyc_document_id") @db.Uuid
  documentNumber String?   @map("document_number") @db.VarChar
  documentUrl    String?   @map("document_url") @db.Text
  documentFields Json?     @map("document_fields") @db.JsonB
  expiryDate     DateTime? @map("expiry_date") @db.Date
  fromD<PERSON>locker Boolean   @default(false) @map("from_digilocker")
  status         KycStatus @map("status")
  rejectionNote  String?   @map("rejection_note") @db.Text
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamp
  updatedAt      DateTime  @updatedAt @map("updated_at") @db.Timestamp
  deletedAt      DateTime? @map("deleted_at") @db.Timestamp

  // Relations
  userProfile UserProfile @relation(fields: [userProfileId], references: [id])
  kycDocument KycDocument @relation(fields: [kycDocumentId], references: [id])
}

enum KycStatus {
  PENDING
  APPROVED
  REJECTED
}

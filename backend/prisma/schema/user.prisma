model User {
  id              String    @id @default(uuid()) @map("id") @db.Uuid
  phoneNumber     String?   @map("phone_number")
  email           String?   @map("email")
  emailVerifiedAt DateTime? @map("email_verified_at") @db.Timestamptz
  phoneVerifiedAt DateTime? @map("phone_verified_at") @db.Timestamptz
  isPolicyAllowed Boolean?  @default(false) @map("is_policy_allowed")
  otpSecret       String?   @map("otp_secret")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  deletedAt       DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  userRoles       UserRole[]
  AuthCredential  AuthCredential[]
  RefreshToken    RefreshToken[]
  userProfiles    UserProfile[]
  userOnboardings UserOnboarding[]

  @@index([phoneNumber], name: "idx_user_phone_number")
  @@index([email], name: "idx_user_email")
  @@map("users")
}

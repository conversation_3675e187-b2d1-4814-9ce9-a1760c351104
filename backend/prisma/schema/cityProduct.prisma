model CityProduct {
  id            String    @id @default(uuid()) @db.Uuid
  cityId        String    @map("city_id") @db.Uuid
  productId     String    @map("product_id") @db.Uuid
  vehicleTypeId String    @map("vehicle_type_id") @db.Uuid
  isEnabled     <PERSON>olean   @default(true) @map("is_enabled")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  city        City        @relation(fields: [cityId], references: [id])
  product     Product     @relation(fields: [productId], references: [id])
  vehicleType VehicleType @relation(fields: [vehicleTypeId], references: [id])

  driverVehicles DriverVehicle[]

  @@map("city_products")
}

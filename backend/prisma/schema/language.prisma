model Language {
  id           String    @id @default(uuid()) @map("id") @db.Uuid
  code         String?   @unique @map("code")
  name         String    @map("name")
  nameInNative String    @map("name_in_native")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at") @db.Timestamptz

  //relations
  userProfiles UserProfile[]

  @@index([code], name: "idx_language_code")
  @@map("languages")
}

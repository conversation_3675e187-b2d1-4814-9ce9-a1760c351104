enum DriverVehicleStatus {
  created
  pending
  verified
  rejected
}

model DriverVehicle {
  id            String  @id @default(uuid()) @map("id") @db.Uuid
  userProfileId String  @map("user_profile_id") @db.Uuid
  cityProductId String? @map("city_product_id") @db.Uuid
  vehicleTypeId String  @map("vehicle_type_id") @db.Uuid

  vehicleNumber String?             @map("vehicle_number")
  isNocRequired Boolean             @default(false) @map("is_noc_required")
  isPrimary     <PERSON><PERSON><PERSON>             @default(false) @map("is_primary")
  status        DriverVehicleStatus @default(pending) @map("status")
  createdAt     DateTime            @default(now()) @map("created_at")
  updatedAt     DateTime            @updatedAt @map("updated_at")
  deletedAt     DateTime?           @map("deleted_at") @db.Timestamptz

  // Relations
  userProfile UserProfile  @relation(fields: [userProfileId], references: [id], onDelete: Cascade)
  vehicleType VehicleType  @relation(fields: [vehicleTypeId], references: [id], onDelete: Cascade)
  cityProduct CityProduct? @relation(fields: [cityProductId], references: [id], onDelete: Cascade)

  driverVehicleDocuments DriverVehicleDocument[] @relation("DriverVehicleToDriverVehicleDocument")

  @@map("driver_vehicles")
}

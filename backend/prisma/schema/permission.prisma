model Permission {
  id          String    @id @default(uuid()) @map("id") @db.Uuid
  name        String    @unique @map("name")
  description String?   @map("description")
  resource    String    @map("resource")
  action      String    @map("action")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  rolePermissions RolePermission[]

  @@index([name], name: "idx_permission_name")
  @@index([resource], name: "idx_permission_resource")
  @@index([action], name: "idx_permission_action")
  @@map("permissions")
}

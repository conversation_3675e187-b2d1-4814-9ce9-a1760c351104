model RolePermission {
  id           String    @id @default(uuid()) @map("id") @db.Uuid
  roleId       String    @map("role_id") @db.Uuid
  permissionId String    @map("permission_id") @db.Uuid
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId], name: "unique_role_permission")
  @@index([roleId], name: "idx_role_permission_role_id")
  @@index([permissionId], name: "idx_role_permission_permission_id")
  @@map("role_permissions")
}

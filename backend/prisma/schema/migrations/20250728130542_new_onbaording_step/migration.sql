/*
  Warnings:

  - The values [DOCUMENT_UPLOAD] on the enum `onboarding_step` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "onboarding_step_new" AS ENUM ('PHONE_VERIFICATION', 'LANGUAGE_UPDATE', 'PROFILE_SETUP', 'VEHICLE_DOCUMENTS_VERIFICATION', 'PROFILE_PHOTO_UPLOAD', 'KYC_DOCUMENT_UPLOAD', 'VEHICLE_REGISTRATION', 'BACKGROUND_CHECK', 'TRAINING_COMPLETION', 'ACCOUNT_ACTIVATION', 'COMPLETED');
ALTER TABLE "user_onboarding" ALTER COLUMN "current_step" TYPE "onboarding_step_new" USING ("current_step"::text::"onboarding_step_new");
ALTER TYPE "onboarding_step" RENAME TO "onboarding_step_old";
ALTER TYPE "onboarding_step_new" RENAME TO "onboarding_step";
DROP TYPE "onboarding_step_old";
COMMIT;

-- AlterTable
ALTER TABLE "user_onboarding" ADD COLUMN     "deleted_at" TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "user_profiles" ADD COLUMN     "deleted_at" TIMESTAMPTZ,
ADD COLUMN     "language_id" UUID,
ALTER COLUMN "first_name" DROP NOT NULL,
ALTER COLUMN "last_name" DROP NOT NULL,
ALTER COLUMN "referral_code" DROP NOT NULL;

-- Add<PERSON><PERSON>ignKey
ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_language_id_fkey" FOREIGN KEY ("language_id") REFERENCES "languages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

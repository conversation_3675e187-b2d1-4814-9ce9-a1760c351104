-- CreateEnum
CREATE TYPE "KycStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateTable
CREATE TABLE "DriverKyc" (
    "id" UUID NOT NULL,
    "user_profile_id" UUID NOT NULL,
    "kyc_document_id" UUID NOT NULL,
    "document_number" VARCHAR,
    "document_url" TEXT,
    "document_fields" JSONB,
    "expiry_date" DATE,
    "from_digilocker" BOOLEAN NOT NULL DEFAULT false,
    "status" "KycStatus" NOT NULL,
    "rejection_note" TEXT,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP NOT NULL,
    "deleted_at" TIMESTAMP,

    CONSTRAINT "DriverKyc_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "DriverKyc" ADD CONSTRAINT "DriverKyc_user_profile_id_fkey" FOREIGN KEY ("user_profile_id") REFERENCES "user_profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DriverKyc" ADD CONSTRAINT "DriverKyc_kyc_document_id_fkey" FOREIGN KEY ("kyc_document_id") REFERENCES "KycDocument"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- CreateEnum
CREATE TYPE "DriverVehicleStatus" AS ENUM ('pending', 'verified', 'rejected');

-- CreateEnum
CREATE TYPE "VehicleType" AS ENUM ('other', 'premium', 'normal');

-- CreateTable
CREATE TABLE "driver_vehicles" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "city_id" UUID NOT NULL,
    "vehicle_id" UUID NOT NULL,
    "vehicle_number" TEXT,
    "is_noc_required" BOOLEAN NOT NULL DEFAULT false,
    "noc_file_url" TEXT,
    "is_primary" BOOLEAN NOT NULL DEFAULT false,
    "type" "VehicleType",
    "status" "DriverVehicleStatus" NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "driver_vehicles_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_driver_vehicle_user_id" ON "driver_vehicles"("user_id");

-- CreateIndex
CREATE INDEX "idx_driver_vehicle_city_id" ON "driver_vehicles"("city_id");

-- CreateIndex
CREATE INDEX "idx_driver_vehicle_vehicle_id" ON "driver_vehicles"("vehicle_id");

-- AddForeignKey
ALTER TABLE "driver_vehicles" ADD CONSTRAINT "driver_vehicles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_vehicles" ADD CONSTRAINT "driver_vehicles_city_id_fkey" FOREIGN KEY ("city_id") REFERENCES "cities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_vehicles" ADD CONSTRAINT "driver_vehicles_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "vehicles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- CreateTable
CREATE TABLE "city_vehicles" (
    "id" UUID NOT NULL,
    "city_id" UUID NOT NULL,
    "vehicle_id" UUID NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "city_vehicles_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_city_vehicle_city_id" ON "city_vehicles"("city_id");

-- CreateIndex
CREATE INDEX "idx_city_vehicle_vehicle_id" ON "city_vehicles"("vehicle_id");

-- AddForeignKey
ALTER TABLE "city_vehicles" ADD CONSTRAINT "city_vehicles_city_id_fkey" FOREIGN KEY ("city_id") REFERENCES "cities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "city_vehicles" ADD CONSTRAINT "city_vehicles_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "vehicles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

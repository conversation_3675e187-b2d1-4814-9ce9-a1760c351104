/*
  Warnings:

  - You are about to drop the column `user_id` on the `driver_vehicle_documents` table. All the data in the column will be lost.
  - You are about to drop the `city_vehicles` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `vehicle_type_id` to the `city_products` table without a default value. This is not possible if the table is not empty.
  - Added the required column `vehicle_document_id` to the `driver_vehicle_documents` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "city_vehicles" DROP CONSTRAINT "city_vehicles_city_id_fkey";

-- DropForeignKey
ALTER TABLE "city_vehicles" DROP CONSTRAINT "city_vehicles_vehicle_type_id_fkey";

-- DropForeignKey
ALTER TABLE "driver_vehicle_documents" DROP CONSTRAINT "driver_vehicle_documents_user_id_fkey";

-- DropIndex
DROP INDEX "idx_driver_vehicle_document_user_id";

-- AlterTable
ALTER TABLE "city_products" ADD COLUMN     "deleted_at" TIMESTAMPTZ,
ADD COLUMN     "vehicle_type_id" UUID NOT NULL;

-- AlterTable
ALTER TABLE "driver_vehicle_documents" DROP COLUMN "user_id",
ADD COLUMN     "vehicle_document_id" UUID NOT NULL;

-- DropTable
DROP TABLE "city_vehicles";

-- CreateTable
CREATE TABLE "vehicle_documents" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "identifier" TEXT NOT NULL,
    "country_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_documents_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_documents_name_key" ON "vehicle_documents"("name");

-- AddForeignKey
ALTER TABLE "city_products" ADD CONSTRAINT "city_products_vehicle_type_id_fkey" FOREIGN KEY ("vehicle_type_id") REFERENCES "vehicle_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_vehicle_documents" ADD CONSTRAINT "driver_vehicle_documents_vehicle_document_id_fkey" FOREIGN KEY ("vehicle_document_id") REFERENCES "vehicle_documents"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_documents" ADD CONSTRAINT "vehicle_documents_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "countries"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

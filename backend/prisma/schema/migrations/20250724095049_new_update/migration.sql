/*
  Warnings:

  - You are about to drop the column `vehicle_id` on the `city_vehicles` table. All the data in the column will be lost.
  - You are about to drop the column `city_id` on the `driver_vehicles` table. All the data in the column will be lost.
  - You are about to drop the column `noc_file_url` on the `driver_vehicles` table. All the data in the column will be lost.
  - You are about to drop the column `type` on the `driver_vehicles` table. All the data in the column will be lost.
  - You are about to drop the column `user_id` on the `driver_vehicles` table. All the data in the column will be lost.
  - You are about to drop the column `vehicle_id` on the `driver_vehicles` table. All the data in the column will be lost.
  - Added the required column `vehicle_type_id` to the `city_vehicles` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_profile_id` to the `driver_vehicles` table without a default value. This is not possible if the table is not empty.
  - Added the required column `vehicle_type_id` to the `driver_vehicles` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
ALTER TYPE "DriverVehicleStatus" ADD VALUE 'created';

-- DropForeignKey
ALTER TABLE "city_vehicles" DROP CONSTRAINT "city_vehicles_vehicle_id_fkey";

-- DropForeignKey
ALTER TABLE "driver_vehicles" DROP CONSTRAINT "driver_vehicles_city_id_fkey";

-- DropForeignKey
ALTER TABLE "driver_vehicles" DROP CONSTRAINT "driver_vehicles_user_id_fkey";

-- DropForeignKey
ALTER TABLE "driver_vehicles" DROP CONSTRAINT "driver_vehicles_vehicle_id_fkey";

-- DropIndex
DROP INDEX "idx_city_vehicle_vehicle_id";

-- DropIndex
DROP INDEX "idx_driver_vehicle_city_id";

-- DropIndex
DROP INDEX "idx_driver_vehicle_user_id";

-- DropIndex
DROP INDEX "idx_driver_vehicle_vehicle_id";

-- AlterTable
ALTER TABLE "city_vehicles" DROP COLUMN "vehicle_id",
ADD COLUMN     "vehicle_type_id" UUID NOT NULL;

-- AlterTable
ALTER TABLE "driver_vehicles" DROP COLUMN "city_id",
DROP COLUMN "noc_file_url",
DROP COLUMN "type",
DROP COLUMN "user_id",
DROP COLUMN "vehicle_id",
ADD COLUMN     "city_product_id" UUID,
ADD COLUMN     "user_profile_id" UUID NOT NULL,
ADD COLUMN     "vehicle_type_id" UUID NOT NULL;

-- DropEnum
DROP TYPE "VehicleType";

-- CreateTable
CREATE TABLE "city_products" (
    "id" UUID NOT NULL,
    "city_id" UUID NOT NULL,
    "product_id" UUID NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "city_products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "driver_vehicle_documents" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "driver_vehicle_id" UUID NOT NULL,
    "vehicle_document_fields" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "driver_vehicle_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "products" (
    "id" UUID NOT NULL,
    "vehicle_type_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vehicle_types" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "image" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "vehicle_types_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_driver_vehicle_document_user_id" ON "driver_vehicle_documents"("user_id");

-- CreateIndex
CREATE INDEX "idx_driver_vehicle_document_driver_vehicle_id" ON "driver_vehicle_documents"("driver_vehicle_id");

-- CreateIndex
CREATE INDEX "idx_vehicle_type_name" ON "vehicle_types"("name");

-- CreateIndex
CREATE INDEX "idx_city_vehicle_vehicle_id" ON "city_vehicles"("vehicle_type_id");

-- AddForeignKey
ALTER TABLE "city_products" ADD CONSTRAINT "city_products_city_id_fkey" FOREIGN KEY ("city_id") REFERENCES "cities"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "city_products" ADD CONSTRAINT "city_products_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "city_vehicles" ADD CONSTRAINT "city_vehicles_vehicle_type_id_fkey" FOREIGN KEY ("vehicle_type_id") REFERENCES "vehicle_types"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_vehicles" ADD CONSTRAINT "driver_vehicles_user_profile_id_fkey" FOREIGN KEY ("user_profile_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_vehicles" ADD CONSTRAINT "driver_vehicles_vehicle_type_id_fkey" FOREIGN KEY ("vehicle_type_id") REFERENCES "vehicle_types"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_vehicles" ADD CONSTRAINT "driver_vehicles_city_product_id_fkey" FOREIGN KEY ("city_product_id") REFERENCES "city_products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_vehicle_documents" ADD CONSTRAINT "driver_vehicle_documents_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_vehicle_documents" ADD CONSTRAINT "driver_vehicle_documents_driver_vehicle_id_fkey" FOREIGN KEY ("driver_vehicle_id") REFERENCES "driver_vehicles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "products" ADD CONSTRAINT "products_vehicle_type_id_fkey" FOREIGN KEY ("vehicle_type_id") REFERENCES "vehicle_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

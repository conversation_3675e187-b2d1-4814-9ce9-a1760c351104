enum Gender {
  MALE
  FEMALE
  OTHER
}

model UserProfile {
  id                String        @id @default(uuid()) @map("id") @db.Uuid
  userId            String        @map("user_id") @db.Uuid
  roleId            String        @map("role_id") @db.Uuid
  firstName         String?       @map("first_name")
  lastName          String?       @map("last_name")
  cityId            String?       @map("city") @db.Uuid
  referralCode      String?       @map("referral_code")
  profilePictureUrl String?       @map("profile_picture_url")
  languageId        String?       @map("language_id") @db.Uuid
  gender            Gender?       @map("gender")
  dob               DateTime?     @map("dob") @db.Date
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @updatedAt @map("updated_at")
  deletedAt         DateTime?     @map("deleted_at") @db.Timestamptz

  // Relations
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           Role            @relation(fields: [roleId], references: [id])
  city           City?           @relation(fields: [cityId], references: [id], onDelete: SetNull)
  language       Language?       @relation(fields: [languageId], references: [id], onDelete: SetNull)
  driverVehicles DriverVehicle[]
  driverKycs     DriverKyc[]

  @@map("user_profiles")
}

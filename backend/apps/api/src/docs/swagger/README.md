# Swagger API Documentation Setup

This document explains the Swagger/OpenAPI setup for the Tukxi API.

## Overview

Swagger is configured using NestJS's `@nestjs/swagger` package and provides interactive API documentation accessible at `/api/docs`.

## Access Points

- **Swagger UI**: `http://localhost:{port}/api/docs`
- **OpenAPI JSON**: `http://localhost:{port}/api/docs-json`

## Configuration

### Main Setup
The main Swagger configuration is in `/docs/swagger/swagger.ts`. It includes:

- API metadata (title, description, version)
- Authentication schemes (JWT Bearer, API Key)
- Server configurations
- Tags for organizing endpoints
- Custom styling

### Available Decorators

#### Response Decorators
```typescript
import { ApiSuccessResponse, ApiCreatedResponse, ApiPaginatedResponse } from '../docs/swagger';

// Success responses
@ApiSuccessResponse(UserDto, 'User retrieved successfully')

// Created responses  
@ApiCreatedResponse(UserDto, 'User created successfully')

// Paginated responses
@ApiPaginatedResponse(UserDto, 'Users retrieved successfully')
```

#### Operation Decorators
```typescript
import { ApiOperationWithAuth, ApiOperationPublic } from '../docs/swagger';

// Protected endpoints
@ApiOperationWithAuth('Create user', 'Creates a new user account')

// Public endpoints
@ApiOperationPublic('Get health status', 'Returns application health')
```

#### Common Response Combinations
```typescript
import { ApiCommonResponses, ApiAuthResponses } from '../docs/swagger';

// Basic error responses (400, 401, 500)
@ApiCommonResponses()

// Auth-protected responses (400, 401, 403, 500)  
@ApiAuthResponses()
```

#### Pagination & Parameters
```typescript
import { ApiPaginationQuery, ApiIdParam } from '../docs/swagger';

// Add pagination query parameters
@ApiPaginationQuery()

// Add ID path parameter
@ApiIdParam('userId', 'User identifier')
```

## Usage Examples

### Basic Controller Example
```typescript
import { Controller, Get, Post, Body, Param } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { 
  ApiOperationWithAuth, 
  ApiOperationPublic,
  ApiSuccessResponse, 
  ApiCreatedResponse,
  ApiPaginationQuery,
  ApiIdParam 
} from '../docs/swagger';

@ApiTags('Users')
@Controller('users')
export class UsersController {
  
  @Get()
  @ApiOperationPublic('Get all users')
  @ApiPaginationQuery()
  @ApiSuccessResponse(UserDto, 'Users retrieved successfully')
  async findAll() {
    // Implementation
  }

  @Get(':id')
  @ApiOperationWithAuth('Get user by ID')
  @ApiIdParam('id', 'User ID')
  @ApiSuccessResponse(UserDto, 'User retrieved successfully')
  async findOne(@Param('id') id: string) {
    // Implementation
  }

  @Post()
  @ApiOperationWithAuth('Create new user')
  @ApiCreatedResponse(UserDto, 'User created successfully')
  async create(@Body() createUserDto: CreateUserDto) {
    // Implementation
  }
}
```

### DTO Example
```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional } from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ 
    example: '+1234567890',
    description: 'User phone number'
  })
  @IsString()
  phoneNumber!: string;

  @ApiProperty({ 
    example: '<EMAIL>',
    description: 'User email address',
    required: false
  })
  @IsEmail()
  @IsOptional()
  email?: string;
}
```

## Authentication

The API supports two authentication methods documented in Swagger:

### JWT Bearer Authentication
```typescript
@ApiBearerAuth('JWT-auth')
```

### API Key Authentication  
```typescript
@ApiSecurity('API-KEY')
```

## File Structure

```
src/
├── docs/
│   └── swagger/
│       ├── index.ts                    # Main exports
│       ├── swagger.ts                  # Swagger setup configuration
│       ├── common-responses.dto.ts     # Common response DTOs
│       ├── swagger-decorators.ts       # Reusable decorators
│       └── example-user.dto.ts         # Example DTO implementation
```

## Best Practices

### 1. Always Document Endpoints
- Use `@ApiOperation()` for all endpoints
- Add appropriate response decorators
- Include examples in DTOs

### 2. Use Consistent Response Format
```typescript
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  timestamp: number;
}
```

### 3. Tag Organization
- Group related endpoints with `@ApiTags()`
- Use consistent tag names
- Follow resource-based naming (Users, Auth, etc.)

### 4. Error Documentation
- Document all possible error responses
- Use standard HTTP status codes
- Provide clear error messages

### 5. DTO Validation
- Combine Swagger decorators with validation decorators
- Use clear examples and descriptions
- Mark optional fields appropriately

## Customization

### Adding New Response Types
1. Create DTO in `common-responses.dto.ts`
2. Add decorator in `swagger-decorators.ts`
3. Export from `index.ts`

### Custom Tags
Add new tags in the swagger configuration:
```typescript
.addTag('NewResource', 'Description of new resource endpoints')
```

### Authentication Schemes
Add new auth schemes in swagger setup:
```typescript
.addApiKey({
  type: 'apiKey',
  name: 'X-CUSTOM-AUTH',
  in: 'header',
}, 'CUSTOM-AUTH')
```

## Development

When adding new API endpoints:

1. **Create DTOs** with proper Swagger decorators
2. **Add controller decorators** for operations and responses  
3. **Test documentation** in Swagger UI
4. **Verify examples** work correctly

## Production Considerations

- Swagger UI may be disabled in production
- OpenAPI JSON remains available for API clients
- Authentication examples should use placeholder tokens
- Sensitive information should not appear in examples

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional } from 'class-validator';

export class UserDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Unique user identifier',
    format: 'uuid',
  })
  id!: string;

  @ApiProperty({
    example: '+1234567890',
    description: 'User phone number',
  })
  phoneNumber!: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
    required: false,
  })
  email?: string;

  @ApiProperty({
    example: '2025-07-21T10:30:00.000Z',
    description: 'User creation timestamp',
  })
  createdAt!: string;

  @ApiProperty({
    example: '2025-07-21T10:30:00.000Z',
    description: 'User last update timestamp',
  })
  updatedAt!: string;
}

export class CreateUserDto {
  @ApiProperty({
    example: '+1234567890',
    description: 'User phone number',
  })
  @IsString()
  phoneNumber!: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  email?: string;
}

export class UpdateUserDto {
  @ApiProperty({
    example: '+1234567890',
    description: 'User phone number',
    required: false,
  })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  email?: string;
}

import { ApiProperty } from '@nestjs/swagger';

export class ApiResponseDto<T> {
  @ApiProperty({ example: 200 })
  statusCode!: number;

  @ApiProperty({ example: 'Success' })
  message!: string;

  @ApiProperty({ example: new Date().toISOString() })
  timestamp!: string;

  @ApiProperty({ example: '/api/v1/example' })
  path!: string;

  @ApiProperty({ example: 'GET' })
  method!: string;

  data!: T;
}

export class PaginatedResponseDto<T> extends ApiResponseDto<T[]> {
  @ApiProperty({ example: 1 })
  page!: number;

  @ApiProperty({ example: 10 })
  limit!: number;

  @ApiProperty({ example: 100 })
  totalItems!: number;

  @ApiProperty({ example: 10 })
  totalPages!: number;

  @ApiProperty({ example: true })
  hasNextPage!: boolean;

  @ApiProperty({ example: false })
  hasPreviousPage!: boolean;
}

export class ErrorResponseDto {
  @ApiProperty({ example: 400 })
  statusCode!: number;

  @ApiProperty({ example: 'Bad Request' })
  message!: string | string[];

  @ApiProperty({ example: new Date().toISOString() })
  timestamp!: string;

  @ApiProperty({ example: '/api/v1/example' })
  path!: string;

  @ApiProperty({ example: 'GET' })
  method!: string;

  @ApiProperty({
    example: [{ field: 'email', message: 'email must be an email' }],
  })
  errors?: Record<string, any>[];
}

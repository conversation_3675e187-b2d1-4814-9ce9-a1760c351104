import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsString, IsNotEmpty } from 'class-validator';
export class CreateDriverVehicleDto {
  // @ApiProperty({ example: 'city-uuid' })
  // @IsUUID()
  // @IsNotEmpty({ message: 'City ID is required' })
  // cityId!: string;

  @ApiProperty({ example: 'vehicle-uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'Vehicle Type ID is required' })
  vehicleTypeId!: string;

  @ApiProperty({ example: 'MH12AB1234', required: false })
  @IsNotEmpty({ message: 'Vehicle Number is required' })
  @IsString()
  vehicleNumber?: string;

  //   @ApiProperty({ example: false, required: false })
  //   @IsOptional()
  //   @IsBoolean()
  //   isNocRequired?: boolean = false;

  //   @ApiProperty({ example: 'https://example.com/noc.pdf', required: false })
  //   @IsOptional()
  //   @IsString()
  //   nocFileUrl?: string;

  //   @ApiProperty({ example: false, required: false })
  //   @IsOptional()
  //   @IsBoolean()
  //   isPrimary?: boolean = false;

  //   @ApiProperty({ enum: VehicleType, required: false })
  //   @IsOptional()
  //   @IsEnum(VehicleType)
  //   type?: VehicleType;

  //   @ApiProperty({ enum: DriverVehicleStatus, required: false })
  //   @IsOptional()
  //   @IsEnum(DriverVehicleStatus)
  //   status?: DriverVehicleStatus = DriverVehicleStatus.pending;
}

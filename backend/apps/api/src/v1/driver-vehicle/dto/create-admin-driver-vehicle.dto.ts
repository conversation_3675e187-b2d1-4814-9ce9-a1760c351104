import { ApiProperty } from '@nestjs/swagger';
import {
  IsUUID,
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
} from 'class-validator';

export class CreateAdminDriverVehicleDto {
  @ApiProperty({
    example: 'profile-uuid',
    description: 'User profile ID for the driver',
  })
  @IsUUID()
  @IsNotEmpty({ message: 'Profile ID is required' })
  profileId!: string;

  @ApiProperty({
    example: 'vehicle-type-uuid',
    description: 'Vehicle type ID',
  })
  @IsUUID()
  @IsNotEmpty({ message: 'Vehicle Type ID is required' })
  vehicleTypeId!: string;

  @ApiProperty({
    example: 'MH12AB1234',
    description: 'Vehicle registration number',
  })
  @IsNotEmpty({ message: 'Vehicle Number is required' })
  @IsString()
  vehicleNumber!: string;

  @ApiProperty({
    example: 'city-product-uuid',
    description: 'City product ID (optional)',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  cityProductId?: string;

  @ApiProperty({
    example: false,
    description: 'Whether NOC is required for this vehicle',
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isNocRequired?: boolean = false;

  @ApiProperty({
    example: false,
    description: 'Whether this is the primary vehicle for the driver',
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean = false;
}

import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsDateString,
  IsBoolean,
  IsObject,
} from 'class-validator';

export class UpdateDriverKycDto {
  @ApiProperty({
    example: 'ABCD1234567890',
    description: 'Document number (e.g., Aadhaar number, license number)',
    required: false,
  })
  @IsOptional()
  @IsString()
  documentNumber?: string;

  @ApiProperty({
    example: 'https://example.com/documents/aadhaar.pdf',
    description: 'URL to the uploaded document',
    required: false,
  })
  @IsOptional()
  @IsString()
  documentUrl?: string;

  @ApiProperty({
    example: {
      aadhaar_number: '1234-5678-9012',
      name: '<PERSON>',
      address: '123 Main St, City',
      date_of_birth: '1990-01-01',
    },
    description: 'Additional document fields as JSON',
    required: false,
  })
  @IsOptional()
  @IsObject()
  documentFields?: any;

  @ApiProperty({
    example: '2030-12-31',
    description: 'Document expiry date (if applicable)',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiProperty({
    example: false,
    description: 'Whether the document was fetched from DigiLocker',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  fromDigilocker?: boolean;
}

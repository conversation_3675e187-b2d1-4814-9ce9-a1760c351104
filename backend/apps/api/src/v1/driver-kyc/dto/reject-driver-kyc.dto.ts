import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MinLength } from 'class-validator';

export class RejectDriverKycDto {
  @ApiProperty({
    example:
      'Document is not clear. Please upload a clearer image of your Aadhaar card.',
    description: 'Reason for rejecting the KYC document',
    minLength: 10,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(10, {
    message: 'Rejection note must be at least 10 characters long',
  })
  rejectionNote!: string;
}

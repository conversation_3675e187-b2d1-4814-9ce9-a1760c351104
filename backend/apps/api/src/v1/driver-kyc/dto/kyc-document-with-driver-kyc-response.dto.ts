import { ApiProperty } from '@nestjs/swagger';
import { CountryResponseDto } from '../../country/dto/country-response.dto';

export class DriverKycDataResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  userProfileId!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  kycDocumentId!: string;

  @ApiProperty({
    example: 'ABCD1234567890',
    required: false,
    nullable: true,
  })
  documentNumber?: string | null;

  @ApiProperty({
    example: 'https://signed-s3-url.amazonaws.com/uploads/document.pdf',
    required: false,
    nullable: true,
    description: 'Signed S3 URL for the document',
  })
  documentUrl?: string | null;

  @ApiProperty({
    example: {
      aadhaar_number: '1234-5678-9012',
      name: '<PERSON>',
      address: '123 Main St, City',
      date_of_birth: '1990-01-01',
    },
    required: false,
    nullable: true,
  })
  documentFields?: any | null;

  @ApiProperty({
    example: '2030-12-31',
    required: false,
    nullable: true,
  })
  expiryDate?: Date | null;

  @ApiProperty({ example: false })
  fromDigilocker!: boolean;

  @ApiProperty({
    example: 'PENDING',
    enum: ['PENDING', 'APPROVED', 'REJECTED'],
  })
  status!: string;

  @ApiProperty({
    example: 'Document is not clear',
    required: false,
    nullable: true,
  })
  rejectionNote?: string | null;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ example: null, required: false, nullable: true })
  deletedAt?: Date | null;
}

export class KycDocumentWithDriverKycResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  countryId!: string;

  @ApiProperty({ example: 'Aadhaar Card' })
  name!: string;

  @ApiProperty({ example: 'aadhaar_card' })
  identifier!: string;

  @ApiProperty({
    example: { fields: ['aadhaar_number', 'name', 'address'] },
    required: false,
    nullable: true,
  })
  requiredFields?: any | null;

  @ApiProperty({ example: true })
  isMandatory!: boolean;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ example: null, required: false, nullable: true })
  deletedAt?: Date | null;

  @ApiProperty({ type: CountryResponseDto, required: false })
  country?: CountryResponseDto;

  @ApiProperty({
    type: DriverKycDataResponseDto,
    required: false,
    nullable: true,
    description:
      'First driver KYC entry for this document type and profile (if exists)',
  })
  driverKyc?: DriverKycDataResponseDto | null;
}

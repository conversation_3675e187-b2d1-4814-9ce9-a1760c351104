import { ApiProperty } from '@nestjs/swagger';
import { KycDocumentResponseDto } from '../../kyc-document/dto/kyc-document-response.dto';

export class UserResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: '+1234567890', required: false, nullable: true })
  phoneNumber?: string | null;

  @ApiProperty({
    example: '<EMAIL>',
    required: false,
    nullable: true,
  })
  email?: string | null;
}

export class UserProfileResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: 'John', required: false, nullable: true })
  firstName?: string | null;

  @ApiProperty({ example: 'Doe', required: false, nullable: true })
  lastName?: string | null;

  @ApiProperty({ example: '1990-01-01', required: false, nullable: true })
  dateOfBirth?: string | null;

  @ApiProperty({ type: UserResponseDto, required: false })
  user?: UserResponseDto;
}

export class DriverKycResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  id!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  userProfileId!: string;

  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  kycDocumentId!: string;

  @ApiProperty({
    example: 'ABCD1234567890',
    required: false,
    nullable: true,
  })
  documentNumber?: string | null;

  @ApiProperty({
    example: 'https://example.com/documents/aadhaar.pdf',
    required: false,
    nullable: true,
  })
  documentUrl?: string | null;

  @ApiProperty({
    example: {
      aadhaar_number: '1234-5678-9012',
      name: 'John Doe',
      address: '123 Main St, City',
      date_of_birth: '1990-01-01',
    },
    required: false,
    nullable: true,
  })
  documentFields?: any | null;

  @ApiProperty({
    example: '2030-12-31',
    required: false,
    nullable: true,
  })
  expiryDate?: Date | null;

  @ApiProperty({ example: false })
  fromDigilocker!: boolean;

  @ApiProperty({
    example: 'PENDING',
    enum: ['PENDING', 'APPROVED', 'REJECTED'],
  })
  status!: string;

  @ApiProperty({
    example: 'Document is not clear',
    required: false,
    nullable: true,
  })
  rejectionNote?: string | null;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ example: null, required: false, nullable: true })
  deletedAt?: Date | null;

  @ApiProperty({ type: UserProfileResponseDto, required: false })
  userProfile?: UserProfileResponseDto;

  @ApiProperty({ type: KycDocumentResponseDto, required: false })
  kycDocument?: KycDocumentResponseDto;
}

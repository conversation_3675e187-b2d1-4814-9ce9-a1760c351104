import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class CreateCountryDto {
  @ApiProperty({ example: 'India' })
  @IsString()
  name!: string;

  @ApiProperty({ example: 'IN' })
  @IsString()
  iso2!: string;

  @ApiProperty({ example: 'IND', required: false })
  @IsOptional()
  @IsString()
  iso3?: string;

  @ApiProperty({ example: '+91', required: false })
  @IsOptional()
  @IsString()
  phoneCode?: string;

  @ApiProperty({ example: 'INR', required: false })
  @IsOptional()
  @IsString()
  currency?: string;
}

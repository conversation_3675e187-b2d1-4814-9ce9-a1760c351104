import { Module } from '@nestjs/common';
import { FileUploadController } from './file-upload.controller';
import { AwsFileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { AppConfigModule as SharedAppConfigModule } from '@shared/shared';

@Module({
  imports: [SharedAppConfigModule],
  controllers: [FileUploadController],
  providers: [AwsFileUploadService],
})
export class ApiFileUploadModule {}

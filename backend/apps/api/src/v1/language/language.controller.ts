import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { LanguageService } from '../../../../../libs/shared/src/modules/language/language.service';
import { CreateLanguageDto } from './dto/create-language.dto';
import { UpdateLanguageDto } from './dto/update-language.dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Languages')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('languages')
export class LanguageController {
  constructor(private readonly languageService: LanguageService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new language' })
  @ApiResponse({ status: 201, type: ApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  async create(@Body() body: CreateLanguageDto) {
    const data = await this.languageService.createLanguage(body);
    return {
      success: true,
      message: 'Language created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all languages' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  async findAll() {
    const data = await this.languageService.findAllLanguages();
    return {
      success: true,
      message: 'Languages fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('paginate')
  @ApiOperation({ summary: 'Get paginated languages' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async paginate(@Query() query: PaginationDto) {
    const result = await this.languageService.paginateLanguages(
      query.page,
      query.limit,
      query,
    );
    return {
      success: true,
      message: 'Languages paginated successfully',
      data: result.data,
      meta: {
        page: result.meta.page,
        limit: result.meta.limit,
        total: result.meta.total,
        totalPages: result.meta.totalPages,
        hasNextPage: result.meta.hasNextPage,
        hasPreviousPage: result.meta.hasPrevPage,
      },
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a language by ID' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async findOne(@Param('id') id: string) {
    const data = await this.languageService.findLanguageById(id);
    return {
      success: true,
      message: 'Language fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a language' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async update(@Param('id') id: string, @Body() body: UpdateLanguageDto) {
    const data = await this.languageService.updateLanguage(id, body);
    return {
      success: true,
      message: 'Language updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a language (soft delete)' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async remove(@Param('id') id: string) {
    const data = await this.languageService.deleteLanguage(id);
    return {
      success: true,
      message: 'Language deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }
}

import { Module } from '@nestjs/common';
import { LanguageController } from './language.controller';
import { LanguageService } from '../../../../../libs/shared/src/modules/language/language.service';
import { LanguageModule as SharedLanguageModule } from '@shared/shared/modules/language/language.module';

@Module({
  imports: [SharedLanguageModule],
  controllers: [LanguageController],
  providers: [LanguageService],
})
export class ApiLanguageModule {}

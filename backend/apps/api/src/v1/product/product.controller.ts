import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ProductService } from '../../../../../libs/shared/src/modules/product/product.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductResponseDto } from './dto/product-response.dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Products')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('products')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new product' })
  @ApiResponse({
    status: 201,
    description: 'Product created successfully',
    type: ApiResponseDto<ProductResponseDto>,
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(@Body() createProductDto: CreateProductDto) {
    const data = await this.productService.createProduct(createProductDto);
    return {
      success: true,
      message: 'Product created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all products with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    type: PaginatedResponseDto<ProductResponseDto>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(@Query() paginationDto: PaginationDto) {
    const result = await this.productService.paginateProducts(paginationDto);
    return {
      success: true,
      message: 'Products retrieved successfully',
      data: result.data,
      meta: result.meta,
      timestamp: Date.now(),
    };
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all products without pagination' })
  @ApiResponse({
    status: 200,
    description: 'All products retrieved successfully',
    type: ApiResponseDto<ProductResponseDto[]>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAllWithoutPagination() {
    const data = await this.productService.findAllProducts();
    return {
      success: true,
      message: 'All products retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('search')
  @ApiOperation({ summary: 'Search products by name' })
  @ApiQuery({ name: 'q', required: true, type: String, example: 'Standard' })
  @ApiResponse({
    status: 200,
    description: 'Products found successfully',
    type: ApiResponseDto<ProductResponseDto[]>,
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async searchProducts(@Query('q') searchTerm: string) {
    const data = await this.productService.searchProductsByName(searchTerm);
    return {
      success: true,
      message: 'Products found successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('vehicle-type/:vehicleTypeId')
  @ApiOperation({ summary: 'Get products by vehicle type ID' })
  @ApiParam({
    name: 'vehicleTypeId',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    type: ApiResponseDto<ProductResponseDto[]>,
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findByVehicleType(@Param('vehicleTypeId') vehicleTypeId: string) {
    const data =
      await this.productService.findProductsByVehicleTypeId(vehicleTypeId);
    return {
      success: true,
      message: 'Products retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('city/:cityId')
  @ApiOperation({ summary: 'Get products by city ID' })
  @ApiParam({
    name: 'cityId',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    type: ApiResponseDto<ProductResponseDto[]>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findByCity(@Param('cityId') cityId: string) {
    const data = await this.productService.findProductsByCityId(cityId);
    return {
      success: true,
      message: 'Products retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('city/:cityId/vehicle-type/:vehicleTypeId')
  @ApiOperation({ summary: 'Get active products by city and vehicle type' })
  @ApiParam({
    name: 'cityId',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiParam({
    name: 'vehicleTypeId',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    type: ApiResponseDto<ProductResponseDto[]>,
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findByCityAndVehicleType(
    @Param('cityId') cityId: string,
    @Param('vehicleTypeId') vehicleTypeId: string,
  ) {
    const data =
      await this.productService.findActiveProductsByCityAndVehicleType(
        cityId,
        vehicleTypeId,
      );
    return {
      success: true,
      message: 'Products retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get product by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Product retrieved successfully',
    type: ApiResponseDto<ProductResponseDto>,
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findOne(@Param('id') id: string) {
    const data = await this.productService.findProductById(id);
    return {
      success: true,
      message: 'Product retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update product by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Product updated successfully',
    type: ApiResponseDto<ProductResponseDto>,
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
  ) {
    const data = await this.productService.updateProduct(id, updateProductDto);
    return {
      success: true,
      message: 'Product updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete product by ID (soft delete)' })
  @ApiParam({
    name: 'id',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Product deleted successfully',
    type: ApiResponseDto<ProductResponseDto>,
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async remove(@Param('id') id: string) {
    const data = await this.productService.deleteProduct(id);
    return {
      success: true,
      message: 'Product deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }
}

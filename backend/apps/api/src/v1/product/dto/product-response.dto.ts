import { ApiProperty } from '@nestjs/swagger';

export class VehicleTypeResponseDto {
  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  id!: string;

  @ApiProperty({ example: 'Car' })
  name!: string;

  @ApiProperty({ example: 'Four-wheeler vehicle', required: false })
  description?: string;

  @ApiProperty({ example: 'https://example.com/car.jpg', required: false })
  image?: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  createdAt!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  updatedAt!: string;
}

export class CityProductResponseDto {
  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  id!: string;

  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  cityId!: string;

  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  productId!: string;

  @ApiProperty({ example: true })
  isEnabled!: boolean;

  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  vehicleTypeId!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  createdAt!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  updatedAt!: string;
}

export class ProductResponseDto {
  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  id!: string;

  @ApiProperty({ example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890' })
  vehicleTypeId!: string;

  @ApiProperty({ example: 'Standard Ride' })
  name!: string;

  @ApiProperty({
    example: 'Comfortable and affordable ride for daily commuting',
  })
  description!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  createdAt!: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  updatedAt!: string;

  @ApiProperty({ type: VehicleTypeResponseDto, required: false })
  vehicleType?: VehicleTypeResponseDto;

  @ApiProperty({ type: [CityProductResponseDto], required: false })
  cityProducts?: CityProductResponseDto[];
}

export class ProductStatisticsResponseDto {
  @ApiProperty({ example: 25 })
  totalProducts!: number;

  @ApiProperty({
    example: { Car: 10, Bike: 8, Auto: 7 },
    description: 'Number of products grouped by vehicle type',
  })
  productsByVehicleType!: Record<string, number>;
}

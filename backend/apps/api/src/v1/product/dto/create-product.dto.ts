import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, MaxLength } from 'class-validator';

export class CreateProductDto {
  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Vehicle type ID that this product belongs to',
  })
  @IsUUID(4, { message: 'Vehicle type ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Vehicle type ID is required' })
  vehicleTypeId!: string;

  @ApiProperty({
    example: 'Standard Ride',
    description: 'Name of the product',
    maxLength: 255,
  })
  @IsString({ message: 'Product name must be a string' })
  @IsNotEmpty({ message: 'Product name is required' })
  @MaxLength(255, { message: 'Product name cannot exceed 255 characters' })
  name!: string;

  @ApiProperty({
    example: 'Comfortable and affordable ride for daily commuting',
    description: 'Description of the product',
    maxLength: 1000,
  })
  @IsString({ message: 'Product description must be a string' })
  @IsNotEmpty({ message: 'Product description is required' })
  @MaxLength(1000, {
    message: 'Product description cannot exceed 1000 characters',
  })
  description!: string;
}

import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Query,
  HttpCode,
  HttpStatus,
  // UseGuards,
  Headers,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiHeader,
  ApiParam,
} from '@nestjs/swagger';
import { UserProfileService } from '@shared/shared/modules/user-profile/user-profile.service';
import { CreateDriverDto } from './dto/create-driver.dto';
import { UpdateDriverDto } from './dto/update-driver.dto';
import { DriverFilterDto } from './dto/driver-filter.dto';
import { DriverResponseDto } from './dto/driver-response.dto';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { RegisterDriverDto } from './dto/register-driver.dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { AuthRole } from '@shared/shared/common/constants/constants';

@ApiTags('Admin - Driver Management')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('drivers/admin')
export class DriverController {
  constructor(private readonly userProfileService: UserProfileService) {}

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Register driver with phone number',
    description:
      'Register a new driver with phone number and send OTP for verification.',
  })
  @ApiResponse({
    status: 201,
    type: ApiResponseDto,
    description: 'Driver registered successfully, OTP sent',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 409, type: ApiErrorResponseDto })
  async registerDriver(@Body() registerDriverDto: RegisterDriverDto) {
    const data = await this.userProfileService.registerDriverWithPhone(
      registerDriverDto.phoneNumber,
    );
    return {
      success: true,
      message:
        'Driver registered successfully. OTP sent to mobile number for verification.',
      data,
      timestamp: Date.now(),
    };
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new driver profile',
    description:
      'Create a new driver profile. Phone number must be verified before creating profile.',
  })
  @ApiResponse({
    status: 201,
    type: ApiResponseDto<DriverResponseDto>,
    description: 'Driver profile created successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 409, type: ApiErrorResponseDto })
  async createDriver(@Body() createDriverDto: CreateDriverDto) {
    const data =
      await this.userProfileService.createDriverForAdmin(createDriverDto);
    return {
      success: true,
      message: 'Driver profile created successfully.',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get paginated list of drivers',
    description: 'Retrieve drivers with pagination and optional filters',
  })
  @ApiResponse({
    status: 200,
    type: PaginatedResponseDto<DriverResponseDto>,
    description: 'Drivers retrieved successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type',
    required: true,
    enum: [AuthRole.SUPER_ADMIN],
  })
  async listDrivers(
    @Query() filters: DriverFilterDto,
    @Headers('x-app-type') role: AuthRole,
  ) {
    const result = await this.userProfileService.listDriversForAdmin(
      filters,
      role,
    );
    return {
      success: true,
      message: 'Drivers retrieved successfully',
      data: result.data,
      meta: result.meta,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver by ID',
    description: 'Retrieve a specific driver by their ID',
  })
  @ApiParam({ name: 'id', description: 'Driver ID', type: 'string' })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto<DriverResponseDto>,
    description: 'Driver retrieved successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async getDriverById(@Param('id') profileId: string) {
    const data = await this.userProfileService.getDriverByIdForAdmin(profileId);
    return {
      success: true,
      message: 'Driver retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':profileId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update driver profile',
    description:
      'Update driver profile information. Only specified fields will be updated.',
  })
  @ApiParam({ name: 'profileId', description: 'Driver ID', type: 'string' })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto<DriverResponseDto>,
    description: 'Driver updated successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async updateDriver(
    @Param('profileId') profileId: string,
    @Body() updateDriverDto: UpdateDriverDto,
  ) {
    const data = await this.userProfileService.updateDriverForAdmin(
      profileId,
      updateDriverDto,
    );
    return {
      success: true,
      message: 'Driver updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post('resend-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Resend OTP for phone verification',
    description: 'Resend OTP to the specified mobile number for verification',
  })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto,
    description: 'OTP sent successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async resendOtp(@Body() resendOtpDto: ResendOtpDto) {
    await this.userProfileService.resendOtpForAdmin(resendOtpDto.phoneNumber);
    return {
      success: true,
      message: 'OTP sent successfully',
      timestamp: Date.now(),
    };
  }

  @Post('verify-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify OTP for phone number',
    description: 'Verify the OTP sent to the mobile number',
  })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto,
    description: 'Phone number verified successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    const data = await this.userProfileService.verifyOtpForAdmin(
      verifyOtpDto.phoneNumber,
      verifyOtpDto.otp,
    );
    return {
      success: true,
      message: data.message,
      timestamp: Date.now(),
    };
  }
}

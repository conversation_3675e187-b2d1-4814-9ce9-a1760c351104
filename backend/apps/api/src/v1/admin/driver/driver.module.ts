import { Module } from '@nestjs/common';
import { DriverController } from './driver.controller';
import { UserProfileModule } from '@shared/shared/modules/user-profile/user-profile.module';
import { AuthModule } from '@shared/shared/modules/auth/auth.module';

@Module({
  imports: [AuthModule, UserProfileModule],
  controllers: [DriverController],
  providers: [],
  exports: [],
})
export class ApiDriverModule {}

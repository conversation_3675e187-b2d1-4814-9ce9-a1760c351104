import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  // UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  Api<PERSON>earerAuth,
} from '@nestjs/swagger';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { CreateVehicleDto, UpdateVehicleDto } from './dto/vehicle.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { VehicleTypeService } from '@shared/shared/modules/vehicle-type/vehicle-type.service';

@ApiTags('Vehicle Types')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('vehicle-type')
export class VehicleTypeController {
  constructor(private readonly vehicleTypeService: VehicleTypeService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new vehicle' })
  @ApiResponse({ status: 201, type: ApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  async create(@Body() body: CreateVehicleDto) {
    const data = await this.vehicleTypeService.createVehicleType(body);
    return {
      success: true,
      message: 'Vehicle created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all vehicles' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  async findAll() {
    const data = await this.vehicleTypeService.findAllVehicleTypes();
    return {
      success: true,
      message: 'Vehicles fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('paginate')
  @ApiOperation({ summary: 'Get paginated vehicles' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async paginate(@Query() query: PaginationDto) {
    const result = await this.vehicleTypeService.paginateVehicleTypes(
      query.page,
      query.limit,
      query,
    );
    return {
      success: true,
      message: 'Vehicles paginated successfully',
      data: result.data,
      meta: {
        page: result.meta.page,
        limit: result.meta.limit,
        total: result.meta.total,
        totalPages: result.meta.totalPages,
        hasNextPage: result.meta.hasNextPage,
        hasPreviousPage: result.meta.hasPrevPage,
      },
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a vehicle by ID' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async findOne(@Param('id') id: string) {
    const data = await this.vehicleTypeService.findVehicleTypeById(id);
    return {
      success: true,
      message: 'Vehicle fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a vehicle' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async update(@Param('id') id: string, @Body() body: UpdateVehicleDto) {
    const data = await this.vehicleTypeService.updateVehicleType(id, body);
    return {
      success: true,
      message: 'Vehicle updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a vehicle (soft delete)' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async remove(@Param('id') id: string) {
    const data = await this.vehicleTypeService.deleteVehicleType(id);
    return {
      success: true,
      message: 'Vehicle deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }
}

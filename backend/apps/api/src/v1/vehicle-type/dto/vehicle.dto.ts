import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateVehicleDto {
  @ApiProperty({ example: 'Toyota Prius', description: 'Name of the vehicle' })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({ example: 'A hybrid electric car', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 'https://example.com/image.png', required: false })
  @IsOptional()
  @IsString()
  image?: string;
}

export class UpdateVehicleDto extends PartialType(CreateVehicleDto) {}

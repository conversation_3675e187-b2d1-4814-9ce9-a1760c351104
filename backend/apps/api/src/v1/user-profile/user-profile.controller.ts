import {
  Controller,
  Get,
  Patch,
  Post,
  Body,
  Req,
  UseGuards,
  HttpCode,
  HttpStatus,
  UnauthorizedException,
  Headers,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  Api<PERSON>eader,
} from '@nestjs/swagger';
import { AuthRole } from '@shared/shared/common/constants/constants';
import { Request } from 'express';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { UserProfileService } from '@shared/shared/modules/user-profile/user-profile.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { VerifyEmailDto } from './dto/verify-email.dto';
import { VerifyPhoneDto } from './dto/verify-phone.dto';
import { ProfileResponseDto } from './dto/profile-response.dto';
import { SaveDriverProfileDto } from './dto/save-driver-profile.dto';
import { UpdateUserLanguageDto } from './dto/update-user-language.dto';
import { UpdateTermsConditionsDto } from './dto/update-terms-conditions.dto';
import { UserOnboardingService } from '@shared/shared/modules/user-onboarding/user-onboarding.service';
import { ResendEmailOtpDto, ResendPhoneOtpDto } from './dto';

@ApiTags('User Profile')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('user-profile')
export class UserProfileController {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly userOnboardingService: UserOnboardingService,
  ) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get user profile by role from header' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (rider, driver)',
    required: true,
    enum: [AuthRole.RIDER, AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User profile retrieved successfully',
    type: ProfileResponseDto,
  })
  async getProfile(
    @Req() req: Request,
    @Headers('x-app-type') appType: string,
  ): Promise<ProfileResponseDto> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    // Validate app type
    if (!appType || !Object.values(AuthRole).includes(appType as AuthRole)) {
      throw new BadRequestException('Invalid or missing x-app-type header');
    }

    const roleName = appType as AuthRole;
    const roles = (req.user as any)?.roles || [];

    // Check if user has the specified role
    if (!roles.includes(roleName)) {
      throw new UnauthorizedException(
        `User does not have the ${roleName} role`,
      );
    }

    const { profile, user } =
      await this.userProfileService.getProfileByUserIdAndRoleName(
        userId,
        roleName,
      );

    const response = new ProfileResponseDto();
    if (profile) {
      Object.assign(response, profile);
    }
    if (user) {
      response.email = user.email;
      response.phone = user.phoneNumber;
      response.emailVerified = !!user.emailVerifiedAt;
      response.phoneVerified = !!user.phoneVerifiedAt;
      response.isPolicyAllowed = user.isPolicyAllowed;
    }

    return response;
  }

  @Get('onboarding-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get user onboarding status by role from header' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (rider, driver)',
    required: true,
    enum: [AuthRole.RIDER, AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Current onboarding status fetched successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Current onboarding status fetched',
        },
        data: { type: 'object' },
        timestamp: { type: 'number', example: 1721913600000 },
      },
    },
  })
  async getProfileOnboardingStatus(
    @Req() req: Request,
    @Headers('x-app-type') appType: string,
  ): Promise<any> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    // Validate app type
    if (!appType || !Object.values(AuthRole).includes(appType as AuthRole)) {
      throw new BadRequestException('Invalid or missing x-app-type header');
    }

    const roleName = appType as AuthRole;
    const roles = (req.user as any)?.roles || [];

    // Check if user has the specified role
    if (!roles.includes(roleName)) {
      throw new UnauthorizedException(
        `User does not have the ${roleName} role`,
      );
    }

    const onboarding =
      await this.userOnboardingService.findOnboardByUserIdAndRoleName(
        userId,
        roleName,
      );
    return {
      success: true,
      message: 'Current onboarding status fetched',
      data: onboarding,
      timestamp: Date.now(),
    };
  }

  @Patch()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update user profile' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (rider, driver)',
    required: true,
    enum: [AuthRole.RIDER, AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Profile updated successfully',
    type: Object,
  })
  async updateProfile(
    @Req() req: Request,
    @Headers('x-app-type') appType: string,
    @Body() updateProfileDto: UpdateProfileDto,
  ): Promise<{
    profile: ProfileResponseDto;
    emailVerificationRequired?: boolean;
    phoneVerificationRequired?: boolean;
  }> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    // Validate app type
    if (!appType || !Object.values(AuthRole).includes(appType as AuthRole)) {
      throw new BadRequestException('Invalid or missing x-app-type header');
    }

    const roleName = appType as AuthRole;
    const roles = (req.user as any)?.roles || [];

    // Check if user has the specified role
    if (!roles.includes(roleName)) {
      throw new UnauthorizedException(
        `User does not have the ${roleName} role`,
      );
    }
    console.log('User is authorized', { userId, roleName });
    const result = await this.userProfileService.updateUserProfile(
      userId,
      roleName,
      updateProfileDto,
    );

    // Map to response DTO
    const response = new ProfileResponseDto();
    if (result.profile) {
      // Copy profile properties
      Object.assign(response, result.profile);
    }

    return {
      profile: response,
      ...(result.emailVerificationRequired && {
        emailVerificationRequired: result.emailVerificationRequired,
      }),
      ...(result.phoneVerificationRequired && {
        phoneVerificationRequired: result.phoneVerificationRequired,
      }),
    };
  }

  @Patch('driver-onboard')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Driver onboard profile' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (driver)',
    required: true,
    enum: [AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Profile updated successfully',
    type: Object,
  })
  async driverProfile(
    @Req() req: Request,
    @Headers('x-app-type') appType: string,
    @Body() updateProfileDto: SaveDriverProfileDto,
  ): Promise<any> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    // Validate app type
    if (!appType || !Object.values(AuthRole).includes(appType as AuthRole)) {
      throw new BadRequestException('Invalid or missing x-app-type header');
    }

    const roleName = appType as AuthRole;
    const roles = (req.user as any)?.roles || [];

    // Check if user has the specified role
    if (!roles.includes(roleName)) {
      throw new UnauthorizedException(
        `User does not have the ${roleName} role`,
      );
    }
    console.log('User is authorized', { userId, roleName });
    const result = await this.userProfileService.createOrUpdateDriverProfile(
      userId,
      updateProfileDto,
    );

    return {
      success: true,
      message: 'Profile updated',
      data: result,
      timestamp: Date.now(),
    };
  }
  @Patch('driver-onboard/language')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Driver onboard language' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (driver)',
    required: true,
    enum: [AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Language updated successfully',
    type: Object,
  })
  async driverProfileUpdateLanguage(
    @Req() req: Request,
    @Headers('x-app-type') appType: string,
    @Body() updateProfileDto: UpdateUserLanguageDto,
  ): Promise<any> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    // Validate app type
    if (!appType || !Object.values(AuthRole).includes(appType as AuthRole)) {
      throw new BadRequestException('Invalid or missing x-app-type header');
    }

    const roleName = appType as AuthRole;
    const roles = (req.user as any)?.roles || [];

    // Check if user has the specified role
    if (!roles.includes(roleName)) {
      throw new UnauthorizedException(
        `User does not have the ${roleName} role`,
      );
    }
    console.log('User is authorized', { userId, roleName });
    const result = await this.userProfileService.updateDriverLanguage(
      userId,
      updateProfileDto.languageId,
    );

    return {
      success: true,
      message: 'Language updated',
      data: result,
      timestamp: Date.now(),
    };
  }

  @Post('verify-email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify email update with OTP' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (rider, driver)',
    required: true,
    enum: [AuthRole.RIDER, AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Email verified successfully',
    type: Boolean,
  })
  async verifyEmail(
    @Req() req: Request,
    @Body() verifyEmailDto: VerifyEmailDto,
  ): Promise<{ success: boolean }> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    const { email, otp } = verifyEmailDto;
    const result = await this.userProfileService.verifyEmailUpdate(
      userId,
      email,
      otp,
    );

    return { success: result };
  }

  @Post('verify-phone')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify phone update with OTP' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (rider, driver)',
    required: true,
    enum: [AuthRole.RIDER, AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Phone verified successfully',
    type: Boolean,
  })
  async verifyPhone(
    @Req() req: Request,
    @Body() verifyPhoneDto: VerifyPhoneDto,
  ): Promise<{ success: boolean }> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    const { phone, otp } = verifyPhoneDto;
    const result = await this.userProfileService.verifyPhoneUpdate(
      userId,
      phone,
      otp,
    );

    return { success: result };
  }

  @Post('resend-email-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Resend OTP for email verification' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (rider, driver)',
    required: true,
    enum: [AuthRole.RIDER, AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Email OTP resent successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'OTP resent to your email address',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'No pending email verification found or rate limit exceeded',
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User not found' })
  async resendEmailOtp(
    @Req() req: Request,
    @Body() resendEmailOtpDto: ResendEmailOtpDto,
  ): Promise<{ success: boolean; message: string }> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    const { email } = resendEmailOtpDto;
    const result = await this.userProfileService.resendEmailOtp(userId, email);

    return {
      success: result,
      message: 'OTP resent to your email address',
    };
  }

  @Post('resend-phone-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Resend OTP for phone verification' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (rider, driver)',
    required: true,
    enum: [AuthRole.RIDER, AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Phone OTP resent successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'OTP resent to your phone number' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'No pending phone verification found or rate limit exceeded',
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User not found' })
  async resendPhoneOtp(
    @Req() req: Request,
    @Body() resendPhoneOtpDto: ResendPhoneOtpDto,
  ): Promise<{ success: boolean; message: string }> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    const { phone } = resendPhoneOtpDto;
    const result = await this.userProfileService.resendPhoneOtp(userId, phone);

    return {
      success: result,
      message: 'OTP resent to your phone number',
    };
  }

  @Patch('terms-conditions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update terms and conditions acceptance status' })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type (rider, driver)',
    required: true,
    enum: [AuthRole.RIDER, AuthRole.DRIVER],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Terms and conditions acceptance status updated successfully',
    type: Object,
  })
  async updateTermsConditions(
    @Req() req: Request,
    @Headers('x-app-type') appType: string,
    @Body() updateTermsConditionsDto: UpdateTermsConditionsDto,
  ): Promise<{ success: boolean }> {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    // Validate app type
    if (!appType || !Object.values(AuthRole).includes(appType as AuthRole)) {
      throw new BadRequestException('Invalid or missing x-app-type header');
    }

    const roleName = appType as AuthRole;
    const roles = (req.user as any)?.roles || [];

    // Check if user has the specified role
    if (!roles.includes(roleName)) {
      throw new UnauthorizedException(
        `User does not have the ${roleName} role`,
      );
    }

    const result =
      await this.userProfileService.updateTermsConditionsAcceptance(
        userId,
        updateTermsConditionsDto.isPolicyAllowed,
      );

    return { success: result };
  }
}

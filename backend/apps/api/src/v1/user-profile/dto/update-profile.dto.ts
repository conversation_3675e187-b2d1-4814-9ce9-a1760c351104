import { ApiPropertyOptional } from '@nestjs/swagger';
import { Gender } from '@shared/shared/repositories/models/userProfile.model';
import {
  IsDate,
  IsEmail,
  IsEnum,
  IsOptional,
  IsPhoneNumber,
  IsString,
  IsUUID,
} from 'class-validator';

export class UpdateProfileDto {
  @ApiPropertyOptional({ example: 'John', description: 'First name' })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({ example: 'Doe', description: 'Last name' })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Email address',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    example: '+1234567890',
    description: 'Phone number',
  })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;

  @ApiPropertyOptional({
    example: 'https://example.com/profile.jpg',
    description: 'Profile picture URL',
  })
  @IsOptional()
  @IsString()
  profilePictureUrl?: string;

  @ApiPropertyOptional({
    enum: Gender,
    description: 'Gender',
    example: Gender.MALE,
  })
  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @ApiPropertyOptional({
    example: '1990-01-01',
    description: 'Date of birth in YYYY-MM-DD format',
  })
  @IsOptional()
  @IsDate()
  dob?: Date;

  @ApiPropertyOptional({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'City ID',
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiPropertyOptional({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Language ID',
  })
  @IsOptional()
  @IsUUID()
  languageId?: string;

  @ApiPropertyOptional({
    example: 'ABC123',
    description: 'Referral code',
  })
  @IsOptional()
  @IsString()
  referralCode?: string;
}

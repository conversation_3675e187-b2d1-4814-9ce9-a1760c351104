import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsPhoneNumber, IsString, Length } from 'class-validator';

export class VerifyPhoneDto {
  @ApiProperty({
    example: '+1234567890',
    description: 'Phone number to verify',
  })
  @IsNotEmpty()
  @IsPhoneNumber()
  phone!: string;

  @ApiProperty({
    example: '1234',
    description: 'OTP code sent to the phone',
  })
  @IsNotEmpty()
  @IsString()
  @Length(4, 6)
  otp!: string;
}

import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Length } from 'class-validator';

export class VerifyEmailDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address to verify',
  })
  @IsNotEmpty()
  @IsEmail()
  email!: string;

  @ApiProperty({
    example: '1234',
    description: 'OTP code sent to the email',
  })
  @IsNotEmpty()
  @IsString()
  @Length(4, 6)
  otp!: string;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProfileResponseDto } from './profile-response.dto';

export class UpdateProfileResponseDto {
  @ApiProperty({
    example: true,
    description: 'Whether the operation was successful',
  })
  success!: boolean;

  @ApiProperty({
    example: 'Profile updated successfully',
    description: 'Message describing the result of the operation',
  })
  message!: string;

  @ApiProperty({
    description: 'The updated profile data',
    type: ProfileResponseDto,
  })
  data!: ProfileResponseDto;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether email verification is required',
  })
  emailVerificationRequired?: boolean;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether phone verification is required',
  })
  phoneVerificationRequired?: boolean;

  @ApiProperty({
    example: 1625097600000,
    description: 'Timestamp of the response',
  })
  timestamp!: number;
}

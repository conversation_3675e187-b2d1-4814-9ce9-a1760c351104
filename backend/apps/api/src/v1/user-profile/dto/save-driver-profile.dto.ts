import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Gender } from '@shared/shared/repositories/models/userProfile.model';
import { IsDate, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

export class SaveDriverProfileDto {
  @ApiProperty({ example: 'John', description: 'First name' })
  @IsOptional()
  firstName!: string;

  @ApiProperty({ example: 'Doe', description: 'Last name' })
  @IsOptional()
  lastName!: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Email address',
  })
  @IsOptional({ message: 'Email is required' })
  email?: string;

  @ApiPropertyOptional({
    example: 'https://example.com/profile.jpg',
    description: 'Profile picture URL',
  })
  @IsOptional()
  profilePictureUrl?: string;

  @ApiProperty({ enum: Gender, required: false })
  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @ApiProperty({
    example: '1990-01-01',
    required: false,
    type: String,
    description: 'Date of birth in YYYY-MM-DD format',
  })
  @IsOptional()
  @IsDate()
  dob?: Date;
  // Add more fields as needed for your driver profile
  @ApiPropertyOptional({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'City ID',
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiPropertyOptional({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Language ID',
  })
  @IsOptional()
  @IsUUID()
  languageId?: string;

  @ApiPropertyOptional({
    example: 'ABC123',
    description: 'Referral code',
  })
  @IsOptional()
  @IsString()
  referralCode?: string;
}

import { Module } from '@nestjs/common';
import { UserProfileModule as SharedUserProfileModule } from '@shared/shared/modules/user-profile/user-profile.module';
import { UserProfileController } from './user-profile.controller';
import { UserOnboardingModule as SharedUserOnboardingModule } from '@shared/shared/modules/user-onboarding/user-onboarding.module';

@Module({
  imports: [SharedUserProfileModule, SharedUserOnboardingModule],
  controllers: [UserProfileController],
  providers: [],
})
export class UserProfileModule {}

import { ApiProperty } from '@nestjs/swagger';

export class CityResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id!: string;

  @ApiProperty({ example: '<PERSON><PERSON>' })
  name!: string;

  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    required: false,
    nullable: true,
  })
  countryId?: string | null;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: Date;

  @ApiProperty({ example: null, required: false, nullable: true })
  deletedAt?: Date | null;
}

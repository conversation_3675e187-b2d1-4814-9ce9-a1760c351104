import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { CityService } from '@shared/shared/modules/city/city.service';
import { VehicleTypeService } from '@shared/shared/modules/vehicle-type/vehicle-type.service';

@ApiTags('Cities')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('cities')
export class CityController {
  constructor(
    private readonly cityService: CityService,
    private readonly vehicleTypeService: VehicleTypeService, // Inject VehicleService to fetch vehicles by city ID
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new city' })
  @ApiResponse({ status: 201, type: ApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  async create(@Body() body: CreateCityDto) {
    const data = await this.cityService.createCity(body);
    return {
      success: true,
      message: 'City created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all cities' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  async findAll() {
    const data = await this.cityService.findAllCities();
    return {
      success: true,
      message: 'Cities fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('paginate')
  @ApiOperation({ summary: 'Get paginated cities' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async paginate(@Query() query: PaginationDto) {
    const result = await this.cityService.paginateCities(
      query.page,
      query.limit,
      query,
    );
    return {
      success: true,
      message: 'Cities paginated successfully',
      data: result.data,
      meta: {
        page: result.meta.page,
        limit: result.meta.limit,
        total: result.meta.total,
        totalPages: result.meta.totalPages,
        hasNextPage: result.meta.hasNextPage,
        hasPreviousPage: result.meta.hasPrevPage,
      },
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a city by ID' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async findOne(@Param('id') id: string) {
    const data = await this.cityService.findCityById(id);
    return {
      success: true,
      message: 'City fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get(':id/vehicles')
  @ApiOperation({ summary: 'Get vehicles by city ID' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async getVehiclesByCityId(@Param('id') id: string) {
    const data =
      await this.vehicleTypeService.findActiveVehicleTypesByCityId(id);
    return {
      success: true,
      message: 'Vehicles fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }
  @Patch(':id')
  @ApiOperation({ summary: 'Update a city' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async update(@Param('id') id: string, @Body() body: UpdateCityDto) {
    const data = await this.cityService.updateCity(id, body);
    return {
      success: true,
      message: 'City updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a city (soft delete)' })
  @ApiResponse({ status: 200, type: ApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async remove(@Param('id') id: string) {
    const data = await this.cityService.deleteCity(id);
    return {
      success: true,
      message: 'City deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }
}

import { Module } from '@nestjs/common';
import { CityController } from './city.controller';
import { CityModule as SharedCityModule } from '@shared/shared/modules/city/city.module';
import { CountryModule as SharedCountryModule } from '@shared/shared/modules/country/country.module';
import { VehicleTypeModule as SharedVehicleTypeModule } from '@shared/shared/modules/vehicle-type/vehicle-type.module';

@Module({
  imports: [SharedCityModule, SharedCountryModule, SharedVehicleTypeModule],
  controllers: [CityController],
})
export class ApiCityModule {}

import { ApiProperty } from '@nestjs/swagger';

export class MemoryDto {
  @ApiProperty({ example: 50000000, description: 'Used memory in bytes' })
  used!: number;

  @ApiProperty({ example: 100000000, description: 'Total memory in bytes' })
  total!: number;

  @ApiProperty({ example: 50.5, description: 'Memory usage percentage' })
  percentage!: number;
}

export class DatabaseDto {
  @ApiProperty({
    example: 'connected',
    enum: ['connected', 'disconnected'],
    description: 'Database connection status',
  })
  status!: 'connected' | 'disconnected';

  @ApiProperty({
    example: 25,
    description: 'Database response time in milliseconds',
    required: false,
  })
  responseTime?: number;
}

export class ServiceStatusDto {
  @ApiProperty({
    example: 'OK',
    enum: ['OK', 'ERROR'],
    description: 'Service status',
  })
  status!: 'OK' | 'ERROR';

  @ApiProperty({
    example: 'Service is healthy',
    description: 'Status message',
    required: false,
  })
  message?: string;

  @ApiProperty({
    example: 15,
    description: 'Service response time in milliseconds',
    required: false,
  })
  responseTime?: number;
}

export class HealthStatusDto {
  @ApiProperty({
    example: 'OK',
    enum: ['OK', 'ERROR', 'PARTIAL'],
    description: 'Overall health status',
  })
  status!: 'OK' | 'ERROR' | 'PARTIAL';

  @ApiProperty({
    example: '2025-07-21T10:30:00.000Z',
    description: 'Health check timestamp',
  })
  timestamp!: string;

  @ApiProperty({ example: 3600, description: 'Application uptime in seconds' })
  uptime!: number;

  @ApiProperty({ example: '1.0.0', description: 'Application version' })
  version!: string;

  @ApiProperty({ example: 'development', description: 'Environment name' })
  environment!: string;

  @ApiProperty({ type: MemoryDto, description: 'Memory usage information' })
  memory!: MemoryDto;

  @ApiProperty({
    type: DatabaseDto,
    description: 'Database connection information',
    required: false,
  })
  database?: DatabaseDto;

  @ApiProperty({
    description: 'Status of various services',
    example: {
      redis: {
        status: 'OK',
        responseTime: 5,
      },
      'external-api': {
        status: 'OK',
        message: 'Service is healthy',
        responseTime: 120,
      },
    },
  })
  services!: Record<string, ServiceStatusDto>;
}

export class SimpleHealthDto {
  @ApiProperty({ example: 'OK', description: 'Simple health status' })
  status!: string;

  @ApiProperty({
    example: '2025-07-21T10:30:00.000Z',
    description: 'Health check timestamp',
  })
  timestamp!: string;
}

export class ReadinessDto {
  @ApiProperty({ example: true, description: 'Application readiness status' })
  ready!: boolean;

  @ApiProperty({
    description: 'Individual health checks',
    example: {
      database: true,
      configuration: true,
      memory: true,
    },
  })
  checks!: Record<string, boolean>;
}

export class LivenessDto {
  @ApiProperty({ example: true, description: 'Application liveness status' })
  alive!: boolean;
}

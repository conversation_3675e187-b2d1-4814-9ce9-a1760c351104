# Health Check Service

This module provides comprehensive health checking capabilities for the Tukxi API.

## Endpoints

All health endpoints are available under the `/v1/health` path:

### 1. Full Health Check - `GET /v1/health`

Returns comprehensive health information including:
- Overall status (OK, PARTIAL, ERROR)
- Timestamp
- Application uptime
- Version information
- Environment details
- Memory usage statistics
- Database connectivity status
- Individual service status checks

**Example Response:**
```json
{
  "status": "OK",
  "timestamp": "2025-07-21T12:00:00.000Z",
  "uptime": 3600,
  "version": "1.0.0",
  "environment": "development",
  "memory": {
    "used": 45,
    "total": 128,
    "percentage": 35
  },
  "database": {
    "status": "connected",
    "responseTime": 12
  },
  "services": {
    "database": {
      "status": "OK",
      "responseTime": 12
    },
    "api": {
      "status": "OK",
      "message": "API is running normally"
    }
  }
}
```

### 2. Simple Health Check - `GET /v1/health/simple`

Returns basic health status for quick checks.

**Example Response:**
```json
{
  "status": "OK",
  "timestamp": "2025-07-21T12:00:00.000Z"
}
```

### 3. Readiness Check - `GET /v1/health/ready`

Checks if the application is ready to serve requests (useful for Kubernetes readiness probes).

**Example Response:**
```json
{
  "ready": true,
  "checks": {
    "database": true,
    "configuration": true,
    "memory": true
  }
}
```

### 4. Liveness Check - `GET /v1/health/live`

Simple liveness check (useful for Kubernetes liveness probes).

**Example Response:**
```json
{
  "alive": true
}
```

## Status Types

- **OK**: All systems operating normally
- **PARTIAL**: Some non-critical services are experiencing issues
- **ERROR**: Critical services are down or experiencing errors

## Usage in Kubernetes

### Liveness Probe
```yaml
livenessProbe:
  httpGet:
    path: /v1/health/live
    port: 3000
  initialDelaySeconds: 30
  periodSeconds: 10
```

### Readiness Probe
```yaml
readinessProbe:
  httpGet:
    path: /v1/health/ready
    port: 3000
  initialDelaySeconds: 5
  periodSeconds: 5
```

## Extending the Health Checks

To add new service checks, modify the `HealthService.getHealth()` method:

```typescript
// Add your custom service check
health.services['your-service'] = {
  status: await this.checkYourService() ? 'OK' : 'ERROR',
  message: 'Custom service description',
  responseTime: responseTime,
};
```

## Database Health Check

The current implementation includes a placeholder for database health checks. To implement actual database connectivity:

1. Inject your database service (e.g., PrismaService)
2. Update the `checkDatabaseHealth()` method to perform a real database query

Example with Prisma:
```typescript
private async checkDatabaseHealth(): Promise<{
  status: 'connected' | 'disconnected';
  responseTime?: number;
}> {
  const startTime = Date.now();
  
  try {
    await this.prisma.$queryRaw`SELECT 1`;
    return {
      status: 'connected',
      responseTime: Date.now() - startTime,
    };
  } catch (error) {
    return {
      status: 'disconnected',
      responseTime: Date.now() - startTime,
    };
  }
}
```

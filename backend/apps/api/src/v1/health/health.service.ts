import { Injectable } from '@nestjs/common';
import { AppConfigService } from '@shared/shared/config';

export interface HealthStatus {
  status: 'OK' | 'ERROR' | 'PARTIAL';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  database?: {
    status: 'connected' | 'disconnected';
    responseTime?: number;
  };
  services: {
    [key: string]: {
      status: 'OK' | 'ERROR';
      message?: string;
      responseTime?: number;
    };
  };
}

@Injectable()
export class HealthService {
  constructor(private readonly configService: AppConfigService) {}

  async getHealth(): Promise<HealthStatus> {
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.heapTotal;
    const usedMemory = memoryUsage.heapUsed;

    const health: HealthStatus = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: '1.0.0',
      environment: process.env['NODE_ENV'] || 'development',
      memory: {
        used: Math.round(usedMemory / 1024 / 1024), // MB
        total: Math.round(totalMemory / 1024 / 1024), // MB
        percentage: Math.round((usedMemory / totalMemory) * 100),
      },
      services: {},
    };

    // Check database connectivity
    try {
      const dbHealth = await this.checkDatabaseHealth();
      health.database = dbHealth;
      const dbService: any = {
        status: dbHealth.status === 'connected' ? 'OK' : 'ERROR',
      };
      if (dbHealth.responseTime !== undefined) {
        dbService.responseTime = dbHealth.responseTime;
      }
      health.services['database'] = dbService;
    } catch (error) {
      health.services['database'] = {
        status: 'ERROR',
        message: 'Database connection failed',
      };
      health.status = 'PARTIAL';
    }

    // Add more service checks as needed
    health.services['api'] = {
      status: 'OK',
      message: 'API is running normally',
    };

    // Determine overall status
    const serviceStatuses = Object.values(health.services).map(
      (service) => service.status,
    );
    if (serviceStatuses.some((status) => status === 'ERROR')) {
      health.status = serviceStatuses.every((status) => status === 'ERROR')
        ? 'ERROR'
        : 'PARTIAL';
    }

    return health;
  }

  async getSimpleHealth(): Promise<{ status: string; timestamp: string }> {
    return {
      status: 'OK',
      timestamp: new Date().toISOString(),
    };
  }

  private async checkDatabaseHealth(): Promise<{
    status: 'connected' | 'disconnected';
    responseTime?: number;
  }> {
    const startTime = Date.now();

    try {
      // This is a placeholder - you'll need to implement actual database health check
      // For example, if using Prisma:
      // await this.prisma.$queryRaw`SELECT 1`;

      // Simulate a database check
      await new Promise((resolve) => setTimeout(resolve, 10));

      return {
        status: 'connected',
        responseTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        status: 'disconnected',
        responseTime: Date.now() - startTime,
      };
    }
  }

  async checkReadiness(): Promise<{
    ready: boolean;
    checks: { [key: string]: boolean };
  }> {
    const checks = {
      database: false,
      configuration: false,
      memory: false,
    };

    // Check database
    try {
      const dbHealth = await this.checkDatabaseHealth();
      checks.database = dbHealth.status === 'connected';
    } catch {
      checks.database = false;
    }

    // Check configuration
    try {
      checks.configuration = !!(
        this.configService.coreApiPort && this.configService.coreApiHost
      );
    } catch {
      checks.configuration = false;
    }

    // Check memory usage
    const memoryUsage = process.memoryUsage();
    const memoryPercentage =
      (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    checks.memory = memoryPercentage < 90; // Consider ready if memory usage < 90%

    const ready = Object.values(checks).every((check) => check === true);

    return {
      ready,
      checks,
    };
  }

  async checkLiveness(): Promise<{ alive: boolean }> {
    // Basic liveness check - if this service can respond, the app is alive
    return { alive: true };
  }
}

import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsUUID,
  IsBoolean,
  IsOptional,
  IsNotEmpty,
} from 'class-validator';

export class CreateKycDocumentDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Country ID where this KYC document is applicable',
  })
  @IsUUID()
  @IsNotEmpty()
  countryId!: string;

  @ApiProperty({
    example: 'Aadhaar Card',
    description: 'Name of the KYC document',
  })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({
    example: 'aadhaar_card',
    description: 'Unique identifier for the KYC document type',
  })
  @IsString()
  @IsNotEmpty()
  identifier!: string;

  @ApiProperty({
    example: { fields: ['aadhaar_number', 'name', 'address'] },
    description: 'Required fields for this KYC document',
    required: false,
  })
  @IsOptional()
  requiredFields?: any;

  @ApiProperty({
    example: true,
    description: 'Whether this KYC document is mandatory for the country',
  })
  @IsBoolean()
  isMandatory!: boolean;
}

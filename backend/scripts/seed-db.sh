#!/bin/bash

# Tukxi Backend Database Seeder
# This script runs the database seeder to populate initial data

echo "🚀 Starting Tukxi database seeding..."

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "❌ node_modules not found. Please run 'pnpm install' first."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create one with DATABASE_URL."
    exit 1
fi

# Run the seeder
echo "🌱 Running database seeder..."
pnpm exec ts-node prisma/seed.ts

# Check if seeder was successful
if [ $? -eq 0 ]; then
    echo "✅ Database seeding completed successfully!"
else
    echo "❌ Database seeding failed!"
    exit 1
fi

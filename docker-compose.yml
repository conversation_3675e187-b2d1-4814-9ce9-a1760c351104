version: '3.1'
services:
  api:
    build:
      context: ./
      dockerfile: ./docker/api.dev.Dockerfile
    volumes:
      - ./backend:/usr/src/app:delegated
      - /usr/src/app/node_modules  # isolate container node_modules
    ports:
      - '3000:3000'
    links:
      - pgsql
      - redis
  pgsql:
    image: 'postgres:15'
    restart: always
    environment:
      POSTGRES_DB: 'tukxi'
      POSTGRES_USER: 'tukxi_db_user'
      POSTGRES_PASSWORD: 'password'
    volumes:
      - pgsql:/var/lib/postgresql/data
    ports:
      - '5432:5432'
  redis:
    image: 'redis:alpine'
    ports:
      - '6379:6379'
volumes:
  pgsql: ~
